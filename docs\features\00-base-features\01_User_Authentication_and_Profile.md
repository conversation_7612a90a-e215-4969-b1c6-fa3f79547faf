# 01: 用户认证与资料管理

---

## 1. 功能设计 (Functional Design)

### 1.1. 统一登录与自动注册

- **核心目标**: 实现用户通过 **手机号/验证码**、**微信一键登录** 或 **用户名/密码/图形验证码** 三种主流方式登录系统。无论哪种方式，如果用户是首次访问，系统都将为其在 `sys_users` 表中自动创建新账户。
- **业务流程 (手机号)**:
  1. **发送验证码**: 用户输入手机号，后端通过短信服务商发送验证码，并将 `phone:code` 键值对存入 Redis，设置5分钟过期。
  2. **登录/注册**: 用户提交手机号和验证码。后端验证通过后：
     - 查询 `sys_users` 表中 `phone` 字段。
     - **若存在**: 登录成功，生成JWT返回。
     - **若不存在**: 创建新的 `SysUser` 记录，填充 `phone` 字段，分配默认角色，然后生成JWT返回。
- **业务流程 (微信)**:
  1. **微信授权**: 用户在App端点击“微信登录”，调用 `uni.login` 获取 `code`。
  2. **登录/注册**: 前端将 `code` 发送至后端。后端通过 `code` 换取 `OpenID` 和 `UnionID`。
     - 查询 `sys_users` 表中 `wechat_union_id` 字段。
     - **若存在**: 登录成功，生成JWT返回。
     - **若不存在**: 创建新的 `SysUser` 记录，填充 `wechat_openid`, `wechat_union_id`, `nickName`, `headerImg` 等字段，分配默认角色，然后生成JWT返回。
- **业务流程 (用户名密码)**:
  1. **获取图形验证码**: 用户进入登录页时，前端向后端请求图形验证码。
  2. **登录**: 用户输入用户名、密码和图形验证码。后端验证通过后：
     - 验证图形验证码是否正确。
     - 查询 `sys_users` 表中 `username` 字段和对应的密码。
     - **若存在且密码正确**: 登录成功，生成JWT返回。
     - **若不存在或密码错误**: 登录失败，提示错误信息。

### 1.2. 用户及企业资料管理

- **核心目标**: 允许登录后的用户补充和管理其个人及企业相关信息。所有信息都存储在 `sys_users` 表的对应记录中。
- **业务流程**:
  1. **查询资料**: 用户访问“我的资料”页面，后端通过JWT解析出的用户ID，返回 `sys_users` 表中完整的用户信息。
  2. **更新资料**: 用户在页面上修改昵称、头像或补充企业名称、地址等信息后保存。后端接收到数据，验证后更新该用户ID对应的记录。

---

## 2. 接口定义 (API Definition)

| 功能描述 | HTTP 方法 | 路径 (Endpoint) | 请求体说明 | 响应体说明 |
| --- | --- | --- | --- | --- |
| **发送登录验证码** | `POST` | `/user/sendLoginCode` | `{"phone": "138xxxxxxxx"}` | `{"code": 0, "msg": "发送成功"}` |
| **手机号登录/注册** | `POST` | `/user/loginByPhone` | `{"phone": "138xxxxxxxx", "code": "123456"}` | `{"code": 0, "data": {"token": "..."}, "msg": "登录成功"}` |
| **微信登录/注册** | `POST` | `/user/loginByWechat` | `{"code": "..."}` | `{"code": 0, "data": {"token": "..."}, "msg": "登录成功"}` |
| **获取图形验证码** | `POST` | `/base/captcha` | (无) | `{"code": 0, "data": {"captchaId": "...", "picPath": "base64编码图片", "openCaptcha": true, "captchaLength": 6}}` |
| **用户名密码登录** | `POST` | `/user/login` | `{"username": "testuser", "password": "password123", "captcha": "abc", "captchaId": "..."}` | `{"code": 0, "data": {"token": "..."}, "msg": "登录成功"}` |
| **获取当前用户信息** | `GET` | `/user/getProfile` | (无) | `{"code": 0, "data": {<SysUser>}}` |
| **更新当前用户信息** | `PUT` | `/user/updateProfile` | `{"nickName": "...", "headerImg": "...", "companyName": "..."}` | `{"code": 0, "msg": "更新成功"}` |

---

## 3. 相关页面 (Related Pages)

- **统一登录页**: `app/src/pages/login/index.vue` (包含手机号、微信和用户名密码登录方式)
- **用户资料页**: `app/src/pages/profile/index.vue`

---

## 4. 测试用例 (Test Cases)

| 用例ID | 场景描述 | 测试步骤 | 预期结果 |
| --- | --- | --- | --- |
| `TC-AUTH-001` | **已注册用户(手机)成功登录** | 1. 输入已注册的手机号和正确的验证码。 2. 点击“登录”。 | 登录成功，跳转主页。 |
| `TC-AUTH-002` | **新用户(手机)自动注册并登录** | 1. 输入未注册的手机号和正确的验证码。 2. 点击“登录”。 | 自动创建新用户并登录成功。 |
| `TC-AUTH-003` | **已注册用户(微信)成功登录** | 1. 点击“微信登录”并授权。 | 登录成功，跳转主页。 |
| `TC-AUTH-004` | **新用户(微信)自动注册并登录** | 1. 首次点击“微信登录”并授权。 | 自动使用微信信息创建新用户并登录成功。 |
| `TC-AUTH-005` | **成功更新用户资料** | 1. 登录后进入用户资料页。 2. 修改昵称并保存。 | 提示“更新成功”，刷新后显示新昵称。 |
| `TC-AUTH-006` | **已注册用户(用户名密码)成功登录** | 1. 切换到“账号密码登录”。 2. 输入正确的用户名、密码和图形验证码。 3. 点击“登录”。 | 登录成功，跳转主页。 |
| `TC-AUTH-007` | **用户(用户名密码)登录失败 - 密码错误** | 1. 切换到“账号密码登录”。 2. 输入正确的用户名、错误的密码和正确的图形验证码。 3. 点击“登录”。 | 提示“密码错误”，保持在登录页。 |
| `TC-AUTH-008` | **用户(用户名密码)登录失败 - 验证码错误** | 1. 切换到“账号密码登录”。 2. 输入正确的用户名、密码和错误的图形验证码。 3. 点击“登录”。 | 提示“验证码错误”，保持在登录页，图形验证码刷新。 |
| `TC-AUTH-009` | **刷新图形验证码** | 1. 切换到“账号密码登录”。 2. 点击图形验证码图片。 | 图形验证码图片刷新，验证码ID改变。 |

---

## 5. 实现方案 (Based on sys_user)

### 5.1. 后端实现

#### 5.1.1. 目录结构

- **API层**: `admin/server/api/v1/system/sys_user.go` (新增 `/user/login` 逻辑)
- **Service层**: `admin/server/service/system/sys_user.go` (新增 `Login` 方法逻辑)
- **Router层**: `admin/server/router/system/sys_user.go` (新增 `/user/login` 路由)
- **验证码API层**: `admin/server/api/v1/system/sys_base.go` (`/base/captcha` 接口)
- **验证码Router层**: `admin/server/router/system/sys_base.go` (`/base/captcha` 路由)

#### 5.1.2. 登录/注册逻辑

1.  **发送验证码 (`/user/sendLoginCode`)**
    *   在 `sys_user.go` API层新增 `SendLoginCode` 方法。
    *   调用Service层，生成验证码并存入Redis (`"code_" + phone`)，有效期5分钟。
    *   集成短信服务发送验证码。

2.  **手机号登录 (`/user/loginByPhone`)**
    *   在 `sys_user.go` API层新增 `LoginByPhone` 方法。
    *   Service层验证Redis中的验证码。
    *   验证通过后，Service层查询 `sys_users` 表。
        *   若用户存在，调用 `TokenNext` 生成Token。
        *   若不存在，创建 `system.SysUser` 实例，设置 `Phone`, `Username` (可设为手机号), `AuthorityId`，调用 `userService.Register` 创建用户，再生成Token。

3.  **微信登录 (`/user/loginByWechat`)**
    *   在 `sys_user.go` API层新增 `LoginByWechat` 方法。
    *   Service层通过 `code` 从微信服务器换取 `OpenID` 和 `UnionID`。
    *   Service层使用 `UnionID` 查询 `sys_users` 表。
        *   若用户存在，调用 `TokenNext` 生成Token。
        *   若不存在，创建 `system.SysUser` 实例，填充微信信息，调用 `userService.Register` 创建用户，再生成Token。

4.  **获取图形验证码 (`/base/captcha`)**
    *   在 `sys_base.go` API层 (`BaseApi` 结构体) 实现 `Captcha` 方法。
    *   调用 `utils/captcha` 包生成图形验证码，返回 `captchaId` 和 base64 编码的图片。
    *   在 `sys_base.go` router 中配置 `POST /base/captcha` 路由。

5.  **用户名密码登录 (`/user/login`)**
    *   在 `sys_user.go` API层新增 `Login` 方法。
    *   Service层接收 `username`, `password`, `captcha`, `captchaId`。
    *   Service层首先验证 `captchaId` 和 `captcha` 是否匹配。
    *   验证通过后，查询 `sys_users` 表，根据 `username` 查找用户。
    *   验证用户密码。
    *   登录成功后，调用 `TokenNext` 生成Token。

#### 5.1.3. 用户资料管理

1.  **模型修改 (增加企业字段)**
    *   修改 `admin/server/model/system/sys_user.go` 中的 `SysUser` 结构体，增加企业相关字段 (如果尚未添加):
        ```go
        type SysUser struct {
            // ... 原有及微信字段
            CompanyName     string `json:"companyName" gorm:"comment:企业名称"`
            CompanyOrgId    string `json:"companyOrgId" gorm:"comment:企业组织编码ID"`
            CompanyAddress  string `json:"companyAddress" gorm:"comment:企业地址"`
        }
        ```

2.  **获取用户信息 (`/user/getProfile`)**
    *   复用 `GetUserInfo` 方法，它能通过JWT自动获取当前用户并返回完整的 `SysUser` 信息。

3.  **更新用户信息 (`/user/updateProfile`)**
    *   复用 `SetUserInfo` 方法。前端传来需要更新的字段（如 `nickName`, `companyName` 等），后端动态更新这些字段。

### 5.2. 前端实现

#### 5.2.1. 登录页面 (`app/src/pages/login/index.vue`)
*   提供“手机号登录”、“微信登录”和“账号密码登录”三个入口，通过Tab切换实现。
*   “手机号登录”部分包含手机号输入框和倒计时按钮。
*   “微信登录”按钮直接调用 `uni.login` 并将 `code` 发送至后端。
*   “账号密码登录”部分包含用户名、密码输入框和图形验证码输入框。
    *   页面加载时自动请求图形验证码并显示图片。
    *   点击图形验证码图片可刷新验证码。
*   登录成功后，将Token存入 `pinia` 并导航至主页。

#### 5.2.2. 用户资料页 (`app/src/pages/profile/index.vue`)
*   加载时调用 `/user/getProfile` 接口填充表单。
*   提供所有可修改字段的输入框。
*   保存时调用 `/user/updateProfile` 接口。
