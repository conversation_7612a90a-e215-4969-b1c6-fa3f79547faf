/**
 * 期货合约相关类型定义
 */

// 删除对 commodity 的依赖

// 期货合约基础信息
export interface IInstrument {
  id: number
  exchange_id: string
  product_id: string
  product_name: string
  instrument_id: string
  instrument_name: string
  product_class: string
  product_class_name?: string
  volume_multiple: number
  price_tick: number
  long_margin_ratio?: number
  short_margin_ratio?: number
  open_ratio_by_money?: number
  close_ratio_by_money?: number
  close_today_ratio?: number
  delivery_year?: number
  delivery_month?: number
  open_date?: string
  expire_date?: string
  inst_life_phase: string
  inst_life_phase_name?: string
  created_at: string
  updated_at: string
}

// 期货合约请求参数
export interface IInstrumentRequest {
  id?: number
  exchange_id: string
  product_id: string
  product_name: string
  instrument_id: string
  instrument_name: string
  product_class: string
  volume_multiple: number
  price_tick: number
  long_margin_ratio?: number
  short_margin_ratio?: number
  open_ratio_by_money?: number
  close_ratio_by_money?: number
  close_today_ratio?: number
  delivery_year?: number
  delivery_month?: number
  open_date?: string
  expire_date?: string
  inst_life_phase: string
}

// 期货合约列表查询参数
export interface IInstrumentListRequest {
  page?: number
  pageSize?: number
  instrument_id?: string
  instrument_name?: string
  exchange_id?: string
  product_id?: string
  product_class?: string
  inst_life_phase?: string
}

// 期货合约列表响应
export interface IInstrumentListResponse {
  list: IInstrument[]
  total: number
  page: number
  pageSize: number
}

// 期货合约选择器查询参数
export interface IInstrumentSelectRequest {
  exchange_id?: string
  product_id?: string
  keyword?: string
  life_phase?: string
}

// 期货合约选择器响应项
export interface IInstrumentSelectItem {
  id: number
  instrument_id: string
  instrument_name: string
  product_name: string
  exchange_id: string
}

// 期货合约选择器组件props
export interface IInstrumentSelectorProps {
  modelValue?: IInstrumentSelectItem | null
  label?: string
  placeholder?: string
  disabled?: boolean
  clearable?: boolean
  exchangeId?: string
  productId?: string
  lifePhase?: string
}

// 期货合约选择器组件emits
export interface IInstrumentSelectorEmits {
  (e: 'update:modelValue', value: IInstrumentSelectItem | null): void
  (e: 'change', value: IInstrumentSelectItem | null): void
}

// 按交易所分组的期货合约数据
export interface IInstrumentsByExchange {
  [exchangeId: string]: {
    [productKey: string]: IInstrumentSelectItem[]
  }
}

// 商品类别映射
export const ProductClassMap: Record<string, string> = {
  '1': '期货',
  '2': '期权',
  '3': '组合',
  '4': '即期',
  '5': '期转现',
  '6': '现货期权',
  '7': 'TAS合约',
  '8': '金属指数',
  '9': '期货期权'
}

// 合约状态映射
export const InstLifePhaseMap: Record<string, string> = {
  '0': '未上市',
  '1': '上市',
  '2': '停牌',
  '3': '到期'
}

// 交易所映射
export const ExchangeMap: Record<string, string> = {
  'CFFEX': '中国金融期货交易所',
  'SHFE': '上海期货交易所',
  'DCE': '大连商品交易所',
  'CZCE': '郑州商品交易所',
  'INE': '上海国际能源交易中心',
  'GFEX': '广州期货交易所'
}