# 功能模块：下单端与服务端WebSocket通信

## 1. 功能模块简介 (Summary)

为解决多个下单终端使用同一期货账户导致交易冲突的问题，本功能模块定义了下单端（Client）与业务服务器（Server）之间基于 WebSocket 的实时通信协议。

该协议的核心目标是建立一个稳定、可靠、一对一的通信链路，确保交易指令的有序下发和状态的精确反馈。

- **核心原则**:
  - **连接唯一性**: 同一期货账户在任意时刻只允许一个活跃的下单端连接。新的连接请求会触发强制下线确认机制。
  - **状态持久化**: 所有连接的关键状态（如连接ID、IP、活跃时间）将通过 Redis 进行集中管理和持久化。
  - **时间同步**: 通过心跳机制校准下单端与服务器的时间，保证所有时间戳的准确性。
  - **闭环通信**: 所有关键操作（如下单）都具备请求-响应-确认的闭环流程，确保消息的可靠传递和操作的可追溯性。

## 2. 数据定义 (Data Definition)

### 2.1. Redis 存储模型

用于管理和持久化连接状态。

- **键名/表名**: `websocket_connections` (建议使用 Hash 结构，以 `account_id` 为 key)
- **字段定义**:

| 字段名 | 类型 | 描述 |
| :--- | :--- | :--- |
| `account_id` | `VARCHAR(64)` | **主键**, 关联的期货账户ID |
| `conn_id` | `VARCHAR(64)` | WebSocket 连接的唯一标识 (如 UUID) |
| `client_ip` | `VARCHAR(15)` | 下单端的 IP 地址 |
| `last_active_time` | `INTEGER` | 最后一次心跳或消息交互的Unix毫秒时间戳 |
| `status` | `VARCHAR(20)` | 连接状态 (`ACTIVE`, `PENDING_DISCONNECT`, `DISCONNECTED`, `FORCE_CLOSED`) |
| `create_time` | `INTEGER` | 连接创建时的Unix毫秒时间戳 |
| `token_expire_time` | `INTEGER` | 认证Token的过期时间戳 |

### 2.2. WebSocket 消息协议

所有消息均为 JSON 格式，包含 `msg_type` 字段用于路由。

#### 2.2.1. 连接建立 (客户端 -> 服务器)

- **`msg_type`: `CONNECT_REQ`**

```json
{
  "msg_type": "CONNECT_REQ",
  "account_id": "ACC123456",
  "token": "eyJhbGciOiJIUzI1NiJ9...",
  "client_time": *************
}
```

#### 2.2.2. 连接响应 (服务器 -> 客户端)

- **`msg_type`: `CONNECT_RES`** (成功)
- **`msg_type`: `CONFLICT_NOTIFY`** (冲突)
- **`msg_type`: `ERROR_RES`** (错误)

#### 2.2.3. 心跳维持 (双向)

- **`msg_type`: `HEARTBEAT_REQ`** (客户端 -> 服务器)
```json
{
  "msg_type": "HEARTBEAT_REQ",
  "client_time": *************
}
```
- **`msg_type`: `HEARTBEAT_RES`** (服务器 -> 客户端)
```json
{
  "msg_type": "HEARTBEAT_RES",
  "server_time": *************
}
```

#### 2.2.4. 订单请求 (服务器 -> 客户端)

- **`msg_type`: `ORDER_REQ`**

```json
{
  "msg_type": "ORDER_REQ",
  "order_id": "ORD20250722172143",
  "symbol": "RB2410",
  "direction": "BUY",
  "quantity": 10,
  "price_type": "LIMIT",
  "price": 3800.0,
  "server_time": *************
}
```

#### 2.2.5. 订单确认与反馈 (客户端 -> 服务器)

- **`msg_type`: `ACK_REQ`** (确认收到订单)
```json
{
  "msg_type": "ACK_REQ",
  "order_id": "ORD20250722172143",
  "client_time": 1721639306000
}
```
- **`msg_type`: `ORDER_RSP`** (反馈执行结果)
```json
{
  "msg_type": "ORDER_RSP",
  "order_id": "ORD20250722172143",
  "status": "SUCCESS",
  "execute_price": 3802.5,
  "error_msg": "",
  "client_feedback_time": 1721639308000
}
```

#### 2.2.6. 强制断开 (客户端 -> 服务器)

- **`msg_type`: `FORCE_DISCONNECT_REQ`**
```json
{
  "msg_type": "FORCE_DISCONNECT_REQ",
  "conn_id": "OLD_CONN_123"
}
```
- **`msg_type`: `FORCE_DISCONNECT_RES`** (服务器响应)
```json
{
    "msg_type": "FORCE_DISCONNECT_RES",
    "success": true
}
```
