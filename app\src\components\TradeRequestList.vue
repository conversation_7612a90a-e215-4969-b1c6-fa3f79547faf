<script lang="ts" setup>
import { computed } from 'vue'
import type { ITradeRequest, TradeRequestType } from '@/types/trade-request'
import TradeRequestItem from './TradeRequestItem.vue'

// Props 定义
interface TradeRequestListProps {
  requests: ITradeRequest[]
  requestType?: TradeRequestType
  loading?: boolean
  refreshing?: boolean
  /** 显示模式：setter-显示setter操作按钮，pricer-显示取消按钮，viewer-仅查看无按钮 */
  mode?: 'setter' | 'pricer' | 'viewer'
  /** 是否为 setter 管理模式（向后兼容） */
  isSetterMode?: boolean
}

// Emits 定义
interface TradeRequestListEmits {
  (e: 'fill', request: ITradeRequest): void
  (e: 'reject', request: ITradeRequest): void
  (e: 'convertToSimulation', request: ITradeRequest): void
  (e: 'convertToTrade', request: ITradeRequest): void
  (e: 'cancel', request: ITradeRequest): void
  (e: 'refresh'): void
}

const props = withDefaults(defineProps<TradeRequestListProps>(), {
  loading: false,
  refreshing: false,
  mode: undefined,
  isSetterMode: false
})

const emit = defineEmits<TradeRequestListEmits>()

// 计算当前实际模式（兼容旧的 isSetterMode）
const actualMode = computed(() => {
  if (props.mode) {
    return props.mode
  }
  return props.isSetterMode ? 'setter' : 'viewer'
})

// 计算属性
const isPointPrice = computed(() => {
  return props.requestType === 'PointPrice'
})

const listTitle = computed(() => {
  if (actualMode.value === 'setter') {
    return '交易请求管理'
  } else if (actualMode.value === 'pricer') {
    return '我的交易请求'
  } else if (actualMode.value === 'viewer') {
    return '交易请求查看'
  }
  return `当日${isPointPrice.value ? '点价' : '洗基差'}请求`
})

const isEmpty = computed(() => {
  return !props.loading && props.requests.length === 0
})

const emptyMessage = computed(() => {
  if (actualMode.value === 'setter') {
    return '暂无交易请求'
  } else if (actualMode.value === 'pricer') {
    return '暂无发起的交易请求'
  } else if (actualMode.value === 'viewer') {
    return '暂无交易记录'
  }
  return `暂无${isPointPrice.value ? '点价' : '洗基差'}记录`
})

const pendingCount = computed(() => {
  if (actualMode.value !== 'setter' && actualMode.value !== 'pricer') return 0
  return props.requests.filter(req => req.status === 'Executing').length
})

// 事件处理
const handleFill = (request: ITradeRequest) => {
  if (actualMode.value === 'setter') {
    emit('fill', request)
  }
}

const handleReject = (request: ITradeRequest) => {
  if (actualMode.value === 'setter') {
    emit('reject', request)
  }
}

const handleConvertToSimulation = (request: ITradeRequest) => {
  if (actualMode.value === 'setter') {
    emit('convertToSimulation', request)
  }
}

const handleConvertToTrade = (request: ITradeRequest) => {
  if (actualMode.value === 'setter') {
    emit('convertToTrade', request)
  }
}

const handleCancel = (request: ITradeRequest) => {
  if (actualMode.value === 'pricer') {
    emit('cancel', request)
  }
}

const handleRefresh = () => {
  emit('refresh')
}
</script>

<template>
  <view class="trade-request-list">

    <!-- 统一的列表显示 -->
    <view class="list-content">
      <!-- 加载状态 -->
      <view v-if="loading" class="loading-state">
        <view class="flex justify-center items-center py-8">
          <view class="loading-spinner w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-3"></view>
          <text class="text-sm text-gray-500">加载中...</text>
        </view>
      </view>

      <!-- 空状态 -->
      <view v-else-if="isEmpty" class="empty-state">
        <view class="text-center py-12 bg-white rounded-lg">
          <view class="empty-icon w-16 h-16 mx-auto mb-4 opacity-20">
            <svg viewBox="0 0 24 24" fill="currentColor" class="w-full h-full text-gray-400">
              <path d="M20 6h-2.18c.11-.31.18-.65.18-1a2.996 2.996 0 0 0-5.5-1.65l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .*********** 1H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"/>
            </svg>
          </view>
          <text class="text-sm text-gray-400">{{ emptyMessage }}</text>
        </view>
      </view>

      <!-- 请求列表 -->
      <view v-else class="request-list">
        <TradeRequestItem
          v-for="request in requests"
          :key="request.ID"
          :request="request"
          :mode="actualMode"
          @fill="handleFill"
          @reject="handleReject"
          @convert-to-simulation="handleConvertToSimulation"
          @convert-to-trade="handleConvertToTrade"
          @cancel="handleCancel"
        />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.trade-request-list {
  .list-header {
    background-color: #fafbfc;
    border-bottom: 1rpx solid #e4e7ed;
    
    h2 {
      font-size: 32rpx;
      color: #303133;
      font-weight: 600;
    }
    
    // Setter 模式头部样式
    &.flex {
      .header-info {
        h2 {
          font-size: 36rpx;
          color: #303133;
          font-weight: 600;
          margin-bottom: 4rpx;
        }
      }
    }
  }
  
  .loading-state {
    .loading-spinner {
      width: 32rpx;
      height: 32rpx;
      border: 2rpx solid #409eff;
      border-top-color: transparent;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  }
  
  .empty-state {
    .empty-icon {
      width: 128rpx;
      height: 128rpx;
      opacity: 0.3;
    }
  }
  
  .request-list {
    // TradeRequestItem 组件已经包含了 mb-3，这里不需要额外的间距
    :deep(.trade-request-item:last-child) {
      margin-bottom: 0;
    }
  }
}

// 添加一些响应式设计
@media (max-width: 750rpx) {
  .trade-request-list {
    .list-header {
      padding: 12rpx 16rpx;
      
      h2 {
        font-size: 28rpx;
      }
    }
    
    .request-list {
      padding: 12rpx 16rpx;
    }
  }
}
</style>