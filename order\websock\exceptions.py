"""
WebSocket异常定义

定义WebSocket通信中使用的异常类
"""


class WebSocketException(Exception):
    """WebSocket基础异常"""
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.error_code = error_code or "WEBSOCKET_ERROR"


class ConnectionException(WebSocketException):
    """连接异常"""
    def __init__(self, message: str):
        super().__init__(message, "CONNECTION_ERROR")


class AuthenticationException(WebSocketException):
    """认证异常"""
    def __init__(self, message: str):
        super().__init__(message, "AUTH_ERROR")


class MessageException(WebSocketException):
    """消息异常"""
    def __init__(self, message: str):
        super().__init__(message, "MESSAGE_ERROR")


class TimeoutException(WebSocketException):
    """超时异常"""
    def __init__(self, message: str):
        super().__init__(message, "TIMEOUT_ERROR")


class ReconnectException(WebSocketException):
    """重连异常"""
    def __init__(self, message: str):
        super().__init__(message, "RECONNECT_ERROR")


def handle_websocket_exception(func):
    """WebSocket异常处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except WebSocketException:
            raise
        except Exception as e:
            raise WebSocketException(f"Unexpected error in {func.__name__}: {str(e)}")
    return wrapper


# 移除异步异常处理装饰器，重构后不再需要异步支持