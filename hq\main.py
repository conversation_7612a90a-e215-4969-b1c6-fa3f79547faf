import traceback
from typing import Optional
import time
from datetime import datetime
from apscheduler.schedulers.blocking import BlockingScheduler

# 导入所需模块
from core.engine import create_engine
from infrastructure.logging import init_logging, get_logger
from infrastructure.config import get_config, init_config, get_section  # 更新导入


class TradingTimeService:
    """交易时间管理服务"""
    
    def __init__(self, config):
        # 从配置获取交易时间设置，或使用默认值
        time_config = config.get('trading_time', {})
        
        # 日间交易时间
        self.day_start = self._parse_time(time_config.get('day_start', '08:30:00'))
        self.day_end = self._parse_time(time_config.get('day_end', '15:40:00'))
        
        # 夜盘交易时间
        self.night_start = self._parse_time(time_config.get('night_start', '20:30:00'))
        self.night_end = self._parse_time(time_config.get('night_end', '03:00:00'))
        
        # 交易日配置（默认周一至周五）
        self.trading_days = time_config.get('trading_days', [0, 1, 2, 3, 4])
    
    def is_trading_time(self) -> bool:
        """判断当前是否在交易时间内"""
        now = datetime.now()
        current_time = now.time()
        weekday = now.weekday()
        
        # 日间交易时段判断
        if weekday in self.trading_days:
            if self.day_start <= current_time <= self.day_end:
                return True
        
        # 夜盘交易时段判断
        if current_time >= self.night_start:
            # 当天晚上的夜盘
            return weekday in self.trading_days
        elif current_time <= self.night_end:
            # 次日凌晨的夜盘（实际是前一天的交易延续）
            return (weekday - 1) % 7 in self.trading_days
            
        return False
        
    def _parse_time(self, time_str):
        """将字符串解析为time对象"""
        return datetime.strptime(time_str, '%H:%M:%S').time()


class EngineManager:
    """引擎生命周期管理类"""
    
    def __init__(self, logger):
        self.engine = None
        self.logger = logger if logger else get_logger("engine")
        self.status = "stopped"
        
    def start_engine(self):
        """同步启动引擎"""
        try:
            self.status = "initializing"
            self.logger.info(f"启动引擎 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.engine = create_engine()
            
            if not self.engine.initialize():
                self.logger.error("引擎初始化失败")
                self.status = "error"
                return False
            
            if not self.engine.start():
                self.logger.error("引擎启动失败")
                self.status = "error"
                return False
            
            self.status = "running"
            self.logger.info("引擎启动成功")
            return True
        except Exception as e:
            self.status = "error"
            self.logger.error(f"启动引擎失败: {e}", exc_info=True)
            return False
        
    def stop_engine(self):
        """停止引擎"""
        if not self.engine:
            return True
            
        try:
            self.status = "stopping"
            self.logger.info(f"关闭引擎 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self.engine.stop()
            self.status = "stopped"
            self.engine = None
            self.logger.info("引擎已停止")
            return True
        except Exception as e:
            self.status = "error"
            self.logger.error(f"停止引擎失败: {e}", exc_info=True)
            return False
            
    def get_status(self):
        """获取引擎状态"""
        if not self.engine:
            return {"is_running": False, "is_connected": False, "subscribed_count": 0}
        return self.engine.get_status()


class HQSystem:
    """行情系统主类"""
    
    def __init__(self, config_path=None):
        self.logger = None
        self.config = None
        self.engine_manager = None
        self.trading_time_service = None
        self.scheduler = None
        self.initialized = False  # 标记初始化状态
        
        # 先尝试初始化基本的日志记录器，以便记录初始化失败信息
        try:
            self.logger = get_logger("main")
        except:
            self.logger = None
            print("警告: 无法创建基本日志记录器")
            
        # 完整初始化系统
        self.initialized = self._initialize_system(config_path)
    
    def _initialize_system(self, config_path):
        """初始化系统"""
        try:
            # 初始化配置
            print(f"正在初始化配置系统, 配置路径: {config_path or '默认'}")
            # 使用新的初始化方式
            init_config(config_path)
            self.config = get_config()  # 获取解析后的配置
            print(f"配置系统初始化完成")
            
            # 初始化日志
            print("正在初始化日志系统...")
            log_config = get_section("log")
            from infrastructure.logging import LogConfig
            
            log_cfg = LogConfig()
            log_cfg.console_level = log_config.get('level', 'INFO')
            log_cfg.log_dir = log_cfg.log_dir  # 使用默认日志目录
            
            init_logging(log_cfg)
            self.logger = get_logger("main")
            
            if not self.logger:
                print("错误: 日志初始化失败，无法获取日志记录器")
                return False
            
            self.logger.info("=== HQ行情系统启动 ===")
            self.logger.info("重构版 - 面向对象设计")
            self.logger.info(f"配置文件: {config_path or '默认配置'}")
            
            # 创建组件
            self.engine_manager = EngineManager(get_logger("engine"))
            self.trading_time_service = TradingTimeService(self.config)
            
            self.logger.info("系统初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"系统初始化失败: {e}", exc_info=True)
            print(f"系统初始化失败: {e}")
            return False
    
    def run(self, schedule_mode=False):
        """系统运行入口"""
        if not self.initialized:
            print("错误: 系统未成功初始化，无法运行")
            return False
            
        try:
            if schedule_mode:
                self._run_schedule_mode()
            else:
                self._run_direct_mode()
            return True
        except Exception as e:
            if self.logger:
                self.logger.error(f"系统运行失败: {e}", exc_info=True)
            else:
                print(f"系统运行失败: {e}")
            return False
    
    def _run_direct_mode(self):
        """直接模式：立即启动并持续运行"""
        self.logger.info("启动直接模式")
        
        # 启动引擎
        if not self.engine_manager.start_engine():
            self.logger.error("引擎启动失败，退出")
            return
        
        # 持续运行
        try:
            self.logger.info("引擎运行中，按Ctrl+C停止...")
            while True:
                # 定期检查引擎状态
                status = self.engine_manager.get_status()
                self.logger.debug(f"引擎状态: 运行={status['is_running']}, 连接={status['is_connected']}, 订阅={status['subscribed_count']}")
                
                time.sleep(10)  # 每10秒检查一次
                
        except KeyboardInterrupt:
            self.logger.info("收到停止信号")
        except Exception as e:
            self.logger.error(f"运行时错误: {e}", exc_info=True)
        finally:
            self.logger.info("正在停止引擎...")
            self.engine_manager.stop_engine()
            self.logger.info("程序已退出")
    
    def _run_schedule_mode(self):
        """调度模式：根据交易时间自动启停"""
        self.logger.info("启动调度模式")
        self.scheduler = BlockingScheduler()
        
        def check_engine_status():
            """检查并调整引擎状态"""
            should_run = self.trading_time_service.is_trading_time()
            
            try:
                is_running = self.engine_manager.engine is not None
                
                if should_run and not is_running:
                    self.logger.info("进入交易时间，启动引擎")
                    self.engine_manager.start_engine()
                elif not should_run and is_running:
                    self.logger.info("离开交易时间，停止引擎")
                    self.engine_manager.stop_engine()
            except Exception as e:
                self.logger.error(f"调度引擎状态检查失败: {e}", exc_info=True)
        
        # 每分钟检查一次引擎状态
        self.scheduler.add_job(check_engine_status, 'interval', minutes=1, id='engine_check')
        self.logger.info("引擎调度器已启动，每分钟检查一次交易时间")
        
        try:
            # 立即运行一次，确保正确的初始状态
            check_engine_status()
            # 启动调度器
            self.scheduler.start()
        except KeyboardInterrupt:
            self.logger.info("收到停止信号，关闭调度器")
            self.engine_manager.stop_engine()
            self.scheduler.shutdown()


def main():
    """主函数"""
    import sys
    
    # 解析命令行参数
    config_path = None
    if len(sys.argv) > 1:
        config_path = sys.argv[1]
        print(f"使用配置文件：{config_path}")
    else:
        print("使用默认配置文件")
    
    # 创建并运行系统
    system = HQSystem(config_path)
    if not system.initialized:
        print("系统初始化失败，退出")
        return
        
    # 根据启动模式选择运行方式
    schedule_mode = len(sys.argv) > 2 and sys.argv[2] == "schedule"
    result = system.run(schedule_mode)
    
    if not result:
        print("系统运行失败，已退出")
        return


if __name__ == "__main__":
    main()
