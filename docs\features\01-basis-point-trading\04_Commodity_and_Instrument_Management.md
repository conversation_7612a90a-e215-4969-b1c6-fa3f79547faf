# 04 - 商品与期货合约管理

## 1. 功能简介

本功能模块旨在建立和维护商品（Commodity）和期货合约（Instrument）的基础数据。这些数据是系统中进行交易、分析和风险管理等核心功能的基础。

- **商品（Commodity）**: 通常指期货合约的品种，例如“沪深300股指”、“螺纹钢”等。
- **期货合约（Instrument）**: 是标准化的可交易合约，例如“IF2505”（沪深300股指2505合约）。

由于这些数据直接影响系统的正常运行，因此需要在后台管理系统中提供稳定、可靠的管理界面，并为前端业务功能提供高效、便捷的数据选择方式。

## 2. 数据定义

### 2.1. Commodity (商品)

商品表用于存储期货品种的基础信息。

- **表名**: `commodities`

| 字段名 | 数据类型 | 长度/格式 | 主键 | 非空 | 注释 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `id` | `int` | | ✓ | ✓ | 自增ID |
| `name` | `varchar` | 50 | | ✓ | 商品名称 (例如: 沪深300股指) |
| `product_id` | `varchar` | 20 | | ✓ | 品种ID (例如: IF) |
| `exchange_id` | `varchar` | 20 | | ✓ | 所属交易所ID (例如: CFFEX) |
| `created_at` | `datetime` | | | ✓ | 创建时间 |
| `updated_at` | `datetime` | | | ✓ | 更新时间 |

### 2.2. Instrument (期货合约)

期货合约表用于存储具体的合约信息，并与商品表关联。

- **表名**: `instruments`

| 字段名 | 数据类型 | 长度/格式 | 主键 | 非空 | 注释 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| `id` | `int` | | ✓ | ✓ | 自增ID |
| `instrument_id` | `varchar` | 30 | | ✓ | 合约ID (例如: IF2505) |
| `instrument_name` | `varchar` | 50 | | ✓ | 合约名称 (例如: 股指2505) |
| `commodity_id` | `int` | | | ✓ | 关联的商品ID (外键) |
| `product_class` | `char` | 1 | | ✓ | 商品类别: '1'-期货, '2'-期权, ... |
| `volume_multiple` | `int` | | | ✓ | 合约乘数 |
| `price_tick` | `decimal` | 10, 4 | | ✓ | 最小变动价位 |
| `long_margin_ratio` | `decimal` | 10, 6 | | | 做多保证金率 |
| `short_margin_ratio` | `decimal` | 10, 6 | | | 做空保证金率 |
| `open_ratio_by_money` | `decimal` | 10, 8 | | | 开仓手续费率 |
| `close_ratio_by_money`| `decimal` | 10, 8 | | | 平仓手续费率 |
| `close_today_ratio` | `decimal` | 10, 8 | | | 平今手续费率 |
| `delivery_year` | `int` | | | | 交割年份 |
| `delivery_month` | `int` | | | | 交割月份 |
| `open_date` | `date` | | | | 上市日期 |
| `expire_date` | `date` | | | | 最后交易日 |
| `inst_life_phase` | `char` | 1 | | ✓ | 合约状态: '0'-未上市, '1'-上市, '2'-停牌, '3'-到期 |
| `created_at` | `datetime` | | | ✓ | 创建时间 |
| `updated_at` | `datetime` | | | ✓ | 更新时间 |

## 3. 后台管理功能 (Admin)

为了方便管理员维护数据，需要在 `admin` 前端项目中增加相应的管理页面。

### 3.1. 商品管理页面

- **路径**: `/commodity/index`
- **功能**:
    - **列表展示**: 以表格形式展示所有商品信息，包含 `id`, `name`, `product_id`, `exchange_id` 等字段。支持分页和按 `name` 或 `product_id` 搜索。
    - **新增商品**: 弹窗表单，用于添加新的商品。
    - **编辑商品**: 弹窗表单，用于修改已有的商品信息。
    - **删除商品**: 提供删除按钮，并有二次确认，防止误操作。

### 3.2. 期货合约管理页面

- **路径**: `/instrument/index`
- **功能**:
    - **列表展示**: 以表格形式展示所有期货合约信息，并显示其关联的商品名称。支持分页和按 `instrument_id` 或 `instrument_name` 搜索。
    - **新增合约**: 弹窗表单，其中“所属商品”通过下拉选择器从商品列表中选取。
    - **编辑合约**: 弹窗表单，用于修改合约信息。
    - **删除合约**: 提供删除按钮，并有二次确认。

## 4. 前端应用设计 (App)

在业务功能中，用户需要频繁选择期货合约。为了提升用户体验，需要设计一个功能强大且易于使用的选择器。

### 4.1. 期货合约选择器 (`@/components/InstrumentSelector.vue`)

这是一个可复用的组件，可以被其他页面或组件调用。

- **UI 设计**:
    - **触发方式**: 通常是一个输入框或一个按钮，点击后弹出选择器。
    - **弹出层**: 选择器以弹窗（Modal/Dialog）的形式展示。
    - **分级展示**:
        - 采用**三级级联**方式组织数据，提升交互体验。
        - **第一级**: 交易所 (Exchange)。例如：`CFFEX` (中金所), `SHFE` (上期所)。
        - **第二级**: 商品/品种 (Product Name)。例如：`沪深300股指` (IF), `甲醇` (MA)。
        - **第三级**: 具体合约 (Instrument Name)。例如：`2505`, `2506`。
    - **常用列表**: 选择器可以增加一个"常用"或"最近使用"的标签页，方便用户快速选择之前用过的合约。

- **技术实现与交互细节**:
    - 可采用 `ColPicker` 等类似组件实现多级联动。
    - 数据通过API异步懒加载，避免一次性加载过多数据。
    - 用户选择第一级（交易所）后，加载该交易所下的商品列表。
    - 用户选择第二级（商品名称）后，加载该商品下的合约列表。
    - 选择结果返回包含完整信息的对象，便于调用方使用。
