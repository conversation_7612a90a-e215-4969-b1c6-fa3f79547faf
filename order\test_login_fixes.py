#!/usr/bin/env python3
"""
登录窗口样式修复验证脚本

验证以下修复：
1. 登录取消按钮大小一致
2. 记住密码、自动登录复选框样式
3. Tab高度统一
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_login_fixes():
    """测试登录窗口样式修复"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 导入并应用全局样式
        from ui.styles.global_styles import GlobalStyleManager
        
        global_style_manager = GlobalStyleManager()
        global_styles = global_style_manager.get_complete_global_style()
        app.setStyleSheet(global_styles)
        
        print("✅ 全局样式已应用")
        
        # 创建登录窗口
        from vnpy.event import EventEngine
        from ui.login.login_window import LoginWindow
        
        event_engine = EventEngine()
        login_window = LoginWindow(event_engine, None, None)
        
        # 显示登录窗口
        login_window.show_centered()
        
        print("=" * 60)
        print("登录窗口样式修复验证")
        print("=" * 60)
        print("修复验证要点:")
        print()
        print("1. 按钮大小一致性:")
        print("   - 登录按钮和取消按钮应该有相同的高度(40px)")
        print("   - 两个按钮的宽度应该合理且一致")
        print("   - 按钮的圆角和字体应该统一")
        print()
        print("2. 复选框样式:")
        print("   - '记住密码'和'自动登录'复选框应该有现代化样式")
        print("   - 复选框应该有18x18px的大小")
        print("   - 选中状态应该显示蓝色背景和白色勾选")
        print("   - 悬停时应该有边框颜色变化")
        print()
        print("3. Tab高度统一:")
        print("   - '用户名密码'和'手机验证码'Tab应该有相同高度")
        print("   - Tab高度应该是32px")
        print("   - Tab的圆角和间距应该一致")
        print()
        print("4. 整体协调性:")
        print("   - 所有组件使用统一的颜色方案")
        print("   - 背景应该是渐变色")
        print("   - 字体和间距应该协调")
        print("=" * 60)
        
        # 延迟检查具体样式
        def detailed_check():
            try:
                # 检查按钮
                login_btn = login_window.login_button
                cancel_btn = login_window.cancel_button
                
                print("\n🔍 详细检查结果:")
                print(f"登录按钮高度: {login_btn.height()}px")
                print(f"取消按钮高度: {cancel_btn.height()}px")
                
                if login_btn.height() == cancel_btn.height():
                    print("✅ 按钮高度一致")
                else:
                    print("❌ 按钮高度不一致")
                
                # 检查复选框
                remember_cb = login_window.login_widget.remember_checkbox
                auto_login_cb = login_window.login_widget.auto_login_checkbox
                
                print(f"记住密码复选框: {remember_cb.text()}")
                print(f"自动登录复选框: {auto_login_cb.text()}")
                
                # 检查Tab
                tab_widget = login_window.login_widget.tab_widget
                tab_bar = tab_widget.tabBar()
                
                print(f"Tab数量: {tab_widget.count()}")
                print(f"Tab1: {tab_widget.tabText(0)}")
                print(f"Tab2: {tab_widget.tabText(1)}")
                
                if tab_bar:
                    print(f"TabBar高度: {tab_bar.height()}px")
                
                print("\n✅ 所有组件检查完成")
                
            except Exception as e:
                print(f"❌ 详细检查失败：{str(e)}")
        
        # 延迟执行详细检查
        QTimer.singleShot(2000, detailed_check)
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_fixes()
