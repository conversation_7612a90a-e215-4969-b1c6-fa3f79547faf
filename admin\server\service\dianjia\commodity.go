package dianjia

import (
	"errors"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
	"gorm.io/gorm"
)

type CommodityService struct{}

// CreateCommodity 创建商品
func (commodityService *CommodityService) CreateCommodity(req dianjia.CommodityRequest) (commodity dianjia.Commodity, err error) {
	// 检查ProductID是否已存在
	var existCommodity dianjia.Commodity
	if err = global.GVA_DB.Where("product_id = ?", req.ProductID).First(&existCommodity).Error; err == nil {
		return commodity, errors.New("品种ID已存在")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return commodity, err
	}

	commodity = dianjia.Commodity{
		Name:       req.Name,
		ProductID:  req.ProductID,
		ExchangeID: req.ExchangeID,
	}

	err = global.GVA_DB.Create(&commodity).Error
	return commodity, err
}

// DeleteCommodity 删除商品
func (commodityService *CommodityService) DeleteCommodity(id uint) (err error) {
	// 检查是否有关联的期货合约
	var count int64
	err = global.GVA_DB.Model(&dianjia.Instrument{}).Where("commodity_id = ?", id).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("该商品下存在期货合约，无法删除")
	}

	err = global.GVA_DB.Delete(&dianjia.Commodity{}, id).Error
	return err
}

// DeleteCommodityByIds 批量删除商品
func (commodityService *CommodityService) DeleteCommodityByIds(ids []uint) (err error) {
	// 检查是否有关联的期货合约
	var count int64
	err = global.GVA_DB.Model(&dianjia.Instrument{}).Where("commodity_id IN ?", ids).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("选中的商品中存在关联期货合约的记录，无法删除")
	}

	err = global.GVA_DB.Delete(&[]dianjia.Commodity{}, "id IN ?", ids).Error
	return err
}

// UpdateCommodity 更新商品
func (commodityService *CommodityService) UpdateCommodity(req dianjia.CommodityRequest) (err error) {
	// 检查ProductID是否已被其他记录使用
	var existCommodity dianjia.Commodity
	if err = global.GVA_DB.Where("product_id = ? AND id != ?", req.ProductID, req.ID).First(&existCommodity).Error; err == nil {
		return errors.New("品种ID已被其他记录使用")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	commodity := dianjia.Commodity{
		Name:       req.Name,
		ProductID:  req.ProductID,
		ExchangeID: req.ExchangeID,
	}
	commodity.ID = req.ID

	err = global.GVA_DB.Model(&commodity).Updates(commodity).Error
	return err
}

// GetCommodity 根据id获取商品记录
func (commodityService *CommodityService) GetCommodity(id uint) (commodity dianjia.Commodity, err error) {
	err = global.GVA_DB.Preload("Instruments").Where("id = ?", id).First(&commodity).Error
	return
}

// GetCommodityInfoList 分页获取商品记录
func (commodityService *CommodityService) GetCommodityInfoList(req dianjia.CommodityListRequest) (list []dianjia.CommodityResponse, total int64, err error) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	
	// 构建查询条件
	db := global.GVA_DB.Model(&dianjia.Commodity{})
	
	if req.Name != "" {
		db = db.Where("name LIKE ?", "%"+req.Name+"%")
	}
	if req.ProductID != "" {
		db = db.Where("product_id LIKE ?", "%"+req.ProductID+"%")
	}
	if req.ExchangeID != "" {
		db = db.Where("exchange_id = ?", req.ExchangeID)
	}

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 获取分页数据
	var commodities []dianjia.Commodity
	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&commodities).Error
	if err != nil {
		return
	}

	// 转换为响应格式并获取关联的合约数量
	list = make([]dianjia.CommodityResponse, len(commodities))
	for i, commodity := range commodities {
		list[i] = dianjia.CommodityResponse{
			ID:         commodity.ID,
			Name:       commodity.Name,
			ProductID:  commodity.ProductID,
			ExchangeID: commodity.ExchangeID,
			CreatedAt:  commodity.CreatedAt,
			UpdatedAt:  commodity.UpdatedAt,
		}
		
		// 获取关联的合约数量
		var count int64
		global.GVA_DB.Model(&dianjia.Instrument{}).Where("commodity_id = ?", commodity.ID).Count(&count)
		list[i].InstrumentCount = int(count)
	}

	return list, total, err
}

// GetCommodityList 获取所有商品列表（用于下拉选择）
func (commodityService *CommodityService) GetCommodityList() (list []dianjia.CommodityResponse, err error) {
	var commodities []dianjia.Commodity
	err = global.GVA_DB.Order("name").Find(&commodities).Error
	if err != nil {
		return
	}

	list = make([]dianjia.CommodityResponse, len(commodities))
	for i, commodity := range commodities {
		list[i] = dianjia.CommodityResponse{
			ID:         commodity.ID,
			Name:       commodity.Name,
			ProductID:  commodity.ProductID,
			ExchangeID: commodity.ExchangeID,
		}
	}

	return list, err
}

// GetCommodityByProductID 根据品种ID获取商品
func (commodityService *CommodityService) GetCommodityByProductID(productID string) (commodity dianjia.Commodity, err error) {
	err = global.GVA_DB.Where("product_id = ?", productID).First(&commodity).Error
	return
}