/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    CancelContractDialog: typeof import('./../components/CancelContractDialog.vue')['default']
    CommoditySelector: typeof import('./../components/CommoditySelector.vue')['default']
    ContractCard: typeof import('./../components/ContractCard.vue')['default']
    ContractSummary: typeof import('./../components/ContractSummary.vue')['default']
    copy: typeof import('./../components/UserSelector copy.vue')['default']
    InstrumentSelector: typeof import('./../components/InstrumentSelector.vue')['default']
    InstrumentSelectorOrigin: typeof import('./../components/InstrumentSelectorOrigin.vue')['default']
    QuotationCard: typeof import('./../components/marketplace/QuotationCard.vue')['default']
    QuoteCard: typeof import('./../components/QuoteCard.vue')['default']
    TradeRequestItem: typeof import('./../components/TradeRequestItem.vue')['default']
    TradeRequestList: typeof import('./../components/TradeRequestList.vue')['default']
    TradeRequestSetterItem: typeof import('./../components/TradeRequestSetterItem.vue')['default']
    TradeRequestSetterList: typeof import('./../components/TradeRequestSetterList.vue')['default']
    UserSelector: typeof import('./../components/UserSelector.vue')['default']
  }
}
