<template>
  <view class="contract-summary">
    <scroll-view class="summary-scroll" scroll-y>
      <view class="scroll-content">
        <!-- 总体统计 -->
        <view class="summary-stats">
        <view class="stats-card">
          <text class="stats-title">{{ partner<PERSON><PERSON><PERSON> }}</text>
          <text class="stats-value">{{ Object.keys(groupedData).length }}</text>
        </view>
        <view class="stats-card">
          <text class="stats-title">总合同数</text>
          <text class="stats-value">{{ totalContracts }}</text>
        </view>
        <view class="stats-card">
          <text class="stats-title">可用数量</text>
          <text class="stats-value">{{ totalAvailable }}</text>
        </view>
      </view>

      <!-- 用户列表 -->
      <view
        v-for="(userInfo, userName) in groupedData"
        :key="userName"
        class="user-card"
      >
        <!-- 用户头部 -->
        <view class="user-header" @click="toggleUserExpanded(userName)">
          <view class="user-main-info">
            <text class="user-name">{{ userInfo.userName }}</text>
            <view class="user-stats">
              <text class="stat-item">{{ userInfo.totalContracts }}个合同</text>
              <text class="stat-item">可用{{ userInfo.totalAvailable }}手</text>
            </view>
          </view>
          <view class="expand-icon" :class="{ 'expanded': isUserExpanded(userName) }">
            <text class="icon">▼</text>
          </view>
        </view>

        <!-- 用户详情（可展开） -->
        <view v-if="isUserExpanded(userName)" class="user-details">
          <view
            v-for="(instrumentInfo, instrumentName) in userInfo.instruments"
            :key="instrumentName"
            class="instrument-card"
            :class="{ 'clickable': enableClick }"
            @click="handleInstrumentClick(instrumentInfo, userInfo)"
          >
            <!-- 期货合约头部 -->
            <view class="instrument-header">
              <view class="instrument-title-row">
                <text class="instrument-name">{{ instrumentInfo.instrumentName }}</text>
                <text class="summary-text">总计{{ instrumentInfo.totalQuantity }}手 | 可用{{ instrumentInfo.totalAvailable }}手</text>
              </view>
            </view>

            <!-- 合同类型详情 -->
            <view class="contract-types">
              <!-- 基差合同 -->
              <view
                v-if="instrumentInfo.basis.quantity > 0"
                class="contract-type-card basis-card"
              >
                <view class="type-header">
                  <text class="type-name">基差合同</text>
                  <text class="contract-count">{{ instrumentInfo.basis.contractCount }}个</text>
                </view>
                <view class="type-stats">
                  <view class="stat-group">
                    <text class="stat-label">剩余</text>
                    <text class="stat-value">{{ instrumentInfo.basis.quantity }}</text>
                  </view>
                  <view class="stat-group">
                    <text class="stat-label">冻结</text>
                    <text class="stat-value warning">{{ instrumentInfo.basis.frozenQuantity }}</text>
                  </view>
                  <view class="stat-group">
                    <text class="stat-label">可用</text>
                    <text class="stat-value available">{{ instrumentInfo.basis.availableQuantity }}</text>
                  </view>
                  <view class="stat-group">
                    <text class="stat-label">均基差</text>
                    <text class="stat-value price">{{ instrumentInfo.basis.avgPrice.toFixed(2) }}</text>
                  </view>
                </view>
              </view>

              <!-- 固定价合同 -->
              <view
                v-if="instrumentInfo.fixed.quantity > 0"
                class="contract-type-card fixed-card"
              >
                <view class="type-header">
                  <text class="type-name">固定价合同</text>
                  <text class="contract-count">{{ instrumentInfo.fixed.contractCount }}个</text>
                </view>
                <view class="type-stats">
                  <view class="stat-group">
                    <text class="stat-label">剩余</text>
                    <text class="stat-value">{{ instrumentInfo.fixed.quantity }}</text>
                  </view>
                  <view class="stat-group">
                    <text class="stat-label">冻结</text>
                    <text class="stat-value warning">{{ instrumentInfo.fixed.frozenQuantity }}</text>
                  </view>
                  <view class="stat-group">
                    <text class="stat-label">可用</text>
                    <text class="stat-value available">{{ instrumentInfo.fixed.availableQuantity }}</text>
                  </view>
                  <view class="stat-group">
                    <text class="stat-label">均价</text>
                    <text class="stat-value price">{{ instrumentInfo.fixed.avgPrice.toFixed(2) }}</text>
                  </view>
                </view>
              </view>

              <!-- 无合同提示 -->
              <view
                v-if="instrumentInfo.basis.quantity === 0 && instrumentInfo.fixed.quantity === 0"
                class="no-contracts"
              >
                <text class="no-contracts-text">暂无可用合同</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { IContract } from '@/types/contract'

// 合同类型信息
interface ContractTypeInfo {
  quantity: number          // 剩余数量
  frozenQuantity: number    // 冻结数量
  availableQuantity: number // 可用数量
  avgPrice: number          // 平均价格
  contractCount: number     // 合同数量
}

// 期货合约信息
interface InstrumentInfo {
  instrumentId: number
  instrumentName: string
  basis: ContractTypeInfo
  fixed: ContractTypeInfo
  totalQuantity: number     // 总剩余数量
  totalFrozen: number       // 总冻结数量
  totalAvailable: number    // 总可用数量
  contracts: IContract[]
}

// 用户信息
interface UserInfo {
  userID: number
  userName: string
  instruments: { [instrumentName: string]: InstrumentInfo }
  totalContracts: number    // 总合同数
  totalQuantity: number     // 总剩余数量
  totalAvailable: number    // 总可用数量
}

// 分组数据
interface GroupedData {
  [userName: string]: UserInfo
}

interface Props {
  contracts: IContract[]    // 合同列表
  userRole: 'pricer' | 'setter'  // 用户角色，决定分组方式
  enableClick?: boolean     // 是否启用点击功能
}

interface Emits {
  (e: 'instrumentClick', data: { instrumentInfo: InstrumentInfo, userInfo: UserInfo }): void
}

const props = withDefaults(defineProps<Props>(), {
  enableClick: false
})

const emit = defineEmits<Emits>()

// 展开状态管理
const expandedUsers = ref<Set<string>>(new Set())

// 合作伙伴标签
const partnerLabel = computed(() => {
  return props.userRole === 'pricer' ? '合作伙伴' : '点价方'
})

// 计算分组数据
const groupedData = computed<GroupedData>(() => {
  const grouped: GroupedData = {}

  props.contracts.forEach(contract => {
    // 只处理执行中状态且有剩余数量的合同
    if (contract.status !== 'Executing' || contract.remainingQuantity <= 0) {
      return
    }

    // 根据用户角色决定分组方式
    let userName: string
    let userID: number

    if (props.userRole === 'pricer') {
      // pricer视图：按setter分组
      userName = contract.setter?.nickName || contract.setter?.userName || `用户${contract.setterID}`
      userID = contract.setterID
    } else {
      // setter视图：按pricer分组
      userName = contract.pricer?.nickName || contract.pricer?.userName || `用户${contract.pricerID}`
      userID = contract.pricerID
    }

    const instrumentName = contract.instrument?.instrument_name || `合约${contract.instrumentRefID}`
    const instrumentId = contract.instrumentRefID

    // 初始化用户信息
    if (!grouped[userName]) {
      grouped[userName] = {
        userID,
        userName,
        instruments: {},
        totalContracts: 0,
        totalQuantity: 0,
        totalAvailable: 0
      }
    }

    // 初始化期货合约信息
    if (!grouped[userName].instruments[instrumentName]) {
      grouped[userName].instruments[instrumentName] = {
        instrumentId,
        instrumentName,
        basis: {
          quantity: 0,
          frozenQuantity: 0,
          availableQuantity: 0,
          avgPrice: 0,
          contractCount: 0
        },
        fixed: {
          quantity: 0,
          frozenQuantity: 0,
          availableQuantity: 0,
          avgPrice: 0,
          contractCount: 0
        },
        totalQuantity: 0,
        totalFrozen: 0,
        totalAvailable: 0,
        contracts: []
      }
    }

    const userInfo = grouped[userName]
    const instrumentInfo = userInfo.instruments[instrumentName]

    // 添加合同到列表
    instrumentInfo.contracts.push(contract)

    // 计算数量和价格
    const remainingQty = contract.remainingQuantity
    const frozenQty = contract.frozenQuantity || 0
    const availableQty = remainingQty - frozenQty

    if (contract.priceType === 'basis') {
      const typeInfo = instrumentInfo.basis
      const currentTotalValue = typeInfo.quantity * typeInfo.avgPrice
      const newTotalValue = currentTotalValue + remainingQty * contract.priceValue

      typeInfo.quantity += remainingQty
      typeInfo.frozenQuantity += frozenQty
      typeInfo.availableQuantity += availableQty
      typeInfo.avgPrice = typeInfo.quantity > 0 ? newTotalValue / typeInfo.quantity : 0
      typeInfo.contractCount += 1
    } else if (contract.priceType === 'fixed') {
      const typeInfo = instrumentInfo.fixed
      const currentTotalValue = typeInfo.quantity * typeInfo.avgPrice
      const newTotalValue = currentTotalValue + remainingQty * contract.priceValue

      typeInfo.quantity += remainingQty
      typeInfo.frozenQuantity += frozenQty
      typeInfo.availableQuantity += availableQty
      typeInfo.avgPrice = typeInfo.quantity > 0 ? newTotalValue / typeInfo.quantity : 0
      typeInfo.contractCount += 1
    }

    // 更新期货合约汇总
    instrumentInfo.totalQuantity += remainingQty
    instrumentInfo.totalFrozen += frozenQty
    instrumentInfo.totalAvailable += availableQty

    // 更新用户汇总
    userInfo.totalContracts += 1
    userInfo.totalQuantity += remainingQty
    userInfo.totalAvailable += availableQty
  })

  return grouped
})

// 计算总合同数
const totalContracts = computed(() => {
  return Object.values(groupedData.value).reduce((sum, user) => sum + user.totalContracts, 0)
})

// 计算总可用数量
const totalAvailable = computed(() => {
  return Object.values(groupedData.value).reduce((sum, user) => sum + user.totalAvailable, 0)
})

// 切换用户展开/折叠状态
function toggleUserExpanded(userName: string | number) {
  const key = String(userName)
  if (expandedUsers.value.has(key)) {
    expandedUsers.value.delete(key)
  } else {
    expandedUsers.value.add(key)
  }
}

// 检查用户是否展开
function isUserExpanded(userName: string | number): boolean {
  return expandedUsers.value.has(String(userName))
}

// 处理期货合约点击
function handleInstrumentClick(instrumentInfo: InstrumentInfo, userInfo: UserInfo) {
  if (props.enableClick) {
    emit('instrumentClick', { instrumentInfo, userInfo })
  }
}
</script>

<style lang="scss" scoped>
.contract-summary {
  .summary-scroll {
    height: calc(100vh - 200rpx);
    padding: 20rpx 0;
  }

  .scroll-content {
    padding: 0 20rpx;
  }

  .summary-stats {
    display: flex;
    gap: 20rpx;
    margin-bottom: 30rpx;

    .stats-card {
      flex: 1;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16rpx;
      padding: 24rpx;
      text-align: center;
      box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);

      .stats-title {
        display: block;
        font-size: 24rpx;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 8rpx;
      }

      .stats-value {
        display: block;
        font-size: 36rpx;
        font-weight: 700;
        color: white;
      }
    }
  }

  .user-card {
    background: white;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
    overflow: hidden;

    .user-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx 30rpx;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
      }

      .user-main-info {
        flex: 1;

        .user-name {
          font-size: 32rpx;
          font-weight: 700;
          color: #1a1a1a;
          margin-bottom: 8rpx;
          display: block;
        }

        .user-stats {
          display: flex;
          gap: 20rpx;

          .stat-item {
            font-size: 24rpx;
            color: #666;
            background: rgba(102, 126, 234, 0.1);
            padding: 4rpx 12rpx;
            border-radius: 12rpx;
          }
        }
      }

      .expand-icon {
        transition: transform 0.3s ease;

        &.expanded {
          transform: rotate(180deg);
        }

        .icon {
          font-size: 24rpx;
          color: #666;
        }
      }
    }

    .user-details {
      padding: 20rpx 30rpx 30rpx;
      background: #fafafa;

      .instrument-card {
        background: white;
        border-radius: 12rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
        overflow: hidden;
        transition: all 0.3s ease;

        &.clickable {
          cursor: pointer;

          &:hover {
            transform: translateY(-2rpx);
            box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
          }

          &:active {
            transform: translateY(0);
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
          }
        }

        &:last-child {
          margin-bottom: 0;
        }

        .instrument-header {
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          padding: 20rpx 24rpx;
          border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

          .instrument-title-row {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .instrument-name {
              font-size: 28rpx;
              font-weight: 600;
              color: #1a1a1a;
              flex: 1;
            }

            .summary-text {
              font-size: 24rpx;
              color: #666;
              flex-shrink: 0;
            }
          }
        }

        .contract-types {
          padding: 0;

          .contract-type-card {
            border-radius: 0;
            padding: 16rpx 24rpx;
            margin-bottom: 0;
            border: none;
            border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

            &:last-child {
              border-bottom: none;
            }

            &.basis-card {
              background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(102, 126, 234, 0.02) 100%);
              border-color: rgba(102, 126, 234, 0.1);
            }

            &.fixed-card {
              background: linear-gradient(135deg, rgba(230, 162, 60, 0.05) 0%, rgba(230, 162, 60, 0.02) 100%);
              border-color: rgba(230, 162, 60, 0.1);
            }

            .type-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12rpx;

              .type-name {
                font-size: 26rpx;
                font-weight: 600;
                color: #1a1a1a;
              }

              .contract-count {
                font-size: 22rpx;
                color: #666;
                background: rgba(0, 0, 0, 0.05);
                padding: 4rpx 8rpx;
                border-radius: 8rpx;
              }
            }

            .type-stats {
              display: flex;
              justify-content: space-between;
              align-items: center;
              gap: 12rpx;

              .stat-group {
                display: flex;
                flex-direction: column;
                align-items: center;
                flex: 1;

                .stat-label {
                  font-size: 20rpx;
                  color: #999;
                  margin-bottom: 4rpx;
                }

                .stat-value {
                  font-size: 24rpx;
                  font-weight: 600;
                  color: #1a1a1a;

                  &.warning {
                    color: #f56c6c;
                  }

                  &.available {
                    color: #67c23a;
                  }

                  &.price {
                    color: #e6a23c;
                  }
                }
              }
            }
          }

          .no-contracts {
            text-align: center;
            padding: 40rpx 20rpx;
            color: #999;

            .no-contracts-text {
              font-size: 26rpx;
            }
          }
        }
      }
    }
  }
}
</style>
