import service from '@/utils/request'

// @Tags Instrument
// @Summary 创建期货合约
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Instrument true "期货合约模型"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /instrument/createInstrument [post]
export const createInstrument = (data) => {
  return service({
    url: '/instrument/createInstrument',
    method: 'post',
    data
  })
}

// @Tags Instrument
// @Summary 删除期货合约
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除期货合约"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /instrument/deleteInstrument [delete]
export const deleteInstrument = (params) => {
  return service({
    url: '/instrument/deleteInstrument',
    method: 'delete',
    params
  })
}

// @Tags Instrument
// @Summary 批量删除期货合约
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除期货合约"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /instrument/deleteInstrumentByIds [delete]
export const deleteInstrumentByIds = (params) => {
  return service({
    url: '/instrument/deleteInstrumentByIds',
    method: 'delete',
    params
  })
}

// @Tags Instrument
// @Summary 更新期货合约
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Instrument true "期货合约模型"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /instrument/updateInstrument [put]
export const updateInstrument = (data) => {
  return service({
    url: '/instrument/updateInstrument',
    method: 'put',
    data
  })
}

// @Tags Instrument
// @Summary 用id查询期货合约
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.Instrument true "用id查询期货合约"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /instrument/findInstrument [get]
export const findInstrument = (params) => {
  return service({
    url: '/instrument/findInstrument',
    method: 'get',
    params
  })
}

// @Tags Instrument
// @Summary 分页获取期货合约列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取期货合约列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /instrument/getInstrumentList [get]
export const getInstrumentList = (params) => {
  return service({
    url: '/instrument/getInstrumentList',
    method: 'get',
    params
  })
}

// @Tags Instrument
// @Summary 获取期货合约选择器列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query object true "期货合约选择器查询条件"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /instrument/getInstrumentSelectList [get]
export const getInstrumentSelectList = (params) => {
  return service({
    url: '/instrument/getInstrumentSelectList',
    method: 'get',
    params
  })
}

// @Tags Instrument
// @Summary 获取按交易所分组的期货合约列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /instrument/getInstrumentsByExchange [get]
export const getInstrumentsByExchange = () => {
  return service({
    url: '/instrument/getInstrumentsByExchange',
    method: 'get'
  })
}