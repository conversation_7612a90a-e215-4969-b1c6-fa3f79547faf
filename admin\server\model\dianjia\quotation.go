package dianjia

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

// QuotationStatus 报价状态枚举
type QuotationStatus string

const (
	QuotationStatusDraft  QuotationStatus = "Draft"  // 草稿
	QuotationStatusActive QuotationStatus = "Active" // 激活（公开可见）
)

// QuotationPriceType 报价价格类型枚举
type QuotationPriceType string

const (
	QuotationPriceTypeFixed QuotationPriceType = "Fixed" // 一口价
	QuotationPriceTypeBasis QuotationPriceType = "Basis" // 基差报价
)

// Quotation 报价表
type Quotation struct {
	global.GVA_MODEL

	// 基本信息
	UserID uint           `json:"userID" gorm:"column:user_id;not null;comment:报价发布者的用户ID"`
	User   system.SysUser `json:"user" gorm:"foreignKey:UserID"`
	Title  string         `json:"title" gorm:"column:title;type:varchar(255);not null;comment:报价的醒目标题"`

	// 商品信息
	CommodityID      uint      `json:"commodityID" gorm:"column:commodity_id;not null;comment:关联的商品种类ID"`
	Commodity        Commodity `json:"commodity" gorm:"foreignKey:CommodityID"`
	DeliveryLocation string    `json:"deliveryLocation" gorm:"column:delivery_location;type:varchar(255);not null;comment:交货地点"`
	Brand            string    `json:"brand" gorm:"column:brand;type:varchar(100);comment:品牌"`
	Specifications   string    `json:"specifications" gorm:"column:specifications;type:text;comment:规格说明"`
	Description      string    `json:"description" gorm:"column:description;type:text;comment:补充说明"`

	// 价格信息
	PriceType       QuotationPriceType `json:"priceType" gorm:"column:price_type;type:varchar(50);not null;comment:报价方式(Fixed/Basis)"`
	Price           float64            `json:"price" gorm:"column:price;type:decimal(10,2);not null;comment:价格或基差值"`
	InstrumentRefID *uint              `json:"instrumentRefID" gorm:"column:instrument_ref_id;comment:关联的期货合约ID(基差报价用)"`
	InstrumentRef   *Instrument        `json:"instrumentRef" gorm:"foreignKey:InstrumentRefID"`

	// 生命周期管理
	ExpiresAt time.Time       `json:"expiresAt" gorm:"column:expires_at;not null;comment:报价的精确过期时间"`
	Status    QuotationStatus `json:"status" gorm:"column:status;type:varchar(50);not null;default:'Draft';comment:报价状态"`
}

// CreateQuotationRequest 创建报价请求
type CreateQuotationRequest struct {
	Title            string             `json:"title" binding:"required"`
	CommodityID      uint               `json:"commodityID" binding:"required"`
	DeliveryLocation string             `json:"deliveryLocation" binding:"required"`
	Brand            string             `json:"brand"`
	Specifications   string             `json:"specifications"`
	Description      string             `json:"description"`
	PriceType        QuotationPriceType `json:"priceType" binding:"required,oneof=Fixed Basis"`
	Price            float64            `json:"price" binding:"required"`
	InstrumentRefID  *uint              `json:"instrumentRefID"`
	ExpiresAt        time.Time          `json:"expiresAt" binding:"required"`
	Status           QuotationStatus    `json:"status" binding:"oneof=Draft Active"` // 创建时只能是Draft或直接Active
}

// UpdateQuotationRequest 更新报价请求
type UpdateQuotationRequest struct {
	ID               uint               `json:"id" binding:"required"`
	Title            string             `json:"title" binding:"required"`
	CommodityID      uint               `json:"commodityID" binding:"required"`
	DeliveryLocation string             `json:"deliveryLocation" binding:"required"`
	Brand            string             `json:"brand"`
	Specifications   string             `json:"specifications"`
	Description      string             `json:"description"`
	PriceType        QuotationPriceType `json:"priceType" binding:"required,oneof=Fixed Basis"`
	Price            float64            `json:"price" binding:"required"`
	InstrumentRefID  *uint              `json:"instrumentRefID"`
	ExpiresAt        time.Time          `json:"expiresAt" binding:"required"`
}

// QuotationListRequest 报价列表请求（公开市场用）
type QuotationListRequest struct {
	Page        int                `json:"page" form:"page"`
	PageSize    int                `json:"pageSize" form:"pageSize"`
	CommodityID *uint              `json:"commodityID" form:"commodityID"` // 商品种类筛选
	Keyword     string             `json:"keyword" form:"keyword"`         // 关键词搜索（标题、企业名称等）
	PriceType   QuotationPriceType `json:"priceType" form:"priceType"`     // 价格类型筛选
}

// MyQuotationListRequest 我的报价列表请求
type MyQuotationListRequest struct {
	Page     int             `json:"page" form:"page"`
	PageSize int             `json:"pageSize" form:"pageSize"`
	Filter   string          `json:"filter" form:"filter"` // valid/invalid - 有效报价/无效报价
	Status   QuotationStatus `json:"status" form:"status"` // 具体状态筛选
}

// QuotationResponse 报价响应
type QuotationResponse struct {
	ID               uint               `json:"id"`
	UserID           uint               `json:"userID"`
	User             system.SysUser     `json:"user"`
	Title            string             `json:"title"`
	CommodityID      uint               `json:"commodityID"`
	Commodity        Commodity          `json:"commodity"`
	DeliveryLocation string             `json:"deliveryLocation"`
	Brand            string             `json:"brand"`
	Specifications   string             `json:"specifications"`
	Description      string             `json:"description"`
	PriceType        QuotationPriceType `json:"priceType"`
	Price            float64            `json:"price"`
	InstrumentRefID  *uint              `json:"instrumentRefID"`
	InstrumentRef    *Instrument        `json:"instrumentRef"`
	ExpiresAt        time.Time          `json:"expiresAt"`
	Status           QuotationStatus    `json:"status"`
	CreatedAt        time.Time          `json:"createdAt"`
	UpdatedAt        time.Time          `json:"updatedAt"`

	// 计算字段
	IsExpired      bool `json:"isExpired"`      // 是否已过期
	RemainingHours int  `json:"remainingHours"` // 剩余小时数
}

// QuotationListResponse 报价列表响应
type QuotationListResponse struct {
	List     []QuotationResponse `json:"list"`
	Total    int64               `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"pageSize"`
}

// PublishQuotationRequest 发布报价请求（将Draft状态改为Active，自动设置过期时间）
type PublishQuotationRequest struct {
	ID        uint      `json:"id" binding:"required"`
	ExpiresAt time.Time `json:"expiresAt" binding:"required"` // 发布时设置过期时间
}

// ToggleQuotationStatusRequest 切换报价状态请求（Active <-> Draft）
type ToggleQuotationStatusRequest struct {
	ID uint `json:"id" binding:"required"`
}

// DeleteQuotationRequest 删除报价请求
type DeleteQuotationRequest struct {
	ID uint `json:"id" binding:"required"`
}

// TableName 指定表名
func (Quotation) TableName() string {
	return "dj_quotations"
}
