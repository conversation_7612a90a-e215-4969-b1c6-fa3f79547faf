"""
HTTP客户端封装

提供统一的HTTP请求接口，包括：
- 异步HTTP请求
- 自动重试机制
- 超时控制
- 错误处理
- 请求/响应日志
"""

import asyncio
import logging
from typing import Optional, Dict, Any, Union
from datetime import datetime

import httpx

from ..exceptions import NetworkException, TimeoutException, handle_async_auth_exception
from ..utils.config import ConfigManager


class HttpClient:
    """HTTP客户端封装类
    
    提供统一的HTTP请求接口，支持自动重试、超时控制等功能。
    """
    
    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """初始化HTTP客户端
        
        Args:
            config_manager: 配置管理器（可选）
        """
        self.config_manager = config_manager or ConfigManager()
        self.logger = logging.getLogger(__name__)
        
        # 从配置获取参数
        self.timeout = self.config_manager.get("auth.server.timeout", 30)
        self.retry_count = self.config_manager.get("auth.server.retry_count", 3)
        self.retry_delay = self.config_manager.get("auth.server.retry_delay", 1)
        
        # 创建HTTP客户端
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(self.timeout),
            follow_redirects=True,
            verify=True  # 验证SSL证书
        )
        
        self.logger.info(f"HTTP客户端初始化完成 - 超时: {self.timeout}s, 重试次数: {self.retry_count}")
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def close(self):
        """关闭HTTP客户端"""
        if self.client:
            await self.client.aclose()
            self.logger.info("HTTP客户端已关闭")
    
    @handle_async_auth_exception
    async def get(self, url: str, params: Optional[Dict[str, Any]] = None,
                  headers: Optional[Dict[str, str]] = None, **kwargs) -> httpx.Response:
        """发送GET请求
        
        Args:
            url: 请求URL
            params: 查询参数
            headers: 请求头
            **kwargs: 其他参数
            
        Returns:
            httpx.Response: HTTP响应
            
        Raises:
            NetworkException: 网络错误
            TimeoutException: 超时错误
        """
        return await self._request("GET", url, params=params, headers=headers, **kwargs)
    
    @handle_async_auth_exception
    async def post(self, url: str, data: Optional[Union[Dict, str, bytes]] = None,
                   json: Optional[Dict[str, Any]] = None,
                   headers: Optional[Dict[str, str]] = None, **kwargs) -> httpx.Response:
        """发送POST请求
        
        Args:
            url: 请求URL
            data: 请求数据
            json: JSON数据
            headers: 请求头
            **kwargs: 其他参数
            
        Returns:
            httpx.Response: HTTP响应
            
        Raises:
            NetworkException: 网络错误
            TimeoutException: 超时错误
        """
        return await self._request("POST", url, data=data, json=json, headers=headers, **kwargs)
    
    @handle_async_auth_exception
    async def put(self, url: str, data: Optional[Union[Dict, str, bytes]] = None,
                  json: Optional[Dict[str, Any]] = None,
                  headers: Optional[Dict[str, str]] = None, **kwargs) -> httpx.Response:
        """发送PUT请求
        
        Args:
            url: 请求URL
            data: 请求数据
            json: JSON数据
            headers: 请求头
            **kwargs: 其他参数
            
        Returns:
            httpx.Response: HTTP响应
            
        Raises:
            NetworkException: 网络错误
            TimeoutException: 超时错误
        """
        return await self._request("PUT", url, data=data, json=json, headers=headers, **kwargs)
    
    @handle_async_auth_exception
    async def delete(self, url: str, headers: Optional[Dict[str, str]] = None,
                     **kwargs) -> httpx.Response:
        """发送DELETE请求
        
        Args:
            url: 请求URL
            headers: 请求头
            **kwargs: 其他参数
            
        Returns:
            httpx.Response: HTTP响应
            
        Raises:
            NetworkException: 网络错误
            TimeoutException: 超时错误
        """
        return await self._request("DELETE", url, headers=headers, **kwargs)
    
    async def _request(self, method: str, url: str, **kwargs) -> httpx.Response:
        """发送HTTP请求（带重试机制）
        
        Args:
            method: HTTP方法
            url: 请求URL
            **kwargs: 请求参数
            
        Returns:
            httpx.Response: HTTP响应
            
        Raises:
            NetworkException: 网络错误
            TimeoutException: 超时错误
        """
        last_exception = None
        
        for attempt in range(self.retry_count + 1):
            try:
                # 记录请求日志
                self._log_request(method, url, attempt)
                
                # 发送请求
                start_time = datetime.now()
                response = await self.client.request(method, url, **kwargs)
                end_time = datetime.now()
                
                # 记录响应日志
                self._log_response(response, start_time, end_time)
                
                # 检查响应状态
                if response.status_code >= 400:
                    self._handle_http_error(response)
                
                return response
                
            except httpx.TimeoutException as e:
                last_exception = TimeoutException(
                    f"请求超时: {url}",
                    url=url,
                    timeout_seconds=self.timeout
                )
                self.logger.warning(f"请求超时 (尝试 {attempt + 1}/{self.retry_count + 1}): {url}")
                
            except httpx.NetworkError as e:
                last_exception = NetworkException(
                    f"网络错误: {str(e)}",
                    url=url
                )
                self.logger.warning(f"网络错误 (尝试 {attempt + 1}/{self.retry_count + 1}): {str(e)}")
                
            except Exception as e:
                last_exception = NetworkException(
                    f"请求失败: {str(e)}",
                    url=url
                )
                self.logger.error(f"请求异常 (尝试 {attempt + 1}/{self.retry_count + 1}): {str(e)}")
            
            # 如果不是最后一次尝试，等待后重试
            if attempt < self.retry_count:
                await asyncio.sleep(self.retry_delay * (attempt + 1))  # 递增延迟
        
        # 所有重试都失败，抛出最后一个异常
        if last_exception:
            raise last_exception
        else:
            raise NetworkException(f"请求失败: {url}", url=url)
    
    def _log_request(self, method: str, url: str, attempt: int):
        """记录请求日志
        
        Args:
            method: HTTP方法
            url: 请求URL
            attempt: 尝试次数
        """
        if attempt == 0:
            self.logger.info(f"发送 {method} 请求: {url}")
        else:
            self.logger.info(f"重试 {method} 请求 (第{attempt + 1}次): {url}")
    
    def _log_response(self, response: httpx.Response, start_time: datetime, end_time: datetime):
        """记录响应日志
        
        Args:
            response: HTTP响应
            start_time: 请求开始时间
            end_time: 请求结束时间
        """
        duration = (end_time - start_time).total_seconds()
        
        if response.status_code < 400:
            self.logger.info(
                f"请求成功: {response.status_code} - {response.url} "
                f"(耗时: {duration:.2f}s)"
            )
        else:
            self.logger.warning(
                f"请求失败: {response.status_code} - {response.url} "
                f"(耗时: {duration:.2f}s)"
            )
    
    def _handle_http_error(self, response: httpx.Response):
        """处理HTTP错误响应
        
        Args:
            response: HTTP响应
            
        Raises:
            NetworkException: 网络错误
        """
        error_message = f"HTTP错误: {response.status_code}"
        
        # 尝试获取错误详情
        try:
            if response.headers.get("content-type", "").startswith("application/json"):
                error_data = response.json()
                if isinstance(error_data, dict):
                    error_message = error_data.get("message", error_message)
        except:
            pass
        
        raise NetworkException(
            error_message,
            url=str(response.url),
            status_code=response.status_code
        )
    
    def set_default_headers(self, headers: Dict[str, str]):
        """设置默认请求头
        
        Args:
            headers: 默认请求头
        """
        self.client.headers.update(headers)
        self.logger.info(f"设置默认请求头: {list(headers.keys())}")
    
    def set_auth_token(self, token: str):
        """设置认证Token
        
        Args:
            token: 认证Token
        """
        self.client.headers["Authorization"] = f"Bearer {token}"
        self.logger.info("设置认证Token")
    
    def clear_auth_token(self):
        """清除认证Token"""
        if "Authorization" in self.client.headers:
            del self.client.headers["Authorization"]
            self.logger.info("清除认证Token")
    
    def get_client_info(self) -> Dict[str, Any]:
        """获取客户端信息
        
        Returns:
            Dict[str, Any]: 客户端信息
        """
        return {
            "timeout": self.timeout,
            "retry_count": self.retry_count,
            "retry_delay": self.retry_delay,
            "headers": dict(self.client.headers),
            "base_url": str(self.client.base_url) if self.client.base_url else None
        }
