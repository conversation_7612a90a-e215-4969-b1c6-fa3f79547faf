#!/usr/bin/env python3
"""
登录点击测试脚本

测试点击登录按钮是否正常工作
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QVBoxLayout, QWidget, QPushButton, QLabel, QLineEdit
from PySide6.QtCore import QTimer

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_login_click():
    """测试登录点击功能"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from vnpy.event import EventEngine
        from ui.login.login_window import LoginWindow
        
        # 创建事件引擎
        event_engine = EventEngine()
        
        # 创建登录窗口
        login_window = LoginWindow(event_engine)

        # 显示窗口（非模态）
        login_window.show()
        
        # 创建测试控制窗口
        test_widget = QWidget()
        test_widget.setWindowTitle("登录测试控制")
        test_widget.resize(400, 300)
        
        layout = QVBoxLayout(test_widget)
        
        info_label = QLabel("登录功能测试\n请在登录窗口中输入测试数据，然后点击下面的按钮")
        layout.addWidget(info_label)
        
        # 填充测试数据按钮
        fill_data_btn = QPushButton("填充测试数据")
        def fill_test_data():
            # 切换到密码登录Tab
            login_window.login_widget.tab_widget.setCurrentIndex(0)
            
            # 填充测试数据
            login_window.login_widget.username_edit.setText("test_user")
            login_window.login_widget.password_edit.setText("test_password")
            login_window.login_widget.captcha_edit.setText("1234")
            
            print("✅ 测试数据已填充")
        
        fill_data_btn.clicked.connect(fill_test_data)
        layout.addWidget(fill_data_btn)
        
        # 填充手机登录数据按钮
        fill_phone_btn = QPushButton("填充手机登录数据")
        def fill_phone_data():
            # 切换到手机登录Tab
            login_window.login_widget.tab_widget.setCurrentIndex(1)
            
            # 填充测试数据
            login_window.login_widget.phone_edit.setText("18678863949")
            login_window.login_widget.code_edit.setText("123456")
            
            print("✅ 手机登录数据已填充")
        
        fill_phone_btn.clicked.connect(fill_phone_data)
        layout.addWidget(fill_phone_btn)
        
        # 模拟登录按钮点击
        test_login_btn = QPushButton("模拟点击登录按钮")
        def test_login():
            try:
                login_window.on_login_clicked()
                print("✅ 登录按钮点击成功，没有异常")
            except Exception as e:
                print(f"❌ 登录按钮点击失败: {str(e)}")
                import traceback
                traceback.print_exc()
        
        test_login_btn.clicked.connect(test_login)
        layout.addWidget(test_login_btn)
        
        # 清空输入框按钮
        clear_btn = QPushButton("清空输入框")
        def clear_inputs():
            login_window.login_widget.username_edit.clear()
            login_window.login_widget.password_edit.clear()
            login_window.login_widget.captcha_edit.clear()
            login_window.login_widget.phone_edit.clear()
            login_window.login_widget.code_edit.clear()
            print("✅ 输入框已清空")
        
        clear_btn.clicked.connect(clear_inputs)
        layout.addWidget(clear_btn)
        
        # 测试验证功能按钮
        test_validation_btn = QPushButton("测试验证功能（空输入）")
        def test_validation():
            try:
                # 清空输入
                clear_inputs()
                # 尝试登录
                login_window.on_login_clicked()
                print("✅ 验证功能正常，应该显示错误消息")
            except Exception as e:
                print(f"❌ 验证功能异常: {str(e)}")
                import traceback
                traceback.print_exc()
        
        test_validation_btn.clicked.connect(test_validation)
        layout.addWidget(test_validation_btn)
        
        # 显示测试窗口
        test_widget.show()
        
        # 设置窗口关闭时退出应用
        def on_window_closed():
            app.quit()
        
        login_window.finished.connect(on_window_closed)
        test_widget.closeEvent = lambda event: on_window_closed()
        
        print("=" * 50)
        print("登录点击测试启动")
        print("=" * 50)
        print("1. 点击'填充测试数据'按钮填充用户名密码")
        print("2. 点击'填充手机登录数据'按钮填充手机号")
        print("3. 点击'模拟点击登录按钮'测试登录功能")
        print("4. 点击'测试验证功能'测试空输入验证")
        print("5. 观察控制台输出和登录窗口反应")
        print("=" * 50)
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_click()
