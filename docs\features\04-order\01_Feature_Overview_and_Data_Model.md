# 功能模块：期货本地下单

## 1. 功能模块简介 (Summary)

本功能模块允许用户通过客户端（PC应用、Web页面或移动端）进行期货下单操作。为保证交易的低延迟和高可靠性，客户端与服务器之间采用 WebSocket 长连接进行实时双向通信。所有连接状态及路由信息将通过 Redis 进行集中管理，以支持服务器的水平扩展和高可用。

## 2. 数据定义 (Data Definition)

### 2.1 客户端 -> 服务器消息

#### 2.1.1 认证请求 (Authentication Request)

连接建立后，客户端发送的第一条消息。

```json
{
  "type": "auth",
  "payload": {
    "token": "user_jwt_token_string"
  }
}
```

#### 2.1.2 下单请求 (Order Placement Request)

```json
{
  "type": "place_order",
  "payload": {
    "client_order_id": "client_generated_uuid_12345",
    "symbol": "FUT.GC2412",
    "direction": "BUY",
    "order_type": "LIMIT",
    "volume": 10,
    "price": 2350.5
  }
}
```

#### 2.1.3 撤单请求 (Order Cancellation Request)

```json
{
  "type": "cancel_order",
  "payload": {
    "client_order_id": "client_generated_uuid_12345"
  }
}
```

#### 2.1.4 心跳请求 (Ping)

```json
{
  "type": "ping",
  "payload": {
    "timestamp": 1678886400000
  }
}
```

### 2.2 服务器 -> 客户端消息

#### 2.2.1 认证响应 (Authentication Response)

```json
{
  "type": "auth_response",
  "payload": {
    "success": true,
    "message": "Authentication successful"
  }
}
```

#### 2.2.2 订单状态更新 (Order Status Update)

```json
{
  "type": "order_update",
  "payload": {
    "client_order_id": "client_generated_uuid_12345",
    "server_order_id": "server_generated_id_67890",
    "status": "SUBMITTED",
    "symbol": "FUT.GC2412",
    "volume": 10,
    "filled_volume": 0,
    "avg_fill_price": 0.0,
    "message": "订单已提交至交易所"
  }
}
```

#### 2.2.3 心跳响应 (Pong)

```json
{
  "type": "pong",
  "payload": {
    "timestamp": 1678886400100
  }
}
```
