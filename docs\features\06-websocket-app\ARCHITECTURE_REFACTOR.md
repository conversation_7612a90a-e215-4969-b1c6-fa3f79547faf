# WebSocket Store 架构重构文档

## 重构概述

本次重构解决了 `socket.ts` 和 `market.ts` 之间的循环依赖问题，并实现了基于事件处理器注册的消息处理机制。

## 重构前的问题

### 循环依赖问题
```
socket.ts → (动态导入) → market.ts → (静态导入) → socket.ts
```

### 硬编码消息处理
- `socket.ts` 中使用硬编码的 switch-case 处理消息
- 每增加新的消息类型都需要修改 `socket.ts`
- 业务逻辑与通信逻辑耦合

## 重构后的架构

### 1. 依赖关系
```
socket.ts ← (单向依赖) ← market.ts
```
- **socket.ts**: 不依赖任何业务 store，只负责 WebSocket 通信
- **market.ts**: 单向依赖 socket.ts，负责行情业务逻辑

### 2. 事件处理机制

#### socket.ts 的改造
```typescript
// 事件处理器映射表
const eventHandlers = new Map<string, Function[]>()

// 注册事件处理器
function registerHandler(eventType: string, handler: Function) {
  if (!eventHandlers.has(eventType)) {
    eventHandlers.set(eventType, [])
  }
  const handlers = eventHandlers.get(eventType)!
  if (!handlers.includes(handler)) {
    handlers.push(handler)
  }
}

// 取消注册事件处理器
function unregisterHandler(eventType: string, handler: Function) {
  const handlers = eventHandlers.get(eventType)
  if (handlers) {
    const index = handlers.indexOf(handler)
    if (index > -1) {
      handlers.splice(index, 1)
      if (handlers.length === 0) {
        eventHandlers.delete(eventType)
      }
    }
  }
}

// 消息处理逻辑
async function handleMessage(event: MessageEvent) {
  const message: IMessageEnvelope = JSON.parse(event.data)
  
  // 处理内置事件 (pong, auth_response, notification, trade_update)
  switch (message.event) {
    // ... 内置事件处理
  }

  // 调用已注册的事件处理器
  const handlers = eventHandlers.get(message.event)
  if (handlers && handlers.length > 0) {
    handlers.forEach(handler => {
      try {
        handler(message.payload)
      } catch (error) {
        console.error(`事件处理器执行失败 (${message.event}):`, error)
      }
    })
  }
}
```

#### market.ts 的改造
```typescript
export const useMarketStore = defineStore('market', () => {
  // 状态定义 - 使用响应式对象而不是Map/Set
  const marketData = ref<Record<string, MarketData>>({})
  const subscribedSymbols = ref<string[]>([])

  // 获取socket store实例
  const socketStore = useSocketStore()

  // 行情更新处理器
  function handleMarketUpdate(data: any) {
    // 处理行情数据更新逻辑，使用响应式对象
    marketData.value[symbol] = marketInfo
  }

  // 初始化事件处理器注册
  function initializeEventHandlers() {
    socketStore.registerHandler('market_update', handleMarketUpdate)
  }

  // 清理事件处理器注册
  function cleanupEventHandlers() {
    socketStore.unregisterHandler('market_update', handleMarketUpdate)
  }

  // 自动初始化事件处理器
  initializeEventHandlers()

  return {
    // ... 其他方法
    cleanupEventHandlers,
    handleMarketUpdate
  }
})
```

## 架构优势

### 1. 解决循环依赖
- **单向依赖**: 所有业务 store 单向依赖 socket.ts
- **清晰的层次结构**: socket.ts 作为基础通信层，业务 store 作为应用层
- **响应式优化**: 使用 Record 和 Array 替代 Map 和 Set，确保 Vue 3 响应式系统正常工作

### 2. 高度可扩展
- **动态注册**: 支持运行时注册和取消注册事件处理器
- **多处理器支持**: 同一事件可以注册多个处理器
- **插件化**: 新的业务模块可以独立注册自己的事件处理器

### 3. 低耦合
- **业务逻辑分离**: socket.ts 不包含任何业务逻辑
- **独立测试**: 每个 store 可以独立测试
- **模块化**: 业务模块可以独立开发和维护

### 4. 错误隔离
- **异常处理**: 单个处理器的异常不会影响其他处理器
- **日志记录**: 详细的错误日志便于调试

### 5. Vue 3 响应式兼容
- **数据结构优化**: 使用 `Record<string, MarketData>` 替代 `Map<string, MarketData>`
- **数组替代Set**: 使用 `string[]` 替代 `Set<string>`
- **响应式更新**: 确保UI能够正确响应数据变化
- **性能提升**: 避免Vue响应式系统对Map/Set的兼容性问题

## 使用示例

### 注册新的事件处理器
```typescript
// 在任何业务 store 中
export const useTradeStore = defineStore('trade', () => {
  const socketStore = useSocketStore()

  function handleTradeUpdate(data: any) {
    // 处理交易更新
  }

  function handleOrderUpdate(data: any) {
    // 处理订单更新
  }

  // 注册多个事件处理器
  socketStore.registerHandler('trade_update', handleTradeUpdate)
  socketStore.registerHandler('order_update', handleOrderUpdate)

  return {
    // ... 方法
  }
})
```

### 在组件中使用
```vue
<script setup lang="ts">
import { onUnmounted } from 'vue'
import { useMarketStore } from '@/store'

const marketStore = useMarketStore()

onUnmounted(() => {
  // 清理事件处理器
  marketStore.cleanupEventHandlers()
})
</script>
```

## 测试验证

### 1. 依赖关系测试
- ✅ socket.ts 不导入任何业务 store
- ✅ market.ts 单向导入 socket.ts
- ✅ 无循环依赖警告

### 2. 事件处理测试
- ✅ 事件处理器正确注册
- ✅ 消息正确分发到处理器
- ✅ 处理器异常不影响其他处理器
- ✅ 处理器可以正确取消注册

### 3. 功能测试
- ✅ 行情订阅功能正常
- ✅ 行情数据更新正常
- ✅ WebSocket 连接状态正常
- ✅ Vue组件响应式更新正常
- ✅ Redis JSON数据解析正常

## 迁移指南

### 对于现有代码
1. **无需修改**: 现有的 `subscribe`、`unsubscribe` 等方法保持不变
2. **自动初始化**: 事件处理器在 store 创建时自动注册
3. **手动清理**: 在组件卸载时调用 `cleanupEventHandlers()`

### 对于新功能
1. **创建处理器**: 在业务 store 中创建事件处理器函数
2. **注册处理器**: 使用 `socketStore.registerHandler()` 注册
3. **清理处理器**: 在适当时机调用 `unregisterHandler()`

## 性能影响

- **内存使用**: 略微增加（事件处理器映射表）
- **执行效率**: 基本无影响（Map 查找 O(1)）
- **开发效率**: 显著提升（模块化、可扩展）

## 后续优化建议

1. **类型安全**: 为事件处理器添加 TypeScript 类型定义
2. **事件命名**: 建立事件命名规范
3. **处理器优先级**: 支持处理器执行优先级
4. **异步处理器**: 支持异步事件处理器
5. **事件过滤**: 支持事件过滤和条件处理
