# 功能文档：公开报价市场 (Public Quotation Marketplace)

> **版本**: 1.2.0 (Consolidated)
> **负责人**: Gemini
> **状态**: 设计中

---

## 1. 功能概述 (Functional Overview)

本功能旨在创建一个双向的报价生态系统。一方面，它为用户（“报价方”）提供了一套完整的工具来创建、管理和发布商品报价；另一方面，它为所有市场参与者提供了一个公开、透明的平台来浏览、搜索和发现这些报价，并为未来的交易撮合奠定基础。

- **报价方工作流**: 用户可以创建报价草稿，进行编辑，在合适的时机发布到公开市场，并管理其已发布报价的生命周期。
- **公开市场工作流**: 任何用户（包括未登录用户）都可以访问市场，按条件筛选和搜索报价，并查看报价的详细信息。

---

## 2. 数据模型与状态机 (Data Model & State Machine)

### 2.1. 报价表 (`quotations`)

| 字段名 | 类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT` | PK, AI | 唯一ID |
| `user_id` | `BIGINT` | NOT NULL, FK | 报价发布者的用户ID |
| `title` | `VARCHAR(255)`| NOT NULL | 报价的醒目标题 |
| `commodity_id` | `BIGINT` | NOT NULL, FK | 关联的商品种类ID |
| `delivery_location` | `VARCHAR(255)`| NOT NULL | 交货地点 |
| `brand` | `VARCHAR(100)`| NULL | 品牌 |
| `specifications` | `TEXT` | NULL | 规格说明 |
| `description` | `TEXT` | NULL | 补充说明 |
| `price_type` | `ENUM('Fixed', 'Basis')` | NOT NULL | 报价方式 |
| `price` | `DECIMAL(10,2)`| NOT NULL | 价格或基差值 |
| `instrument_ref_id` | `BIGINT` | NULL, FK | 关联的期货合约ID (基差报价) |
| `expires_at` | `TIMESTAMP` | NOT NULL | 报价的精确过期时间 |
| **`status`** | `ENUM('Draft', 'Active')` | NOT NULL, DEFAULT 'Draft' | **报价状态** |
| `created_at` | `TIMESTAMP` | | 创建时间 |
| `updated_at` | `TIMESTAMP` | | 更新时间 |

### 2.2. 报价状态机 (Quotation State Machine)

```mermaid
graph TD
    A[Draft] -- Publish --> B(Active);
    B -- Expires (Scheduler) --> A;
    B -- Toggle Status (User) --> A;
    A -- Toggle Status (User) --> B;
    A -- Edit --> A;
    A -- Delete --> E[Deleted];
    B -- Delete --> E[Deleted];
```

---

## 3. 报价方工作流与UI设计 (Quoter's Workflow & UI)

### 3.1. 创建/编辑报价页 (`/pages/quotes/edit.vue`)
-   **功能**: 一个统一的表单页面，用于创建新报价或修改现有报价。
-   **模式**: 通过路由判断是创建 (`/quotes/create`) 还是编辑 (`/quotes/edit/{id}`) 模式。
-   **有效期处理**: 前端提供便捷选项（如 `当日有效`, `3天内有效`），并计算出最终的 `expires_at` 时间戳发送给后端。
-   **核心操作**: 
    -   `[ 保存草稿 ]`: 保存或更新报价，状态保持或变为 `Draft`。
    -   `[ 直接发布 ]`: 保存或更新报价，并直接将状态设置为 `Active`。

### 3.2. 我的报价列表 (`/pages/quotes/my-list.vue`)
-   **功能**: 集中展示和管理当前用户创建的所有报价。
-   **布局**: 顶部是“创建新报价”按钮和状态过滤器，下方是报价列表。
-   **过滤器**: 提供两个核心过滤选项：
    -   **有效报价**: 显示所有 `status = 'Active'` 的报价。
    -   **无效报价**: 显示所有 `status IN ('Draft', 'Expired', 'Withdrawn')` 的报价。
-   **列表项操作**: 列表中的每一项都应提供快捷操作按钮，如 `编辑`、`发布` (仅对草稿)、`撤回` (仅对激活)、`删除` (仅对草稿)。

---

## 4. 公开市场工作流与UI设计 (Public Marketplace Workflow & UI)

### 4.1. 公开报价市场页 (`/pages/quotes/marketplace.vue`)
-   **功能**: 作为所有用户发现报价的入口，展示所有 `status = 'Active'` 的报价。
-   **布局**: 采用“左侧分类，右侧列表”或“顶部筛选，下方列表”的经典布局。
-   **筛选与搜索**:
    -   **分类筛选**: 提供按**商品种类 (`commodity`)** 进行快速分类查看的功能。
    -   **模糊搜索框**: 一个显眼的搜索框，允许用户输入关键词，对报价的`标题`、`企业名称`（通过`user_id`关联查询）、`商品种类`等字段进行模糊匹配。
-   **报价卡片列表**: 
  - 列表中的每一项都是一个简洁的“报价卡片”，突出显示核心信息：
    -   **标题**
    -   **价格**: 根据`price_type`格式化显示，如 `¥ 3850.00` 或 `IF2509 + 150`。
    -   **商品与地点**: `商品种类` - `交货地点`。
    -   **发布方**: 发布企业/用户的名称。
    -   **有效期**: `剩余 2 天` 或 `今日到期`。

### 4.2. 报价详情页 (`/pages/quotes/{id}.vue`) - **自适应视图**
-   **设计原则**: **采用单个页面组件，内部通过逻辑判断进行自适应显示**，避免代码冗余。
-   **判断逻辑**: 在页面加载时，通过 `store.currentUser.id === quote.user_id` 判断当前访问者是否为报价的发布者。
-   **视图差异**:
    -   **公众视角 (Public View)**:
        -   **信息分层**: 
            -   **主信息区 (突出显示)**: 使用大号字体或独立卡片，清晰展示最关键的交易信息（商品种类、交货地点、价格）。
            -   **次要信息区**: 使用标准字体展示其他补充信息（报价标题、发布方、品牌、规格、有效期等）。
        -   **行动号召 (Call to Action)**: 在页面底部或侧边设置一个明确的按钮，如 `[ 对此报价感兴趣，发起点价 ]`，用于衔接交易流程。
    -   **发布者视角 (Owner View)**:
        -   **不显示** 发布者自己的企业信息（因为是多余信息）。
        -   在页面顶部或底部**显示**一个“编辑此报价”的按钮，链接到编辑页。
        -   **不显示**“发起点价”按钮。

---

## 5. 后台核心逻辑 (Backend Logic)

- **报价生命周期管理**: 后端根据API调用来处理报价状态的流转（Draft -> Active, Active -> Withdrawn）。
- **定时过期任务**: 一个可靠的后台定时任务（Cron Job）必须稳定运行，每小时查询并更新所有 `status = 'Active'` 且 `expires_at < NOW()` 的报价，将其状态置为 `Expired`。

---

## 6. 统一接口定义 (Consolidated API Definition)

| 功能描述 | HTTP 方法 | 路径 (Endpoint) | 核心参数/Body | 角色/说明 |
| :--- | :--- | :--- | :--- | :--- |
| **保存报价草稿/更新** | `POST` / `PUT` | `/api/v1/quotations` | `Quotation` 对象 | Quoter. 创建或更新，状态为`Draft` |
| **发布报价** | `POST` | `/api/v1/quotations/{id}/publish` | (无) | Quoter. 将`Draft`状态的报价变为`Active` |
| **获取我的报价列表** | `GET` | `/api/v1/my-quotations` | `?filter=valid/invalid` | Quoter. 获取自己的报价列表 |
| **撤回我的报价** | `POST` | `/api/v1/quotations/{id}/withdraw` | (无) | Quoter. 将`Active`变为`Withdrawn` |
| **删除我的草稿** | `DELETE` | `/api/v1/quotations/{id}` | (无) | Quoter. 只能删除`Draft`状态的报价 |
| **获取公开报价列表** | `GET` | `/api/v1/quotations` | `?commodity_id=1&q=关键词` | Public. 只返回`Active`状态的 |
| **获取报价详情** | `GET` | `/api/v1/quotations/{id}` | (无) | Public/Quoter. 后端返回完整对象，前端适配 |

---

## 7. 统一测试用例 (Consolidated Test Cases)

| 用例ID | 模块 | 场景描述 | 预期结果 |
| :--- | :--- | :--- | :--- |
| `TC-QT-001` | 报价方 | **创建一口价草稿** | 报价创建成功，`status`为`Draft`，不在市场页显示。 |
| `TC-QT-002` | 报价方 | **草稿与发布流程** | 用户在“我的报价”中找到草稿并点击“发布”，报价状态变为`Active`，在市场页可见。 |
| `TC-QT-003` | 报价方 | **无效报价过滤** | 在“我的报价”页选择“无效报价”过滤器，列表正确显示`Draft`, `Expired`, `Withdrawn`状态的报价。 |
| `TC-QT-004` | 公开市场 | **搜索和筛选** | 在市场页按`螺纹钢`分类并搜索`中粮`，列表正确显示`中粮`发布的`螺纹钢`报价。 |
| `TC-QT-005` | 公开市场 | **详情页视图切换** | 发布者A访问自己报价的详情页看到“编辑”按钮；用户B访问则看到A的公司名和“发起点价”按钮。 |
| `TC-QT-006` | 后台 | **报价自动过期** | 一个`Active`的报价在到达`expires_at`时间后，被定时任务更新为`Expired`状态，并从市场页消失。 |

---

## 8. 注意事项 (Notes/Caveats)

- **权限控制**: 创建、编辑和管理报价需要用户登录认证。浏览公开市场和查看详情则无需认证。
- **定时任务可靠性**: 必须确保用于处理过期的后台定时任务是稳定且可靠的，否则市场将充斥大量无效报价。
- **下一步集成**: 下一阶段的核心工作是将详情页的“发起点价”按钮与`01-basis-point-trading`中定义的**交易请求 (Trade Request)** 流程完全打通。