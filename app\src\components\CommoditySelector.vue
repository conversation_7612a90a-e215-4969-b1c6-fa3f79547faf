<script lang="ts" setup>
import type { ICommodity } from '@/types/commodity'
import { ref, computed, watch } from 'vue'
import { getAllCommodityList } from '@/api/commodity'

// 组件属性
interface Props {
  modelValue?: number
  label?: string
  placeholder?: string
  customClass?: string
  customLabelClass?: string
  customValueClass?: string
}

// 组件事件
interface Emits {
  (e: 'update:modelValue', value: number): void
  (e: 'change', commodity: ICommodity | null): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 0,
  label: '商品选择',
  placeholder: '请选择商品种类',
  customClass: '',
  customLabelClass: '',
  customValueClass: '',
})

const emit = defineEmits<Emits>()

// 响应式数据
const showModal = ref(false)
const searchKeyword = ref('')
const selectedCommodity = ref<ICommodity | null>(null)
const availableCommodities = ref<ICommodity[]>([])
const isLoading = ref(false)

// 计算属性
const currentCommodityId = computed({
  get: () => props.modelValue,
  set: (value: number) => {
    emit('update:modelValue', value)
  }
})

// 方法
async function loadAvailableCommodities() {
  try {
    isLoading.value = true
    const res = await getAllCommodityList()
    const allCommodities = res.data || []
    
    // 如果有搜索关键词，进行过滤
    if (searchKeyword.value.trim()) {
      availableCommodities.value = allCommodities.filter(commodity => 
        commodity.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
      )
    } else {
      availableCommodities.value = allCommodities
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    uni.showToast({
      title: '获取商品列表失败',
      icon: 'error'
    })
    availableCommodities.value = []
  } finally {
    isLoading.value = false
  }
}

function showCommodityPicker() {
  loadAvailableCommodities()
  showModal.value = true
}

function searchCommodities() {
  // 搜索商品时重新加载列表
  loadAvailableCommodities()
}

function confirmCommoditySelection(commodity: ICommodity) {
  selectedCommodity.value = commodity
  currentCommodityId.value = commodity.id
  showModal.value = false
  emit('change', commodity)
}

function removeCommodity() {
  selectedCommodity.value = null
  currentCommodityId.value = 0
  emit('change', null)
}

// 初始化时根据 modelValue 设置选中商品
async function initSelectedCommodity() {
  if (props.modelValue && props.modelValue > 0) {
    try {
      // 先尝试从已加载的列表中查找
      let commodity = availableCommodities.value.find(c => c.id === props.modelValue)
      
      // 如果未找到，重新加载完整列表
      if (!commodity) {
        await loadAvailableCommodities()
        commodity = availableCommodities.value.find(c => c.id === props.modelValue)
      }
      
      selectedCommodity.value = commodity || null
    } catch (error) {
      console.error('获取商品信息失败:', error)
      selectedCommodity.value = null
    }
  }
}

// 监听 modelValue 变化
watch(() => props.modelValue, async (newValue) => {
  if (newValue && newValue > 0 && (!selectedCommodity.value || selectedCommodity.value.id !== newValue)) {
    await initSelectedCommodity()
  } else if (!newValue || newValue === 0) {
    selectedCommodity.value = null
  }
}, { immediate: true })
</script>

<template>
  <view :class="['commodity-selector-compact', customClass]">
    <view class="commodity-content">
      <view v-if="!selectedCommodity" class="empty-commodity-compact">
        {{ placeholder }}
      </view>
      <view v-else class="selected-commodity-compact">
        <text class="commodity-name">
          {{ selectedCommodity.name }}
        </text>
      </view>
    </view>
    
    <wd-button v-if="!selectedCommodity" type="primary" size="small" custom-class="dj-btn-primary" @click="showCommodityPicker">
      选择
    </wd-button>
    <wd-button v-else type="error" size="small" custom-class="dj-btn-danger" @click="removeCommodity">
      移除
    </wd-button>

    <!-- 商品选择弹窗 -->
    <wd-popup v-model="showModal" position="bottom" custom-style="height: 70%" custom-class="dj-popup">
      <view class="commodity-picker">
        <view class="picker-header">
          <text class="picker-title">
            {{ label }}
          </text>
        </view>

        <view class="picker-content">
          <wd-search v-model="searchKeyword" placeholder="搜索商品种类" custom-class="dj-search" @search="searchCommodities" />

          <view class="commodity-list">
            <view v-if="isLoading" class="loading-container">
              <wd-loading type="ring" color="#667eea" />
              <text class="loading-text">加载中...</text>
            </view>
            <view v-else-if="availableCommodities.length === 0" class="empty-container">
              <text class="empty-text">暂无商品数据</text>
            </view>
            <view v-else>
              <view v-for="commodity in availableCommodities" :key="commodity.id" class="commodity-item" @click="confirmCommoditySelection(commodity)">
                <view class="commodity-info">
                  <text class="commodity-name">
                    {{ commodity.name }}
                  </text>
                </view>
                <wd-radio :value="selectedCommodity?.id === commodity.id" custom-class="dj-radio" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
// 基础变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$secondary-color: #764ba2;
$text-primary: #303133;
$text-secondary: #606266;
$text-light: #909399;
$font-size-large: 32rpx;
$font-size-medium: 28rpx;
$font-size-title: 36rpx;
$box-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
$transition-base: all 0.3s ease;

// 商品选择器
.commodity-selector-compact {
  display: flex;
  align-items: center;
  gap: 20rpx;
  background-color: #f9fafc;
  padding: 24rpx;
  border-radius: 8rpx;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4rpx;
    background: linear-gradient(180deg, $secondary-color, $primary-color);
  }

  .commodity-content {
    flex: 1;

    .empty-commodity-compact {
      color: $text-light;
      font-size: $font-size-medium;
      font-style: italic;
    }

    .selected-commodity-compact {
      display: flex;
      align-items: center;
      gap: 15rpx;
      background-color: rgba(102, 126, 234, 0.05);
      padding: 12rpx 16rpx;
      border-radius: 8rpx;

      .commodity-name {
        font-size: $font-size-medium;
        color: $text-primary;
        font-weight: 500;
      }

      .commodity-symbol {
        font-size: 24rpx;
        color: $text-secondary;
        background-color: rgba(102, 126, 234, 0.1);
        padding: 4rpx 8rpx;
        border-radius: 4rpx;
      }
    }
  }
}

// 弹窗选择器
.commodity-picker {
  height: 100%;
  display: flex;
  flex-direction: column;

  .picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    background: $primary-gradient;

    .picker-title {
      font-size: $font-size-title;
      font-weight: bold;
      color: #ffffff;
      text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
    }
  }

  .picker-content {
    flex: 1;
    padding: 20rpx;
    overflow-y: auto;
    background-color: #f9fafc;

    .commodity-list {
      margin-top: 20rpx;

      .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60rpx 0;
        
        .loading-text {
          margin-top: 20rpx;
          font-size: $font-size-medium;
          color: $text-secondary;
        }
      }

      .empty-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 60rpx 0;
        
        .empty-text {
          font-size: $font-size-medium;
          color: $text-light;
        }
      }

      .commodity-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 25rpx;
        background: white;
        border-radius: 8rpx;
        margin-bottom: 15rpx;
        box-shadow: $box-shadow-sm;
        position: relative;
        overflow: hidden;
        transition: $transition-base;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4rpx;
          background: linear-gradient(180deg, $primary-color, $secondary-color);
        }

        &:hover {
          background-color: #f9fafc;
          box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
        }

        .commodity-info {
          display: flex;
          align-items: center;
          gap: 12rpx;

          .commodity-name {
            font-size: $font-size-large;
            color: $text-primary;
            font-weight: 500;
          }

          .commodity-symbol {
            font-size: 24rpx;
            color: $text-secondary;
            background-color: rgba(102, 126, 234, 0.1);
            padding: 4rpx 8rpx;
            border-radius: 4rpx;
          }
        }
      }
    }
  }
}

// 深度选择器样式
:deep() {
  .dj-btn-primary {
    background: $primary-gradient;
    border: none;
    color: white;
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
    transition: $transition-base;

    &:hover {
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
      transform: translateY(-1rpx);
    }
  }

  .dj-btn-danger {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    border: none;
    color: white;
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
    transition: $transition-base;

    &:hover {
      box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
      transform: translateY(-1rpx);
    }
  }

  .dj-search {
    background: white;
    border-radius: 40rpx;
    padding: 0 16rpx;
    box-shadow: $box-shadow-sm;
    transition: $transition-base;

    &:focus-within {
      box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.15);
    }
  }

  .dj-radio {
    .wd-radio__label {
      color: $text-primary;
      font-size: $font-size-medium;
      font-weight: 500;
      padding-left: 12rpx;
    }

    .wd-radio__shape {
      border-color: #c0c4cc;
      transition: $transition-base;

      &.is-checked {
        background-color: $primary-color;
        border-color: $primary-color;
        box-shadow: 0 0 4rpx rgba(102, 126, 234, 0.3);
      }
    }
  }

  .dj-popup {
    border-radius: 20rpx 20rpx 0 0;
    overflow: hidden;
  }
}
</style>