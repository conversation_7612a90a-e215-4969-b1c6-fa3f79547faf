# WebSocket 服务重构方案

> **版本**: 1.0.0
> **负责人**: Gemini
> **状态**: 提案

---

## 1. 目标

将当前单一的 WebSocket 服务重构为两个独立、解耦的服务，分别用于满足 **App 端** 和 **Order 端** 的不同通信需求。此举旨在提高系统的可维护性、可扩展性和稳定性。

## 2. 核心问题分析

现有的 `@admin/server/service/ws.go` 实现采用了一个全局共享的 `Hub` 实例 (`global.GVA_WS_HUB`)，这导致了以下问题：

1.  **单点瓶颈**: 所有类型的客户端（App/Order）都连接到同一个 Hub，业务逻辑耦合在一起，难以独立扩展和维护。
2.  **协议僵化**: 无法为不同类型的客户端定义独立的 `MessageEnvelope` 或事件处理逻辑。例如，Order 端的通信协议可能与 App 端完全不同。
3.  **管理混乱**: 客户端管理、广播逻辑混杂，无法做到精细化控制和隔离。

## 3. 重构方案

核心思想是**服务拆分**和**职责分离**。我们将创建独立的 Hub 管理器、路由和业务逻辑处理文件。

### 3.1. 目录结构调整

为了清晰地组织代码，我们将在 `@admin/server/` 目录下创建一个新的包 `ws_hub`，专门用于存放所有 WebSocket 相关的业务逻辑。

```
admin/server/
├── service/
│   └── (删除或重构 ws.go)
├── router/
│   └── ws.go (将被修改)
├── global/
│   └── global.go (将被修改)
├── initialize/
│   └── ws.go (将新增此文件用于初始化)
└── ws_hub/
    ├── app_hub.go      # App端WebSocket服务的完整实现
    ├── order_hub.go    # Order端WebSocket服务的完整实现
    └── common.go       # (可选)存放共享的数据结构，如MessageEnvelope
```

### 3.2. 代码拆分与职责定义

1.  **`ws_hub/app_hub.go`**:
    *   **职责**: 完全负责与 App 客户端的 WebSocket 通信。
    *   **内容**:
        *   定义 `AppHub` 结构体，管理 App 客户端。
        *   定义 `AppClient` 结构体，代表一个 App 连接。
        *   包含 `ServeWsForApp` Gin Handler，用于处理 App 的连接请求。
        *   实现 `handleMessageForApp` 方法，处理来自 App 的所有事件（如 `auth`, `subscribe` 等）。
        *   此文件的初始代码将从现有的 `service/ws.go` 迁移和重构而来。

2.  **`ws_hub/order_hub.go`**:
    *   **职责**: 完全负责与 Order 客户端的 WebSocket 通信。
    *   **内容**:
        *   定义 `OrderHub` 结构体，管理 Order 客户端。
        *   定义 `OrderClient` 结构体，代表一个 Order 连接。
        *   包含 `ServeWsForOrder` Gin Handler，用于处理 Order 的连接请求。
        *   实现 `handleMessageForOrder` 方法，根据 Order 端的通信协议处理其特定事件。

### 3.3. 全局实例与初始化

我们将弃用单一的 `global.GVA_WS_HUB`。

1.  **`global/global.go`**:
    *   移除 `GVA_WS_HUB`。
    *   新增两个全局变量：
        ```go
        var (
            GVA_APP_HUB   *ws_hub.AppHub
            GVA_ORDER_HUB *ws_hub.OrderHub
        )
        ```

2.  **`initialize/ws.go` (新增)**:
    *   创建 `InitializeWebSocketHubs` 函数。
    *   在此函数中，分别创建 `AppHub` 和 `OrderHub` 的实例，并启动它们的 `Run()` 方法。
        ```go
        func InitializeWebSocketHubs() {
            global.GVA_APP_HUB = ws_hub.NewAppHub()
            go global.GVA_APP_HUB.Run()

            global.GVA_ORDER_HUB = ws_hub.NewOrderHub()
            go global.GVA_ORDER_HUB.Run()
        }
        ```
    *   在 `initialize/router.go` (或总初始化入口) 中调用 `InitializeWebSocketHubs()`。

### 3.4. 路由配置

路由需要区分 App 和 Order 的连接入口。

1.  **`router/ws.go`**:
    *   修改 `InitWsRouter` 函数。
    *   创建两个不同的路由端点，例如 `/ws/app` 和 `/ws/order`。
    *   每个路由端点分别调用其对应的 `ServeWs` 处理器。
        ```go
        import "github.com/flipped-aurora/gin-vue-admin/server/ws_hub"

        func InitWsRouter(Router *gin.RouterGroup) {
            wsRouter := Router.Group("ws")
            {
                // App 端 WebSocket 路由
                wsRouter.GET("/app", func(c *gin.Context) {
                    ws_hub.ServeWsForApp(global.GVA_APP_HUB, c)
                })

                // Order 端 WebSocket 路由
                wsRouter.GET("/order", func(c *gin.Context) {
                    ws_hub.ServeWsForOrder(global.GVA_ORDER_HUB, c)
                })
            }
        }
        ```

## 4. 需要修改/创建的文件列表

-   **`admin/server/ws_hub/app_hub.go` (创建)**
    *   **改动**: 新建文件，将原 `service/ws.go` 的逻辑迁移并重构至此，专门服务于 App 端。
-   **`admin/server/ws_hub/order_hub.go` (创建)**
    *   **改动**: 新建文件，为 Order 端实现一套独立的 WebSocket 服务逻辑。
-   **`admin/server/initialize/ws.go` (创建)**
    *   **改动**: 新建文件，用于统一初始化所有 WebSocket Hub。
-   **`admin/server/global/global.go` (修改)**
    *   **改动**: 移除旧的 `GVA_WS_HUB`，添加 `GVA_APP_HUB` 和 `GVA_ORDER_HUB`。
-   **`admin/server/router/ws.go` (修改)**
    *   **改动**: 更新路由，创建 `/ws/app` 和 `/ws/order` 两个端点，分别指向对应的处理函数。
-   **`admin/server/service/ws.go` (删除/清空)**
    *   **改动**: 文件内容已被重构至 `ws_hub` 包中，此文件可以删除或清空。
-   **`app/src/store/socket.ts` (修改)**
    *   **改动**: 将 WebSocket 连接地址 `VITE_WS_URL` 从 `ws://.../ws` 修改为 `ws://.../ws/app`。

## 5. 后续步骤

1.  根据本方案，执行代码重构。
2.  为 Order 端编写其客户端连接逻辑。
3.  分别对 App 端和 Order 端的 WebSocket 连接进行独立测试，确保功能正常且互不影响。
