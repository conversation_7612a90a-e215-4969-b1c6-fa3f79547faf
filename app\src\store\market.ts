import { defineStore } from 'pinia'
import { ref, readonly } from 'vue'
import { useSocketStore } from './socket'
import { toast } from '@/utils/toast'

// 行情数据接口，基于实际WebSocket数据结构
export interface MarketData {
  gateway_name: string
  symbol: string
  exchange: string
  datetime: string
  name: string
  volume: string
  turnover: string
  open_interest: string
  last_price: string
  last_volume: string
  limit_up: string
  limit_down: string
  open_price: string
  high_price: string
  low_price: string
  pre_close: string
  bid_price_1: string
  bid_price_2: string
  bid_price_3: string
  bid_price_4: string
  bid_price_5: string
  ask_price_1: string
  ask_price_2: string
  ask_price_3: string
  ask_price_4: string
  ask_price_5: string
  bid_volume_1: string
  bid_volume_2: string
  bid_volume_3: string
  bid_volume_4: string
  bid_volume_5: string
  ask_volume_1: string
  ask_volume_2: string
  ask_volume_3: string
  ask_volume_4: string
  ask_volume_5: string
  localtime: string
  vt_symbol: string
  // 可以根据需要添加更多字段
  [key: string]: any
}

export const useMarketStore = defineStore('market', () => {
  // 状态定义 - 使用响应式对象而不是Map/Set
  const marketData = ref<Record<string, MarketData>>({})
  const subscribedSymbols = ref<string[]>([])

  // 获取socket store实例
  const socketStore = useSocketStore()

  /**
   * 订阅指定合约的行情数据
   * @param symbol 合约代码，如 'IF2508'
   * @param exchange 交易所代码，如 'CFFEX'
   */
  function subscribe(symbol: string, exchange: string) {
    // 检查是否已经订阅
    if (subscribedSymbols.value.includes(symbol)) {
      console.log(`[MarketStore] 合约 ${symbol} 已订阅，跳过重复订阅`)
      return
    }

    // 检查WebSocket连接状态
    if (!socketStore.isConnected) {
      toast.error('WebSocket未连接，无法订阅行情')
      return
    }

    try {
      // 发送订阅请求
      socketStore.sendMessage('subscribe_market', {
        symbol,
        exchange
      })

      // 添加到已订阅列表
      subscribedSymbols.value.push(symbol)

      console.log(`[MarketStore] 已发送订阅请求: ${symbol} (${exchange})`)
      toast.success(`已订阅 ${symbol} 行情`)
    } catch (error) {
      console.error(`[MarketStore] 订阅失败:`, error)
      toast.error(`订阅 ${symbol} 行情失败`)
    }
  }

  /**
   * 取消订阅指定合约的行情数据
   * @param symbol 合约代码
   */
  function unsubscribe(symbol: string) {
    // 检查是否已订阅
    if (!subscribedSymbols.value.includes(symbol)) {
      console.log(`[MarketStore] 合约 ${symbol} 未订阅，跳过取消订阅`)
      return
    }

    // 检查WebSocket连接状态
    if (!socketStore.isConnected) {
      toast.error('WebSocket未连接，无法取消订阅')
      return
    }

    try {
      // 发送取消订阅请求
      socketStore.sendMessage('unsubscribe_market', {
        symbol
      })

      // 从已订阅列表中移除
      const index = subscribedSymbols.value.indexOf(symbol)
      if (index > -1) {
        subscribedSymbols.value.splice(index, 1)
      }

      // 从行情数据中移除
      delete marketData.value[symbol]

      console.log(`[MarketStore] 已发送取消订阅请求: ${symbol}`)
      toast.success(`已取消订阅 ${symbol} 行情`)
    } catch (error) {
      console.error(`[MarketStore] 取消订阅失败:`, error)
      toast.error(`取消订阅 ${symbol} 行情失败`)
    }
  }

  /**
   * 处理从WebSocket收到的行情更新
   * 这是一个内部方法，作为事件处理器注册到socketStore
   * @param data 行情数据
   */
  function handleMarketUpdate(data: any) {
    try {
      // 验证数据格式
      if (!data || typeof data !== 'object') {
        console.error('[MarketStore] 无效的行情数据格式:', data)
        return
      }

      const { symbol } = data
      if (!symbol) {
        console.error('[MarketStore] 行情数据缺少symbol字段:', data)
        return
      }

      // 检查是否是已订阅的合约
      if (!subscribedSymbols.value.includes(symbol)) {
        console.warn(`[MarketStore] 收到未订阅合约 ${symbol} 的行情数据`)
        return
      }

      // 更新行情数据 - 直接保存原始数据
      const marketInfo: MarketData = {
        gateway_name: data.gateway_name || '',
        symbol: data.symbol || '',
        exchange: data.exchange || '',
        datetime: data.datetime || '',
        name: data.name || '',
        volume: data.volume || '0',
        turnover: data.turnover || '0',
        open_interest: data.open_interest || '0',
        last_price: data.last_price || '0',
        last_volume: data.last_volume || '0',
        limit_up: data.limit_up || '0',
        limit_down: data.limit_down || '0',
        open_price: data.open_price || '0',
        high_price: data.high_price || '0',
        low_price: data.low_price || '0',
        pre_close: data.pre_close || '0',
        bid_price_1: data.bid_price_1 || '0',
        bid_price_2: data.bid_price_2 || '0',
        bid_price_3: data.bid_price_3 || '0',
        bid_price_4: data.bid_price_4 || '0',
        bid_price_5: data.bid_price_5 || '0',
        ask_price_1: data.ask_price_1 || '0',
        ask_price_2: data.ask_price_2 || '0',
        ask_price_3: data.ask_price_3 || '0',
        ask_price_4: data.ask_price_4 || '0',
        ask_price_5: data.ask_price_5 || '0',
        bid_volume_1: data.bid_volume_1 || '0',
        bid_volume_2: data.bid_volume_2 || '0',
        bid_volume_3: data.bid_volume_3 || '0',
        bid_volume_4: data.bid_volume_4 || '0',
        bid_volume_5: data.bid_volume_5 || '0',
        ask_volume_1: data.ask_volume_1 || '0',
        ask_volume_2: data.ask_volume_2 || '0',
        ask_volume_3: data.ask_volume_3 || '0',
        ask_volume_4: data.ask_volume_4 || '0',
        ask_volume_5: data.ask_volume_5 || '0',
        localtime: data.localtime || '',
        vt_symbol: data.vt_symbol || '',
        ...data // 保留其他字段
      }

      marketData.value[symbol] = marketInfo
      
      console.log(`[MarketStore] 更新行情数据: ${symbol}`, marketInfo)
    } catch (error) {
      console.error('[MarketStore] 处理行情更新失败:', error)
    }
  }

  /**
   * 获取指定合约的行情数据
   * @param symbol 合约代码
   * @returns 行情数据或undefined
   */
  function getMarketData(symbol: string): MarketData | undefined {
    return marketData.value[symbol]
  }

  /**
   * 获取所有行情数据
   * @returns 所有行情数据的Record对象
   */
  function getAllMarketData(): Record<string, MarketData> {
    return marketData.value
  }

  /**
   * 检查是否已订阅指定合约
   * @param symbol 合约代码
   * @returns 是否已订阅
   */
  function isSubscribed(symbol: string): boolean {
    return subscribedSymbols.value.includes(symbol)
  }

  /**
   * 获取所有已订阅的合约列表
   * @returns 已订阅合约的数组
   */
  function getSubscribedSymbols(): string[] {
    return [...subscribedSymbols.value]
  }

  /**
   * 清空所有行情数据和订阅关系
   * 通常在用户登出时调用
   */
  function clearAll() {
    marketData.value = {}
    subscribedSymbols.value = []
    console.log('[MarketStore] 已清空所有行情数据和订阅关系')
  }

  /**
   * 初始化事件处理器注册
   */
  function initializeEventHandlers() {
    // 注册market_update事件处理器
    socketStore.registerHandler('market_update', handleMarketUpdate)
    console.log('[MarketStore] 已注册market_update事件处理器')
  }

  /**
   * 清理事件处理器注册
   */
  function cleanupEventHandlers() {
    // 取消注册market_update事件处理器
    socketStore.unregisterHandler('market_update', handleMarketUpdate)
    console.log('[MarketStore] 已取消注册market_update事件处理器')
  }

  // 初始化事件处理器
  initializeEventHandlers()

  return {
    // 只读状态
    marketData: readonly(marketData),
    subscribedSymbols: readonly(subscribedSymbols),
    
    // 公开方法
    subscribe,
    unsubscribe,
    getMarketData,
    getAllMarketData,
    isSubscribed,
    getSubscribedSymbols,
    clearAll,
    cleanupEventHandlers,

    // 内部方法，作为事件处理器
    handleMarketUpdate
  }
})
