#!/usr/bin/env python3
"""
自适应高度测试脚本

测试登录窗口的自适应高度功能
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (
    QApplication, QVBoxLayout, QHBoxLayout, QWidget, 
    QPushButton, QLabel, QGroupBox
)
from PySide6.QtCore import QTimer

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_adaptive_height():
    """测试自适应高度功能"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from vnpy.event import EventEngine
        from ui.login.login_window import LoginWindow
        
        # 创建事件引擎
        event_engine = EventEngine()
        
        # 创建登录窗口
        login_window = LoginWindow(event_engine)
        
        # 创建测试控制面板
        test_panel = QGroupBox("自适应高度测试控制")
        test_layout = QVBoxLayout(test_panel)
        
        # 状态显示
        status_label = QLabel("窗口高度: 未知")
        test_layout.addWidget(status_label)
        
        def update_status():
            height = login_window.height()
            width = login_window.width()
            status_label.setText(f"窗口尺寸: {width}x{height}")
        
        # 定时更新状态
        status_timer = QTimer()
        status_timer.timeout.connect(update_status)
        status_timer.start(500)
        
        # 测试按钮行
        button_row1 = QHBoxLayout()
        button_row2 = QHBoxLayout()
        button_row3 = QHBoxLayout()
        
        # 切换Tab测试
        switch_tab_btn = QPushButton("切换Tab")
        def switch_tab():
            current = login_window.login_widget.tab_widget.currentIndex()
            new_index = 1 if current == 0 else 0
            login_window.login_widget.tab_widget.setCurrentIndex(new_index)
            tab_name = "密码登录" if new_index == 0 else "手机登录"
            print(f"✅ 切换到{tab_name}，窗口应该自适应调整高度")
            update_status()
        switch_tab_btn.clicked.connect(switch_tab)
        button_row1.addWidget(switch_tab_btn)
        
        # 手动调整大小
        adjust_btn = QPushButton("手动调整大小")
        def adjust_size():
            login_window.adjust_size_to_content()
            print("✅ 手动调整窗口大小以适应内容")
            update_status()
        adjust_btn.clicked.connect(adjust_size)
        button_row1.addWidget(adjust_btn)
        
        # 显示窗口信息
        info_btn = QPushButton("显示窗口信息")
        def show_info():
            height = login_window.height()
            width = login_window.width()
            min_height = login_window.minimumHeight()
            max_height = login_window.maximumHeight()
            print(f"📊 窗口信息:")
            print(f"   当前尺寸: {width}x{height}")
            print(f"   最小高度: {min_height}")
            print(f"   最大高度: {max_height}")
            print(f"   宽度: 固定{width}px")
            update_status()
        info_btn.clicked.connect(show_info)
        button_row2.addWidget(info_btn)
        
        # 测试内容变化
        content_change_btn = QPushButton("模拟内容变化")
        def test_content_change():
            # 触发凭据变化事件，这会导致窗口重新调整大小
            login_window.on_credentials_changed()
            print("✅ 触发内容变化，窗口应该重新调整大小")
            update_status()
        content_change_btn.clicked.connect(test_content_change)
        button_row2.addWidget(content_change_btn)
        
        # 重新居中
        center_btn = QPushButton("重新居中")
        def recenter():
            login_window.show_centered()
            print("✅ 窗口重新居中显示")
            update_status()
        center_btn.clicked.connect(recenter)
        button_row3.addWidget(center_btn)
        
        # 重置窗口
        reset_btn = QPushButton("重置窗口")
        def reset_window():
            login_window.setMinimumHeight(300)
            login_window.setMaximumHeight(800)
            login_window.adjust_size_to_content()
            print("✅ 窗口已重置")
            update_status()
        reset_btn.clicked.connect(reset_window)
        button_row3.addWidget(reset_btn)
        
        # 添加按钮行到布局
        test_layout.addLayout(button_row1)
        test_layout.addLayout(button_row2)
        test_layout.addLayout(button_row3)
        
        # 将测试面板添加到登录窗口
        main_layout = login_window.layout()
        main_layout.addWidget(test_panel)
        
        # 显示窗口（使用自适应高度）
        login_window.show_centered()
        
        # 初始状态更新
        update_status()
        
        # 设置窗口关闭时退出应用
        def on_window_closed():
            status_timer.stop()
            app.quit()
        
        login_window.finished.connect(on_window_closed)
        login_window.closeEvent = lambda event: (on_window_closed(), event.accept())
        
        print("=" * 60)
        print("自适应高度测试启动")
        print("=" * 60)
        print("功能特性:")
        print("1. 窗口宽度固定为400px")
        print("2. 窗口高度根据内容自动调整")
        print("3. 最小高度: 300px，最大高度: 800px")
        print("4. Tab切换时自动调整高度")
        print("5. 内容变化时自动重新调整")
        print("6. 窗口居中显示")
        print()
        print("测试操作:")
        print("- 切换Tab观察高度变化")
        print("- 查看窗口信息")
        print("- 测试内容变化响应")
        print("- 重新居中和重置功能")
        print("=" * 60)
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_adaptive_height()
