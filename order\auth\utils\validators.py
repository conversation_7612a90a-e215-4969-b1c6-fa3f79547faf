"""
输入验证工具

提供各种输入数据的验证功能，包括：
- 手机号验证
- 密码验证
- 用户名验证
- 验证码验证
"""

import re
import logging
from typing import Optional, List, Dict, Any
from abc import ABC, abstractmethod


class BaseValidator(ABC):
    """验证器基类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @abstractmethod
    def validate(self, value: str) -> bool:
        """验证输入值
        
        Args:
            value: 待验证的值
            
        Returns:
            bool: 验证是否通过
        """
        pass
    
    @abstractmethod
    def get_error_message(self) -> str:
        """获取错误消息
        
        Returns:
            str: 错误消息
        """
        pass


class PhoneValidator(BaseValidator):
    """手机号验证器
    
    支持中国大陆手机号格式验证。
    """
    
    def __init__(self):
        super().__init__()
        # 中国大陆手机号正则表达式
        self.pattern = re.compile(r'^1[3-9]\d{9}$')
        self.error_message = "手机号格式不正确"
    
    def validate(self, phone: str) -> bool:
        """验证手机号格式
        
        Args:
            phone: 手机号
            
        Returns:
            bool: 验证是否通过
        """
        if not phone:
            return False
        
        # 去除空格和特殊字符
        phone = re.sub(r'[\s\-\(\)]', '', phone)
        
        # 如果以+86开头，去除国家代码
        if phone.startswith('+86'):
            phone = phone[3:]
        elif phone.startswith('86') and len(phone) == 13:
            phone = phone[2:]
        
        return bool(self.pattern.match(phone))
    
    def get_error_message(self) -> str:
        return self.error_message
    
    def format_phone(self, phone: str) -> str:
        """格式化手机号
        
        Args:
            phone: 原始手机号
            
        Returns:
            str: 格式化后的手机号
        """
        if not self.validate(phone):
            return phone
        
        # 去除空格和特殊字符
        phone = re.sub(r'[\s\-\(\)]', '', phone)
        
        # 如果以+86开头，去除国家代码
        if phone.startswith('+86'):
            phone = phone[3:]
        elif phone.startswith('86') and len(phone) == 13:
            phone = phone[2:]
        
        return phone


class PasswordValidator(BaseValidator):
    """密码验证器
    
    支持密码强度验证。
    """
    
    def __init__(self, min_length: int = 6, max_length: int = 20,
                 require_uppercase: bool = False, require_lowercase: bool = False,
                 require_digit: bool = False, require_special: bool = False):
        """初始化密码验证器
        
        Args:
            min_length: 最小长度
            max_length: 最大长度
            require_uppercase: 是否需要大写字母
            require_lowercase: 是否需要小写字母
            require_digit: 是否需要数字
            require_special: 是否需要特殊字符
        """
        super().__init__()
        self.min_length = min_length
        self.max_length = max_length
        self.require_uppercase = require_uppercase
        self.require_lowercase = require_lowercase
        self.require_digit = require_digit
        self.require_special = require_special
        
        # 特殊字符集合
        self.special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        
        self.error_messages = []
    
    def validate(self, password: str) -> bool:
        """验证密码
        
        Args:
            password: 密码
            
        Returns:
            bool: 验证是否通过
        """
        self.error_messages = []
        
        if not password:
            self.error_messages.append("密码不能为空")
            return False
        
        # 长度检查
        if len(password) < self.min_length:
            self.error_messages.append(f"密码长度不能少于{self.min_length}位")
        
        if len(password) > self.max_length:
            self.error_messages.append(f"密码长度不能超过{self.max_length}位")
        
        # 字符类型检查
        if self.require_uppercase and not re.search(r'[A-Z]', password):
            self.error_messages.append("密码必须包含大写字母")
        
        if self.require_lowercase and not re.search(r'[a-z]', password):
            self.error_messages.append("密码必须包含小写字母")
        
        if self.require_digit and not re.search(r'\d', password):
            self.error_messages.append("密码必须包含数字")
        
        if self.require_special and not any(c in self.special_chars for c in password):
            self.error_messages.append("密码必须包含特殊字符")
        
        return len(self.error_messages) == 0
    
    def get_error_message(self) -> str:
        """获取错误消息
        
        Returns:
            str: 错误消息
        """
        return "; ".join(self.error_messages) if self.error_messages else ""
    
    def get_strength_score(self, password: str) -> int:
        """获取密码强度评分
        
        Args:
            password: 密码
            
        Returns:
            int: 强度评分 (0-100)
        """
        if not password:
            return 0
        
        score = 0
        
        # 长度评分 (最多30分)
        length_score = min(30, len(password) * 2)
        score += length_score
        
        # 字符类型评分 (每种类型15分)
        if re.search(r'[a-z]', password):
            score += 15
        if re.search(r'[A-Z]', password):
            score += 15
        if re.search(r'\d', password):
            score += 15
        if any(c in self.special_chars for c in password):
            score += 15
        
        # 复杂度评分 (最多10分)
        unique_chars = len(set(password))
        complexity_score = min(10, unique_chars)
        score += complexity_score
        
        return min(100, score)
    
    def get_strength_level(self, password: str) -> str:
        """获取密码强度等级
        
        Args:
            password: 密码
            
        Returns:
            str: 强度等级 (弱/中/强)
        """
        score = self.get_strength_score(password)
        
        if score < 40:
            return "弱"
        elif score < 70:
            return "中"
        else:
            return "强"


class UsernameValidator(BaseValidator):
    """用户名验证器
    
    支持用户名格式验证。
    """
    
    def __init__(self, min_length: int = 3, max_length: int = 20,
                 allow_chinese: bool = True, allow_special: bool = False):
        """初始化用户名验证器
        
        Args:
            min_length: 最小长度
            max_length: 最大长度
            allow_chinese: 是否允许中文
            allow_special: 是否允许特殊字符
        """
        super().__init__()
        self.min_length = min_length
        self.max_length = max_length
        self.allow_chinese = allow_chinese
        self.allow_special = allow_special
        
        # 构建正则表达式
        pattern_parts = [r'a-zA-Z0-9']
        if allow_chinese:
            pattern_parts.append(r'\u4e00-\u9fff')
        if allow_special:
            pattern_parts.append(r'_\-\.')
        
        self.pattern = re.compile(f'^[{"".join(pattern_parts)}]{{{min_length},{max_length}}}$')
        self.error_message = "用户名格式不正确"
    
    def validate(self, username: str) -> bool:
        """验证用户名
        
        Args:
            username: 用户名
            
        Returns:
            bool: 验证是否通过
        """
        if not username:
            return False
        
        # 长度检查
        if len(username) < self.min_length or len(username) > self.max_length:
            return False
        
        # 格式检查
        if not self.pattern.match(username):
            return False
        
        # 不能以特殊字符开头或结尾
        if self.allow_special:
            if username.startswith(('_', '-', '.')) or username.endswith(('_', '-', '.')):
                return False
        
        return True
    
    def get_error_message(self) -> str:
        return self.error_message


class SMSCodeValidator(BaseValidator):
    """短信验证码验证器"""
    
    def __init__(self, code_length: int = 6):
        """初始化验证码验证器
        
        Args:
            code_length: 验证码长度
        """
        super().__init__()
        self.code_length = code_length
        self.pattern = re.compile(f'^\\d{{{code_length}}}$')
        self.error_message = f"验证码应为{code_length}位数字"
    
    def validate(self, code: str) -> bool:
        """验证短信验证码
        
        Args:
            code: 验证码
            
        Returns:
            bool: 验证是否通过
        """
        if not code:
            return False
        
        return bool(self.pattern.match(code))
    
    def get_error_message(self) -> str:
        return self.error_message


class EmailValidator(BaseValidator):
    """邮箱验证器"""
    
    def __init__(self):
        super().__init__()
        # 邮箱正则表达式
        self.pattern = re.compile(
            r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        )
        self.error_message = "邮箱格式不正确"
    
    def validate(self, email: str) -> bool:
        """验证邮箱格式
        
        Args:
            email: 邮箱地址
            
        Returns:
            bool: 验证是否通过
        """
        if not email:
            return False
        
        return bool(self.pattern.match(email))
    
    def get_error_message(self) -> str:
        return self.error_message


class ValidationResult:
    """验证结果类"""
    
    def __init__(self, is_valid: bool, error_message: str = "", 
                 field_name: str = "", suggestions: Optional[List[str]] = None):
        self.is_valid = is_valid
        self.error_message = error_message
        self.field_name = field_name
        self.suggestions = suggestions or []
    
    def __bool__(self):
        return self.is_valid
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "is_valid": self.is_valid,
            "error_message": self.error_message,
            "field_name": self.field_name,
            "suggestions": self.suggestions
        }


class CompositeValidator:
    """复合验证器
    
    可以组合多个验证器进行验证。
    """
    
    def __init__(self):
        self.validators = {}
        self.logger = logging.getLogger(__name__)
    
    def add_validator(self, field_name: str, validator: BaseValidator):
        """添加验证器
        
        Args:
            field_name: 字段名
            validator: 验证器
        """
        self.validators[field_name] = validator
    
    def validate_field(self, field_name: str, value: str) -> ValidationResult:
        """验证单个字段
        
        Args:
            field_name: 字段名
            value: 字段值
            
        Returns:
            ValidationResult: 验证结果
        """
        if field_name not in self.validators:
            return ValidationResult(True)
        
        validator = self.validators[field_name]
        is_valid = validator.validate(value)
        error_message = validator.get_error_message() if not is_valid else ""
        
        return ValidationResult(is_valid, error_message, field_name)
    
    def validate_all(self, data: Dict[str, str]) -> Dict[str, ValidationResult]:
        """验证所有字段
        
        Args:
            data: 字段数据
            
        Returns:
            Dict[str, ValidationResult]: 验证结果
        """
        results = {}
        
        for field_name, value in data.items():
            results[field_name] = self.validate_field(field_name, value)
        
        return results
    
    def is_all_valid(self, data: Dict[str, str]) -> bool:
        """检查所有字段是否都有效
        
        Args:
            data: 字段数据
            
        Returns:
            bool: 是否都有效
        """
        results = self.validate_all(data)
        return all(result.is_valid for result in results.values())
