<template>
  <div class="contract-setter-list">
    <div class="gva-search-box">
      <el-form ref="searchForm" :model="searchInfo" label-width="80px" :inline="true">
        <el-form-item label="状态">
          <el-select v-model="searchInfo.status" placeholder="请选择状态" clearable>
            <el-option label="激活中" value="Active" />
            <el-option label="已完成" value="Completed" />
            <el-option label="已过期" value="Expired" />
            <el-option label="已取消" value="Cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始日期">
          <el-date-picker
            v-model="searchInfo.startDate"
            type="date"
            placeholder="选择开始日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="结束日期">
          <el-date-picker
            v-model="searchInfo.endDate"
            type="date"
            placeholder="选择结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getTableData">查询</el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="Plus" @click="openDialog('create')">新建合同</el-button>
      </div>

      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="contractCode" label="合同编码" width="150" />
        <el-table-column prop="pricer.nickName" label="点价方" width="120" />
        <el-table-column prop="instrument.name" label="期货合约" width="150" />
        <el-table-column label="数量信息" width="180">
          <template #default="scope">
            <div>总量: {{ scope.row.totalQuantity }}</div>
            <div>剩余: {{ scope.row.remainingQuantity }}</div>
            <div v-if="scope.row.frozenQuantity > 0" class="frozen-info">
              冻结: {{ scope.row.frozenQuantity }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="价格信息" width="150">
          <template #default="scope">
            <div>类型: {{ scope.row.priceType === 'basis' ? '基差' : '固定价' }}</div>
            <div>价值: {{ scope.row.priceValue }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300">
          <template #default="scope">
            <el-button size="small" type="primary" @click="viewDetail(scope.row)">详情</el-button>
            <el-button 
              v-if="canEdit(scope.row)" 
              size="small" 
              type="warning" 
              @click="openDialog('edit', scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              v-if="canArchive(scope.row)" 
              size="small" 
              type="info" 
              @click="archiveContract(scope.row)"
            >
              归档
            </el-button>
            <el-button 
              v-if="canCancel(scope.row)" 
              size="small" 
              type="danger" 
              @click="openCancelDialog(scope.row)"
            >
              取消
            </el-button>
            <el-button 
              v-if="scope.row.status !== 'Active'" 
              size="small" 
              @click="viewCancelRecords(scope.row)"
            >
              取消记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 合同表单对话框 -->
    <ContractFormDialog 
      v-model:visible="dialogVisible"
      :mode="dialogMode"
      :contract-data="currentContract"
      @success="handleDialogSuccess"
    />

    <!-- 取消合同对话框 -->
    <CancelContractDialog
      v-model:visible="cancelDialogVisible"
      :contract-data="currentContract"
      @success="handleCancelSuccess"
    />

    <!-- 取消记录对话框 -->
    <CancelRecordsDialog
      v-model:visible="recordsDialogVisible"
      :contract-data="currentContract"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getContractsAsSetter, archiveContract as archiveContractApi } from '@/api/contract'
import ContractFormDialog from './components/ContractFormDialog.vue'
import CancelContractDialog from './components/CancelContractDialog.vue'
import CancelRecordsDialog from './components/CancelRecordsDialog.vue'

// 响应式数据
const tableData = ref([])
const searchInfo = reactive({
  status: '',
  startDate: '',
  endDate: ''
})

// 对话框相关
const dialogVisible = ref(false)
const dialogMode = ref('create') // 'create' | 'edit'
const currentContract = ref(null)

const cancelDialogVisible = ref(false)
const recordsDialogVisible = ref(false)

// 方法
const getTableData = async () => {
  try {
    const res = await getContractsAsSetter(searchInfo)
    if (res.code === 0) {
      tableData.value = res.data || []
    }
  } catch (error) {
    ElMessage.error('获取合同列表失败')
  }
}

const resetSearch = () => {
  searchInfo.status = ''
  searchInfo.startDate = ''
  searchInfo.endDate = ''
  getTableData()
}

const openDialog = (mode, contract = null) => {
  dialogMode.value = mode
  currentContract.value = contract
  dialogVisible.value = true
}

const handleDialogSuccess = () => {
  dialogVisible.value = false
  getTableData()
}

const openCancelDialog = (contract) => {
  currentContract.value = contract
  cancelDialogVisible.value = true
}

const handleCancelSuccess = () => {
  cancelDialogVisible.value = false
  getTableData()
}

const viewDetail = (contract) => {
  // 跳转到详情页面
  // router.push(`/contract/detail/${contract.id}`)
}

const viewCancelRecords = (contract) => {
  currentContract.value = contract
  recordsDialogVisible.value = true
}

const archiveContract = async (contract) => {
  try {
    await ElMessageBox.confirm('确定要归档此合同吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const res = await archiveContractApi(contract.id)
    if (res.code === 0) {
      ElMessage.success('归档成功')
      getTableData()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('归档失败')
    }
  }
}

// 权限判断
const canEdit = (contract) => {
  return contract.status === 'Active' && contract.frozenQuantity === 0
}

const canArchive = (contract) => {
  return contract.status === 'Active' && contract.frozenQuantity === 0
}

const canCancel = (contract) => {
  return contract.status === 'Active' && contract.frozenQuantity === 0
}

// 工具方法
const getStatusType = (status) => {
  const typeMap = {
    'Active': 'success',
    'Pending': 'warning',
    'Completed': 'info',
    'Expired': 'info',
    'Cancelled': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status) => {
  const textMap = {
    'Active': '激活中',
    'Pending': '待激活',
    'Completed': '已完成',
    'Expired': '已过期',
    'Cancelled': '已取消'
  }
  return textMap[status] || status
}

const formatDate = (dateStr) => {
  return new Date(dateStr).toLocaleString()
}

// 生命周期
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.contract-setter-list {
  padding: 20px;
}

.frozen-info {
  color: #f56c6c;
  font-size: 12px;
}
</style>
