# 行情系统（@hq）重构详细设计方案 (v1.1 优化版)

**版本:** 1.1
**目标:** 基于对 `hq` 库现有代码的全面分析，优化原重构计划，制定一份集成现有资产、架构更优、健壮性更强的可执行方案。

---

## 第一阶段：核心依赖解耦与功能整合

**目标:** 完全移除对 MySQL 数据库的依赖，整合并利用现有代码，使行情系统能够独立、高效地运行。

### 步骤 1: Broker (期货公司) 配置本地化与智能优选

- **功能描述:**
  1.  **整合现有解析器**: 直接利用已存在的 `hq/broker_parser.py` 模块。系统启动时，由该模块负责解析 `hq/broker.xml` 文件，加载所有期货公司的服务器信息。
  2.  **保留并重构测速逻辑**: 将 `hq/engine/address.py` 中的 `ping` 和 `async_ping_addresses` 网络测速功能，迁移至独立的工具模块（如 `hq/utils/network_utils.py`）。
  3.  **实现服务器管理器 (`ServerManager`)**:
      -   新建 `hq/core/server_manager.py`。
      -   该管理器在启动时，调用 `broker_parser` 获取所有服务器地址，然后调用网络工具进行并发 `ping` 测速。
      -   根据测速结果（延迟最低且可用），动态选择最佳的主 `broker_id` 及其对应的行情服务器地址列表。
  4.  **配置注入**: `ServerManager` 将选定的最优服务器信息提供给行情引擎核心。
  5.  **代码清理**: 彻底移除 `hq/engine/address.py` 中所有基于 `pymysql` 的数据库查询代码。

- **涉及文件变更:**
  - **重构/迁移**:
    -   `hq/engine/address.py` -> `hq/utils/network_utils.py` (迁移并保留 `ping` 相关函数)。
    -   `hq/broker_parser.py`: 无需新增，直接使用。
  - **新增:** `hq/core/server_manager.py` - 实现服务器选择、测速和优选的核心逻辑。
  - **修改:** `hq/main.py` 和 `hq/engine/__init__.py` - 修改启动流程，调用 `ServerManager` 来获取行情地址，而不是直接调用旧的 `get_md_address`。
  - **删除:** 删除 `get_md_address` 函数及其所有数据库依赖。

- **测试方案:**
  1.  **单元测试**: 保持并完善 `hq/broker_test.py`，确保 XML 解析的健壮性。为 `network_utils.py` 编写单元测试，验证 `ping` 功能。
  2.  **集成测试**: 测试 `ServerManager`，验证它能否正确组合解析器和测速工具，并输出预期的最优服务器地址。
  3.  **端到端测试**: 启动主程序，确认系统在无数据库环境下，能通过 `ServerManager` 成功连接到最优行情服务器。
  Server是ctp的连接服务器，可以通过ping测试，是作为ctp连接行情的服务器地址。 

### 步骤 2: Instrument (合约) 信息获取 API 化与缓存

- **功能描述:**
  1.  **API 直连获取**: 在 `hq/utils/instrument_fetcher.py` 中，使用 `requests` 库直接请求 `http://dict.openctp.cn/prices?types=futures` 接口，获取包含所有期货合约信息的 JSON 数据。
  2.  **数据提取与标准化**: 解析返回的 JSON，提取 `data` 字段中的合约列表。将列表中的每个合约对象转换为与 `README.md` 中 `instruments` 表结构一致的 `dataclass` 或字典，重点提取 `InstrumentID` 和 `ExchangeID` 等关键字段。
  3.  **健壮的缓存机制**:
      -   在 `hq/utils/cache_manager.py` 中实现本地缓存读写。
      -   成功从 API 获取的合约列表，应立即序列化为 JSON 格式并覆盖本地缓存文件 `@hq/cache/instruments.json`。
      -   系统启动时，**优先加载本地缓存**，然后异步尝试从 API 更新。如果更新成功，则刷新内存中的合约信息；如果失败（如网络中断、API不通），则使用已加载的缓存数据继续运行，并记录错误日志。此策略可确保在网络故障时系统依然可以启动。
  4.  **代码清理**: 彻底移除 `hq/engine/instrument.py` 及其所有数据库查询代码，并从 `requirements.txt` 中移除 `PyMySQL`。

- **涉及文件变更:**
  - **新增/修改:**
    -   `hq/utils/instrument_fetcher.py` - 负责从指定 API 获取和解析合约数据。
    -   `hq/utils/cache_manager.py` - 负责合约数据的本地缓存读写。
  - **修改:** `hq/engine/__init__.py` (或重构后的 `hq/core/engine.py`) - 修改合约加载逻辑，集成新的 API 获取和缓存机制。
  - **删除:** `hq/engine/instrument.py`。

- **测试方案:**
  1.  **单元测试**: 测试 `instrument_fetcher.py` 的 API 请求和 JSON 解析逻辑（可使用 mock API 响应）。测试 `cache_manager.py` 的读写功能。
  2.  **集成测试**: 运行 `instrument_fetcher.py`，验证能否成功从 `openctp` API 获取数据并生成格式正确的 `instruments.json` 缓存文件。
  3.  **异常测试**: 模拟网络中断或 API 返回错误码，验证系统能否平滑地加载并使用本地缓存启动。

### 步骤 3: 最小化集成测试方案

**目标:** 验证第一阶段重构后的核心功能（Broker服务器优选 和 合约信息获取）能够无缝集成，并替换原有功能，确保系统在无数据库依赖下能正常启动并连接行情。

**测试脚本:** `hq/test_step1_integration.py` (新增)

**测试逻辑:**

1.  **初始化环境:**
    *   确保 `hq/broker.xml` 和 `hq/cache/instruments.json` (若存在) 已准备好。
    *   确保网络连接正常，以便进行 ping 测试和 API 访问。

2.  **Broker 服务器优选测试:**
    *   **目的:** 验证 `ServerManager` 能否成功加载、测速并选出最优服务器。
    *   **步骤:**
        1.  实例化 `ServerManager`。
        2.  调用其初始化方法（例如 `manager.initialize()`）。
        3.  断言 `ServerManager` 返回的 `best_server` 不是 `None`。
        4.  打印选出的最优服务器信息 (IP, Port, BrokerID)，以便人工核对。
        5.  **日志验证:** 检查日志输出，确认 `ping` 测速过程被记录，且包含了延迟信息。

3.  **合约信息获取测试:**
    *   **目的:** 验证 `InstrumentManager` (或新的合约服务) 能否通过 API 或缓存成功加载合约。
    *   **步骤:**
        1.  实例化 `InstrumentManager`。
        2.  调用其加载方法（例如 `instrument_manager.load_instruments()`）。
        3.  断言加载的合约列表 `instruments` 不为空。
        4.  打印加载的合约总数和第一个合约作为示例。
        5.  **缓存验证:** 检查 `hq/cache/instruments.json` 文件是否被成功创建或更新。

4.  **核心引擎集成测试:**
    *   **目的:** 模拟 `main.py` 的启动流程，将重构后的模块组合起来，验证系统能否完整地执行连接流程。
    *   **步骤:**
        1.  **依赖注入:**
            *   创建 `ServerManager` 和 `InstrumentManager` 的实例。
            *   将 `ServerManager` 选出的最优服务器地址和 `InstrumentManager` 获取的合约列表，传递给核心引擎 (`CtpMdApi` 或其封装器 `VnpyAdapter`)。
        2.  **模拟连接:**
            *   调用核心引擎的 `connect` 方法，并传入必要的参数 (user_id, password, broker_id, address)。
            *   **断言:** 在短时间内（如 5 秒），引擎的连接状态应变为“已连接”。
            *   **日志验证:** 检查日志，确认 CTP 登录成功的信息。
        3.  **模拟订阅:**
            *   从已加载的合约列表中，选择一个或多个合约（如 `rb2410`）。
            *   调用核心引擎的 `subscribe` 方法。
            *   **断言:** 检查日志或回调，确认订阅请求已发送。
        4.  **清理:** 调用 `close` 方法断开连接，释放资源。

5.  **异常场景测试 (可选，但建议):**
    *   **无效 Broker 配置:** 测试 `broker.xml` 文件不存在或格式错误时，系统能否优雅失败并记录错误。
    *   **网络中断:** 模拟 API 或行情服务器无法访问，验证系统能否：
        *   在使用缓存合约的情况下启动。
        *   在服务器测速失败时，记录错误并终止。

**预期成果:**

*   一个独立的 `test_step1_integration.py` 脚本，执行该脚本可以完整地验证第一阶段重构的成果。
*   测试输出清晰，能明确展示每个环节的成功或失败。
*   该测试为第二阶段的架构升级提供一个可靠的回归测试基准。


---

  ## 第二阶段：系统架构升级与健壮性增强

**目标:** 在已完成数据库解耦的基础上，引入更现代化的分层软件架构，全面提升代码的可维护性、扩展性和运行稳定性。

### 步骤 3: 实施分层架构 (Architecture Refactoring)

**现状:** `core` 和 `utils` 目录已初步建立，但核心业务逻辑、外部服务交互和基础设施代码仍有耦合。

**功能描述:**

1.  **目录结构最终确定**:
    *   `hq/core/`: 存放核心业务逻辑。
        *   `engine.py`: **新建文件**。将 `hq/engine/__init__.py` 中的 `Engine` 类移入此文件，并重构为核心业务流程的编排器。它将不再直接初始化 `CtpMdApi`，而是接收一个适配器实例。
        *   `server_manager.py`: **保留**，作为服务器选择和管理的核心服务。
        *   `instrument_manager.py`: **保留**，作为合约信息管理的核心服务。
    *   `hq/adapters/`: **新建目录**，存放所有与外部服务的接口适配器。
        *   `vnpy_adapter.py`: **新建文件**。封装 `vnpy_ctp` 的 `MdApi`，负责与 CTP 行情接口的实际交互（连接、订阅、断开等）。它将包含 `CtpMdApi` 的大部分逻辑，并增加更强的错误处理和状态管理。
        *   `redis_adapter.py`: **新建文件**。封装所有 `redis` 操作（如 `setex`, `publish`），提供清晰的接口供上层调用。
    *   `hq/infrastructure/`: **新建目录**，存放应用的基础设施代码。
        *   `logging.py`: **新建文件**。配置全局结构化日志系统（推荐使用 `loguru`）。
        *   `config.py`: **重构** `hq/config/__init__.py`，迁移至此目录，并考虑升级为 `Pydantic-Settings` 以实现类型安全的配置管理。
    *   `hq/utils/`: **保留**，存放无状态的通用工具函数，如 `network_utils.py`。

2.  **实现依赖注入 (Dependency Injection)**:
    *   重构后的核心 `Engine` (`hq/core/engine.py`) 应通过构造函数接收 `VnpyAdapter`、`RedisAdapter`、`ServerManager` 和 `InstrumentManager` 的实例。
    *   `main.py` 将负责初始化所有组件，并将它们注入到 `Engine` 中。这种方式实现了控制反转 (IoC)，极大地提高了代码的可测试性和灵活性。

**涉及文件变更:**

*   **大量重命名/移动**: 将 `hq/engine/__init__.py` 的逻辑拆分到 `hq/core/engine.py` 和 `hq/adapters/vnpy_adapter.py`。将 `hq/config/__init__.py` 移动到 `hq/infrastructure/config.py`。
*   **重构**:
    *   `hq/main.py`: 更新启动逻辑，按照新的分层关系初始化并注入依赖。
    *   `hq/engine/md.py`: 其功能被 `hq/adapters/vnpy_adapter.py` 替代。
*   **删除**: `hq/engine/` 目录在重构完成后应被删除。

**测试方案:**

1.  **分层测试**:
    *   为 `adapters` 层的每个适配器（如 `VnpyAdapter`）编写独立的集成测试，验证其与外部服务（CTP, Redis）的交互。
    *   为 `core` 层的业务逻辑（如 `Engine`）编写单元测试，可以 mock 掉适配器层，专注于业务流程的正确性。
2.  **回归测试**: 重构完成后，再次执行 `test_step1_integration.py` 和 `test_step2_integration.py`，确保核心功能无回归缺陷。

### 步骤 4: 全面的健壮性增强 (Robustness Enhancement)

**目标:** 建立一个能够自我监控、自动恢复的行情系统。

**功能描述:**

1.  **连接与故障恢复**:
    *   在 `VnpyAdapter` (`hq/adapters/vnpy_adapter.py`) 中实现**自动重连**机制。当 CTP 连接因网络波动等原因断开时，适配器应能按预设策略（如指数退避算法）自动尝试重新连接。
    *   在 `ServerManager` (`hq/core/server_manager.py`) 中实现**健康检查与故障切换**。
        *   `ServerManager` 定期（如每5分钟）调用 `health_check()` 方法，监控当前连接质量。
        *   如果健康检查失败（如延迟过高或连接丢失），并且 `VnpyAdapter` 的自动重连也失败，`ServerManager` 应立即触发新一轮的全量服务器测速。
        *   测速完成后，如果选出的最优服务器与当前不同，`ServerManager` 将命令 `VnpyAdapter` 断开旧连接，并使用新的最优地址重新连接。

2.  **结构化日志系统**:
    *   在 `hq/infrastructure/logging.py` 中，使用 `loguru` 配置全局日志系统。
    *   实现日志的自动轮转（按天或按大小）、格式化输出（包含时间戳、级别、模块名），并能同时输出到控制台和文件。
    *   **全局替换**: 使用配置好的 `logger` 对象，全面替换项目中的所有 `print()` 语句，确保所有输出都可被管理和追溯。

3.  **配置系统升级**:
    *   在 `hq/infrastructure/config.py` 中，使用 `Pydantic-Settings` 替代 `configparser`。
    *   **优势**:
        *   **类型安全**: 自动进行类型校验和转换。
        *   **默认值管理**: 为配置项提供清晰的默认值。
        *   **多源加载**: 支持从 `.ini` 文件、环境变量、`.env` 文件等多种来源加载配置，并支持优先级覆盖。
        *   **代码清晰**: 配置项以类属性形式定义，结构清晰，易于维护。

**涉及文件变更:**

*   **修改**:
    *   `hq/adapters/vnpy_adapter.py`: 增加自动重连逻辑。
    *   `hq/core/server_manager.py`: 增加健康检查和故障切换的触发与执行逻辑。
    *   `hq/infrastructure/config.py`: 使用 `Pydantic-Settings` 重写配置加载和管理。
*   **新增**: `hq/infrastructure/logging.py`。
*   **全局修改**: 将所有 `print()` 替换为 `logger` 调用。

**测试方案:**

1.  **断线与恢复测试**:
    *   在系统运行时，手动模拟网络断开，验证 `VnpyAdapter` 的自动重连功能。
    *   长时间断开网络，验证 `ServerManager` 是否能成功触发故障切换，并连接到备用服务器。
2.  **配置加载测试**: 验证系统能否从不同的来源（如 `.ini` 文件和环境变量）正确加载配置，并验证优先级是否符合预期。
3.  **日志审计**: 在各种正常和异常场景下，检查日志文件是否记录了预期的、格式正确的信息，并且没有 `print` 输出。