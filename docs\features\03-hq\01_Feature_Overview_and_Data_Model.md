# 功能模块：行情数据采集

## 1. 功能模块简介 (Summary)

本功能负责从期货市场实时获取全市场的行情数据，并将其持久化存储，为其他业务功能（如 **[基础数据看板](../02-basic-data-dashboard/01_Feature_Overview_and_Data_Model.md)**）提供稳定、可靠的数据支持。

## 2. 数据定义 (Data Definition)

### 2.1 数据来源

- **上游**: 期货公司的行情网关接口 (e.g., CTP)。
- **下游**: 系统内其他业务模块，如数据看板、策略回测、风险管理等。

### 2.2 核心技术栈

- **数据采集框架**: `vn.py` - 一个成熟的开源量化交易框架，具备强大的行情订阅和处理能力。
- **时序数据库**: `TDengine` - 用于高效存储和查询海量的时序数据，如 Tick、分钟线和日线数据。
- **实时缓存数据库**: `Redis` - 用于存储最新的 Tick 数据，旨在为前端提供低延迟的实时数据推送。

### 2.3 数据模型

本功能模块产出的核心数据模型与 **[基础数据看板](../02-basic-data-dashboard/01_Feature_Overview_and_Data_Model.md)** 中定义的数据模型一致。主要包括 `market_data_daily`（日线）、`market_data_1min`（分钟线）和 `market_data_tick`（Tick）等数据表。
