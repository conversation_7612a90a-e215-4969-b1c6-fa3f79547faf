"""
登录表单组件

处理用户输入，管理登录表单。
"""

import os
import json
import logging
from typing import Optional

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QPushButton, QCheckBox, QLabel, QTabWidget, QSizePolicy
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QPixmap

from auth.models import LoginCredentials
from auth.utils.validators import PhoneValidator, PasswordValidator, UsernameValidator


class LoginWidget(QWidget):
    """登录表单组件
    
    处理用户输入，管理登录表单，包括：
    - 双模式表单（密码/手机验证码）
    - 输入验证和格式化
    - 凭据数据提取
    - 配置保存和加载
    """
    
    # 信号定义
    credentials_changed = Signal()
    send_code_requested = Signal(str)  # 发送验证码请求信号
    refresh_captcha_requested = Signal()  # 刷新验证码请求信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        
        # 验证器
        self.phone_validator = PhoneValidator()
        self.password_validator = PasswordValidator()
        self.username_validator = UsernameValidator()
        
        self.setup_ui()
        self.load_saved_credentials()
        
        # 使用Qt默认样式，不需要设置对象名称
    
    def setup_ui(self):
        """设置登录表单UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)  # 减少主布局间距
        layout.setContentsMargins(0, 0, 0, 0)

        # 创建Tab切换组件
        self.tab_widget = QTabWidget()
        self.tab_widget.setMinimumHeight(250)  # 确保Tab组件有足够高度显示内容

        # 设置Tab组件的大小策略，确保它能够扩展
        self.tab_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 用户名密码登录页面
        self.password_tab = self.create_password_tab()
        self.tab_widget.addTab(self.password_tab, "用户名密码")

        # 手机验证码登录页面
        self.phone_tab = self.create_phone_tab()
        self.tab_widget.addTab(self.phone_tab, "手机验证码")

        # 选项区域
        options_widget = self.create_options_widget()

        # 添加到主布局
        layout.addWidget(self.tab_widget, 1)  # 给Tab组件设置stretch factor为1，确保它获得主要空间
        layout.addWidget(options_widget, 0)   # 选项区域不拉伸

        # 连接Tab切换信号
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
    
    def create_password_tab(self) -> QWidget:
        """创建密码登录Tab页面"""
        widget = QWidget()
        layout = QFormLayout(widget)
        layout.setSpacing(10)  # 进一步减少表单内部间距
        layout.setContentsMargins(20, 10, 20, 10)  # 进一步减少上下内边距
        layout.setLabelAlignment(Qt.AlignVCenter | Qt.AlignRight)  # 标签垂直居中且右对齐
        layout.setFormAlignment(Qt.AlignLeft | Qt.AlignVCenter)  # 表单整体对齐
        layout.setRowWrapPolicy(QFormLayout.DontWrapRows)  # 不换行
        layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)  # 字段扩展策略

        # 用户名输入
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入用户名")
        # 使用Qt默认样式
        self.username_edit.textChanged.connect(lambda: self.credentials_changed.emit())

        # 密码输入容器
        password_container = QWidget()
        password_layout = QHBoxLayout(password_container)
        password_layout.setContentsMargins(0, 0, 0, 0)
        password_layout.setSpacing(5)

        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("请输入密码")
        self.password_edit.setEchoMode(QLineEdit.Password)
        # 使用Qt默认样式
        self.password_edit.textChanged.connect(lambda: self.credentials_changed.emit())

        # 显示/隐藏密码按钮
        self.show_password_btn = QPushButton("👁")
        # 使用Qt默认样式
        self.show_password_btn.setCheckable(True)
        self.show_password_btn.clicked.connect(self.toggle_password_visibility)

        password_layout.addWidget(self.password_edit)
        password_layout.addWidget(self.show_password_btn)

        # 验证码输入容器
        captcha_container = QWidget()
        captcha_layout = QHBoxLayout(captcha_container)
        captcha_layout.setContentsMargins(0, 0, 0, 0)
        captcha_layout.setSpacing(10)

        self.captcha_edit = QLineEdit()
        self.captcha_edit.setPlaceholderText("请输入验证码")
        # 使用Qt默认样式
        self.captcha_edit.textChanged.connect(lambda: self.credentials_changed.emit())

        # 验证码图片
        self.captcha_label = QLabel()
        self.captcha_label.setFixedSize(100, 30)
        # 使用Qt默认样式
        self.captcha_label.setText("验证码")
        self.captcha_label.mousePressEvent = lambda event: self.refresh_captcha_requested.emit()

        captcha_layout.addWidget(self.captcha_edit)
        captcha_layout.addWidget(self.captcha_label)

        # 添加到表单布局
        layout.addRow("用户名:", self.username_edit)
        layout.addRow("密码:", password_container)
        layout.addRow("验证码:", captcha_container)

        # 确保Tab内容有最小高度和正确的大小策略
        widget.setMinimumHeight(200)
        widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        return widget
    
    def create_phone_tab(self) -> QWidget:
        """创建手机验证码Tab页面"""
        widget = QWidget()
        layout = QFormLayout(widget)
        layout.setSpacing(10)  # 进一步减少表单内部间距
        layout.setContentsMargins(20, 10, 20, 10)  # 进一步减少上下内边距
        layout.setLabelAlignment(Qt.AlignVCenter | Qt.AlignRight)  # 标签垂直居中且右对齐
        layout.setFormAlignment(Qt.AlignLeft | Qt.AlignVCenter)  # 表单整体对齐
        layout.setRowWrapPolicy(QFormLayout.DontWrapRows)  # 不换行
        layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)  # 字段扩展策略

        # 手机号输入
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("请输入手机号")
        # 使用Qt默认样式
        self.phone_edit.textChanged.connect(lambda: self.credentials_changed.emit())

        # 验证码输入容器
        code_container = QWidget()
        code_layout = QHBoxLayout(code_container)
        code_layout.setContentsMargins(0, 0, 0, 0)
        code_layout.setSpacing(10)

        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("请输入验证码")
        # 使用Qt默认样式
        self.code_edit.textChanged.connect(lambda: self.credentials_changed.emit())

        self.send_code_button = QPushButton("获取验证码")
        self.send_code_button.setFixedWidth(100)
        # 使用Qt默认样式
        self.send_code_button.clicked.connect(self.on_send_code_clicked)

        code_layout.addWidget(self.code_edit)
        code_layout.addWidget(self.send_code_button)

        # 添加到表单布局
        layout.addRow("手机号:", self.phone_edit)
        layout.addRow("验证码:", code_container)

        # 确保Tab内容有最小高度和正确的大小策略
        widget.setMinimumHeight(150)
        widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        return widget
    
    def on_tab_changed(self, index):
        """处理Tab切换"""
        self.credentials_changed.emit()

        # 设置焦点到相应的输入框
        if index == 0:  # 密码登录
            self.username_edit.setFocus()
        elif index == 1:  # 手机登录
            self.phone_edit.setFocus()
    
    def create_options_widget(self) -> QWidget:
        """创建选项区域"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        self.remember_checkbox = QCheckBox("记住密码")
        self.auto_login_checkbox = QCheckBox("自动登录")
        
        # 连接信号
        self.remember_checkbox.toggled.connect(lambda: self.credentials_changed.emit())
        self.auto_login_checkbox.toggled.connect(lambda: self.credentials_changed.emit())
        
        layout.addWidget(self.remember_checkbox)
        layout.addWidget(self.auto_login_checkbox)
        layout.addStretch()
        
        return widget
    
    def get_current_login_type(self) -> str:
        """获取当前登录类型"""
        return "password" if self.tab_widget.currentIndex() == 0 else "phone"
    
    def toggle_password_visibility(self):
        """切换密码显示/隐藏"""
        if self.show_password_btn.isChecked():
            self.password_edit.setEchoMode(QLineEdit.Normal)
            self.show_password_btn.setText("🙈")
        else:
            self.password_edit.setEchoMode(QLineEdit.Password)
            self.show_password_btn.setText("👁")
    
    def on_send_code_clicked(self):
        """处理发送验证码按钮点击"""
        phone = self.phone_edit.text().strip()
        if phone:
            self.send_code_requested.emit(phone)
    
    def get_login_credentials(self) -> LoginCredentials:
        """获取登录凭据"""
        if self.get_current_login_type() == "password":
            return LoginCredentials(
                login_type="password",
                username=self.username_edit.text().strip(),
                password=self.password_edit.text(),
                captcha=self.captcha_edit.text().strip(),
                remember_me=self.remember_checkbox.isChecked(),
                auto_login=self.auto_login_checkbox.isChecked()
            )
        else:
            return LoginCredentials(
                login_type="phone",
                phone=self.phone_edit.text().strip(),
                sms_code=self.code_edit.text().strip(),
                remember_me=self.remember_checkbox.isChecked(),
                auto_login=self.auto_login_checkbox.isChecked()
            )
    
    def validate_current_input(self) -> Optional[str]:
        """验证当前输入

        Returns:
            Optional[str]: 错误消息，None表示验证通过
        """
        if self.get_current_login_type() == "password":
            # 验证用户名密码
            username = self.username_edit.text().strip()
            password = self.password_edit.text()
            captcha = self.captcha_edit.text().strip()

            if not username:
                return "请输入用户名"

            if not self.username_validator.validate(username):
                return self.username_validator.get_error_message()

            if not password:
                return "请输入密码"

            if not self.password_validator.validate(password):
                return self.password_validator.get_error_message()

            if not captcha:
                return "请输入验证码"

            if len(captcha) != 4:
                return "验证码应为4位字符"
        else:
            # 验证手机号验证码
            phone = self.phone_edit.text().strip()
            code = self.code_edit.text().strip()

            if not phone:
                return "请输入手机号"

            if not self.phone_validator.validate(phone):
                return self.phone_validator.get_error_message()

            if not code:
                return "请输入验证码"

            if len(code) != 6 or not code.isdigit():
                return "验证码应为6位数字"

        return None
    
    def load_saved_credentials(self):
        """加载保存的登录凭据"""
        try:
            config_file = "login_config.json"
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                self.username_edit.setText(config.get("username", ""))
                self.phone_edit.setText(config.get("phone", ""))
                self.remember_checkbox.setChecked(config.get("remember_me", False))
                self.auto_login_checkbox.setChecked(config.get("auto_login", False))
                
                self.logger.info("登录配置加载成功")
                
        except Exception as e:
            self.logger.error(f"加载登录配置失败: {str(e)}")
    
    def save_credentials(self, credentials: LoginCredentials):
        """保存登录凭据"""
        try:
            if credentials.remember_me:
                config = {
                    "username": credentials.username or "",
                    "phone": credentials.phone or "",
                    "remember_me": credentials.remember_me,
                    "auto_login": credentials.auto_login
                }
                
                with open("login_config.json", 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
                
                self.logger.info("登录配置保存成功")
            else:
                # 如果不记住密码，清除配置文件
                if os.path.exists("login_config.json"):
                    os.remove("login_config.json")
                    self.logger.info("登录配置已清除")
                    
        except Exception as e:
            self.logger.error(f"保存登录配置失败: {str(e)}")
    
    def clear_form(self):
        """清空表单"""
        self.username_edit.clear()
        self.password_edit.clear()
        self.phone_edit.clear()
        self.code_edit.clear()
        self.remember_checkbox.setChecked(False)
        self.auto_login_checkbox.setChecked(False)
    
    def set_send_code_button_state(self, enabled: bool, text: str = "获取验证码"):
        """设置发送验证码按钮状态"""
        self.send_code_button.setEnabled(enabled)
        self.send_code_button.setText(text)

    def set_captcha_image(self, image_data: bytes):
        """设置验证码图片"""
        pixmap = QPixmap()
        if pixmap.loadFromData(image_data):
            self.captcha_label.setPixmap(pixmap.scaled(100, 35, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        else:
            self.captcha_label.setText("验证码")

    def clear_captcha(self):
        """清空验证码"""
        self.captcha_edit.clear()
        self.captcha_label.clear()
        self.captcha_label.setText("验证码")
