# ContractSummary 组件

一个可复用的合同汇总视图组件，支持不同用户角色的合同数据展示和分组汇总。

## 功能特点

- 📊 **多角色支持**: 支持pricer和setter两种用户角色
- 🔄 **智能分组**: 根据用户角色自动选择分组方式
- 📱 **响应式设计**: 适配移动端，支持展开/折叠交互
- 🎯 **可选点击**: 支持启用/禁用点击跳转功能
- 📈 **完整统计**: 显示总体统计和详细的合同类型信息

## Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| contracts | IContract[] | 是 | - | 合同列表数据 |
| userRole | 'pricer' \| 'setter' | 是 | - | 用户角色，决定分组方式 |
| enableClick | boolean | 否 | false | 是否启用点击功能 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| instrumentClick | { instrumentInfo, userInfo } | 点击期货合约时触发（需启用点击） |

## 分组逻辑

### Pricer角色 (userRole="pricer")
- **分组方式**: 按setter分组
- **显示标签**: "合作伙伴"
- **数据来源**: 从合同的setter信息中获取用户名和ID

### Setter角色 (userRole="setter")  
- **分组方式**: 按pricer分组
- **显示标签**: "点价方"
- **数据来源**: 从合同的pricer信息中获取用户名和ID

## 使用示例

### Pricer视图（支持点击跳转）
```vue
<template>
  <ContractSummary
    :contracts="summaryContractList"
    user-role="pricer"
    :enable-click="true"
    @instrument-click="handleInstrumentClick"
  />
</template>

<script setup>
import ContractSummary from '@/components/ContractSummary.vue'

// 处理期货合约点击事件
function handleInstrumentClick(data) {
  const { instrumentInfo, userInfo } = data
  uni.navigateTo({
    url: `/pages/trade/execute?instrumentId=${instrumentInfo.instrumentId}&setterID=${userInfo.userID}`
  })
}
</script>
```

### Setter视图（仅展示，不支持点击）
```vue
<template>
  <ContractSummary
    :contracts="contractList"
    user-role="setter"
    :enable-click="false"
  />
</template>

<script setup>
import ContractSummary from '@/components/ContractSummary.vue'
</script>
```

## 数据结构

### 输入数据
组件接收标准的IContract[]数组，自动进行分组和汇总计算。

### 内部数据结构

#### ContractTypeInfo
```typescript
interface ContractTypeInfo {
  quantity: number          // 剩余数量
  frozenQuantity: number    // 冻结数量
  availableQuantity: number // 可用数量
  avgPrice: number          // 平均价格
  contractCount: number     // 合同数量
}
```

#### InstrumentInfo
```typescript
interface InstrumentInfo {
  instrumentId: number
  instrumentName: string
  basis: ContractTypeInfo      // 基差合同信息
  fixed: ContractTypeInfo      // 固定价合同信息
  totalQuantity: number        // 总剩余数量
  totalFrozen: number          // 总冻结数量
  totalAvailable: number       // 总可用数量
  contracts: IContract[]       // 原始合同列表
}
```

#### UserInfo
```typescript
interface UserInfo {
  userID: number
  userName: string
  instruments: { [instrumentName: string]: InstrumentInfo }
  totalContracts: number       // 总合同数
  totalQuantity: number        // 总剩余数量
  totalAvailable: number       // 总可用数量
}
```

## 界面布局

### 总体统计卡片
- 合作伙伴/点价方数量
- 总合同数
- 总可用数量

### 用户卡片
- **头部**: 用户名、统计信息、展开/折叠图标
- **详情**: 期货合约列表（可展开）

### 期货合约卡片
- **头部**: 合约名称、汇总信息（同一行显示）
- **详情**: 基差合同和固定价合同的详细统计

### 合同类型详情
- 剩余数量、冻结数量、可用数量
- 平均价格（基差/固定价）
- 合同数量

## 样式特点

- **现代卡片设计**: 圆角、阴影、渐变背景
- **交互动效**: 展开/折叠动画、hover效果
- **颜色系统**: 基差合同蓝色主题，固定价合同橙色主题
- **状态标识**: 冻结数量红色警告，可用数量绿色显示
- **响应式布局**: 适配不同屏幕尺寸

## 重构优势

### 代码复用
- 一个组件支持两种用户角色
- 统一的UI设计和交互逻辑
- 减少重复代码，提高维护性

### 灵活配置
- 通过props控制分组方式
- 可选的点击功能
- 事件驱动的交互模式

### 性能优化
- 计算属性自动缓存
- 响应式数据更新
- 高效的DOM渲染

## 迁移指南

### 从pricer-list.vue迁移
1. 导入ContractSummary组件
2. 替换汇总视图模板
3. 添加handleInstrumentClick事件处理
4. 删除不再需要的代码和样式

### 在setter-list.vue中使用
1. 导入ContractSummary组件
2. 设置user-role="setter"
3. 设置enable-click="false"
4. 传入合同数据

这个组件提供了一个统一、灵活、高性能的合同汇总解决方案，可以在不同的页面中复用，大大提高了开发效率和用户体验。
