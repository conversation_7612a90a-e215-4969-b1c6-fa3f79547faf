# 点价系统 UI 设计规范与提示词指南

## 📋 概述

本文档基于登录页面的高级设计特点，提供了一套完整的UI设计规范和提示词模板，确保所有页面都能达到统一的高级感和现代化视觉效果。

## 🎨 设计系统规范

### 1. 核心设计变量

```scss
// 主色调系统
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$primary-dark: #764ba2;

// 背景系统
$bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$bg-light: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
$bg-card: rgba(255, 255, 255, 0.95);

// 圆角系统
$radius-sm: 8rpx;
$radius-md: 12rpx;
$radius-lg: 20rpx;
$radius-xl: 44rpx;

// 阴影系统
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
$shadow-focus: 0 0 0 4rpx rgba(102, 126, 234, 0.1);

// 间距系统
$spacing-xs: 12rpx;
$spacing-sm: 20rpx;
$spacing-md: 36rpx;
$spacing-lg: 48rpx;
$spacing-xl: 60rpx;

// 颜色系统
$text-primary: #333;
$text-secondary: #606266;
$text-tertiary: #909399;
$border-color: #e4e7ed;
$success-color: #4cd964;
$error-color: #dd524d;
```

### 2. 核心设计原则

- **渐变背景**：使用蓝紫色渐变营造现代感和科技感
- **毛玻璃效果**：`backdrop-filter: blur(10rpx)` 增加视觉层次
- **圆角设计**：统一使用20rpx大圆角提升现代感
- **精致阴影**：多层次阴影系统增强立体感
- **动画过渡**：0.3s ease 过渡动画提升交互体验
- **品牌色系**：统一的蓝紫色主题保持视觉一致性

## 📝 通用页面提示词模板

### 基础页面改造提示词

```
请按照以下高级UI设计规范重构这个页面，确保与登录页面保持统一的视觉风格：

【背景设计】
- 页面主背景：使用渐变色 linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) 或登录页渐变 linear-gradient(135deg, #667eea 0%, #764ba2 100%)
- 卡片背景：使用半透明白色 rgba(255, 255, 255, 0.95) 配合毛玻璃效果 backdrop-filter: blur(10rpx)

【布局容器】
- 所有内容卡片使用 20rpx 圆角
- 添加精致阴影：0 8rpx 32rpx rgba(0, 0, 0, 0.12)
- 卡片间距：36rpx
- 内边距：40rpx-60rpx

【交互元素】
- 按钮：使用渐变背景 linear-gradient(135deg, #667eea 0%, #764ba2 100%)，圆角 44rpx，高度 88rpx
- 输入框：圆角 12rpx，聚焦时添加蓝色边框 #667eea 和阴影效果 0 0 0 4rpx rgba(102, 126, 234, 0.1)
- 标签/筛选项：圆角 20rpx，激活状态使用主色调渐变

【动画效果】
- 添加 0.3s ease 过渡动画
- 悬停状态：轻微上移 translateY(-2rpx) 和阴影增强
- 页面切换使用 fadeIn 动画

【色彩搭配】
- 主色调：#667eea 到 #764ba2 的渐变
- 文字颜色：#333（主要）、#606266（次要）、#909399（辅助）
- 边框颜色：#e4e7ed
- 成功色：#4cd964，错误色：#dd524d

【字体层级】
- 标题：32rpx，font-weight: 600
- 正文：28rpx-30rpx
- 辅助文字：24rpx-26rpx

【wot-design-uni 组件样式】
- 使用 custom-class 属性应用自定义样式
- 保持组件功能完整性的同时提升视觉效果
```

## 🎯 专用页面提示词

### 列表页面专用提示词

```
【列表页面特殊要求】
- 搜索栏：白色半透明背景，毛玻璃效果，添加阴影，圆角设计
- 搜索输入框：使用 custom-class="search-input" 应用聚焦样式
- 筛选按钮：添加激活状态的红点提示，使用脉冲动画
- 列表项：卡片式设计，悬停效果 translateY(-4rpx)
- 空状态：居中设计，卡片包装，配图+文字+渐变按钮
- 加载状态：使用品牌色的加载动画

【wot-design-uni 组件定制】
- wd-input: custom-class="search-input"
- wd-button: custom-class="primary-button" 
- wd-tag: custom-class="filter-tag-item"
- wd-loading: custom-class="loading-spinner"

【响应式设计】
- 确保在不同屏幕尺寸下的适配
- 使用 rpx 单位保证跨端一致性
- 小屏幕下调整间距和字体大小
```

### 表单页面专用提示词

```
【表单页面特殊要求】
- 表单容器：白色半透明背景，毛玻璃效果，20rpx圆角
- 输入框组：统一的标签样式，聚焦状态反馈
- 按钮组：主按钮使用渐变，次要按钮使用边框样式
- 验证反馈：使用品牌色系的成功/错误状态
- 步骤指示：如有多步骤，使用现代化的进度指示器

【wot-design-uni 表单组件】
- wd-input: custom-class="form-input"
- wd-button[type="primary"]: custom-class="primary-button"
- wd-button[type="default"]: custom-class="secondary-button"
```

### 详情页面专用提示词

```
【详情页面特殊要求】
- 头部信息：使用渐变背景卡片
- 内容区域：分块卡片设计，每块使用毛玻璃效果
- 操作按钮：底部固定，使用渐变背景
- 标签展示：使用品牌色渐变标签
- 数据展示：重要数据使用大字体和品牌色
```

## 🛠️ 实施指南

### 1. 全局样式文件创建

创建 `styles/design-system.scss` 文件：

```scss
// 设计系统 Mixin
@mixin card-style {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  transition: all 0.3s ease;
}

@mixin button-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 44rpx;
  height: 88rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
  }
}

@mixin input-style {
  border: 2rpx solid #e4e7ed;
  border-radius: 12rpx;
  transition: all 0.3s ease;

  &:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
  }
}

@mixin hover-lift {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  }
}
```

### 2. 主题配置更新

更新 `layouts/default.vue` 中的主题配置：

```typescript
const themeVars: ConfigProviderThemeVars = {
  colorTheme: '#667eea',
  buttonPrimaryBgColor: '#667eea',
  buttonPrimaryColor: '#ffffff',
  // 添加更多主题变量
}
```

### 3. 组件化常用样式

创建通用组件 `components/ui/GradientCard.vue`：

```vue
<template>
  <view class="gradient-card">
    <slot />
  </view>
</template>

<style lang="scss" scoped>
.gradient-card {
  @include card-style;
  padding: 40rpx;
  margin-bottom: 36rpx;
}
</style>
```

## 📋 检查清单

使用此设计系统时，请确保：

- [ ] 页面背景使用指定的渐变色
- [ ] 所有卡片使用毛玻璃效果和统一圆角
- [ ] 按钮使用渐变背景和悬停效果
- [ ] 输入框有聚焦状态反馈
- [ ] 动画过渡时间统一为 0.3s ease
- [ ] wot-design-uni 组件使用 custom-class 定制样式
- [ ] 颜色使用统一的品牌色系
- [ ] 字体大小符合层级规范
- [ ] 响应式设计适配不同屏幕

## 🎯 具体使用示例

### 改造现有页面的完整提示词

```
请使用点价系统UI设计规范重构 [页面名称] 页面：

1. 将页面背景改为浅灰渐变：background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)
2. 主要内容区域使用卡片样式：白色半透明背景 + 毛玻璃效果 + 20rpx圆角 + 精致阴影
3. 所有按钮使用渐变背景：linear-gradient(135deg, #667eea 0%, #764ba2 100%)
4. 输入框添加聚焦状态：边框变蓝 + 阴影效果
5. 列表项/卡片添加悬停动画：translateY(-4rpx) + 阴影增强
6. 使用 wot-design-uni 的 custom-class 属性应用自定义样式
7. 所有交互元素添加 0.3s ease 过渡动画
8. 确保颜色搭配使用统一的品牌色系

请保持原有功能不变，只升级视觉效果，确保与登录页面保持一致的高级感。
```

## 📚 参考资源

- 登录页面设计：`app/src/pages/login/index.vue`
- 改造示例：`app/src/pages/quotes/marketplace.vue`
- wot-design-uni 文档：[官方文档链接]
- UnoCSS 配置：`app/uno.config.ts`

---

**版本**: v1.0
**更新日期**: 2024-12-19
**维护者**: 开发团队
