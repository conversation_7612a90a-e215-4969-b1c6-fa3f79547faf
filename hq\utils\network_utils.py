"""
网络工具模块
从 engine/address.py 中提取并优化的网络测试功能
"""
import socket
import time
import concurrent.futures
from typing import List, Dict, Tuple, Optional


def ping(address: str, port: int) -> Tuple[str, int, float]:
    """测试单个地址的延迟，返回包含地址和端口的元组以便识别"""
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(0.5)
    try:
        start_time = time.perf_counter()
        s.connect((address, port))
        s.send(b'ping')
        s.recv(1024)
        delay = (time.perf_counter() - start_time) * 1000
        return address, port, delay
    except Exception:
        return address, port, -1.0
    finally:
        s.close()


def ping_ip_port(ip_port: str) -> Tuple[str, float]:
    """封装 ping 函数，方便在线程池中调用，返回原始地址和延迟"""
    try:
        ip, port_str = ip_port.rsplit(':', 1)
        port = int(port_str)
        _, _, delay = ping(ip, port)
        return ip_port, delay
    except ValueError:
        return ip_port, -1.0


def batch_ping_addresses_sync(addresses: List[str]) -> Dict[str, float]:
    """
    使用共享的线程池并发测试多个地址的同步版本。
    """
    if not addresses:
        return {}

    results = {}
    with concurrent.futures.ThreadPoolExecutor(max_workers=len(addresses)) as executor:
        future_to_addr = {executor.submit(ping_ip_port, addr): addr for addr in addresses}
        for future in concurrent.futures.as_completed(future_to_addr):
            addr = future_to_addr[future]
            try:
                addr_res, speed = future.result()
                if speed > 0:
                    results[addr_res] = speed
            except Exception as exc:
                # Log the exception if necessary
                pass
    return results


def get_best_address(addresses: List[str]) -> Optional[Tuple[str, float]]:
    """从地址列表中获取延迟最低的地址"""
    if not addresses:
        return None, -1

    speed_results = batch_ping_addresses_sync(addresses)
    if not speed_results:
        return None, -1

    best_address = min(speed_results.items(), key=lambda x: x[1])
    return best_address


def calculate_average_speed(speed_results: Dict[str, float]) -> float:
    """计算平均延迟"""
    if not speed_results:
        return -1

    return sum(speed_results.values()) / len(speed_results)
