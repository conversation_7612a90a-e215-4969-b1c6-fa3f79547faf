import typer

from features.instrument_scraper import cli as instrument_cli

# Create the main Typer app
app = typer.Typer(
    name="dianjia-cli",
    help="A CLI for various data scripts and utilities for the DianJia project.",
    no_args_is_help=True,
)

# Add the instrument-related commands as a sub-application
app.add_typer(instrument_cli.app, name="instrument")

# You can add other sub-applications here in the future
# For example:
# from features.other_feature import cli as other_cli
# app.add_typer(other_cli.app, name="other")

if __name__ == "__main__":
    app()