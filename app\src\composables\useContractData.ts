import { ref, computed } from 'vue'
import type { IContractResponse } from '@/types/contract'
import type { IInstrumentSelectItem } from '@/types/instrument'
import { getContractsAsPricer } from '@/api/contract'
import { getInstrumentById } from '@/api/instrument'

export function useContractData() {
  // 响应式状态
  const availableContracts = ref<IContractResponse[]>([])
  const instrumentId = ref<number | null>(null)
  const setterID = ref<number | null>(null)
  const setterName = ref<string>('')
  const instrument = ref<IInstrumentSelectItem | null>(null)

  // 当前选中组合的合同
  const currentContracts = computed(() => {
    if (!instrumentId.value || !setterID.value) return []
    return availableContracts.value.filter(contract =>
      contract.instrumentRefID === instrumentId.value &&
      contract.setterID === setterID.value
    )
  })

  // 可用的 setterID-instrumentId 组合（使用 Record 格式确保唯一性）
  const availableCombinations = computed(() => {
    const combinations: Record<string, any> = {}
    
    availableContracts.value.forEach(contract => {
      const key = `${contract.setterID}-${contract.instrumentRefID}`
      if (!combinations[key]) {
        // 获取 setter 名称和 instrument 名称
        const setterName = contract.setter?.nickName || `用户${contract.setterID}`
        const instrumentName = contract.instrument?.instrument_name || contract.instrument?.instrument_id || `合约${contract.instrumentRefID}`
        
        combinations[key] = {
          value: key,
          label: `${setterName}-${instrumentName}`,
          setterID: contract.setterID,
          instrumentRefID: contract.instrumentRefID,
          setterName,
          instrumentName,
          contract // 保存原始合同数据以便后续使用
        }
      }
    })
    
    return Object.values(combinations)
  })

  // 当前选中的组合
  const currentCombination = computed(() => {
    if (!setterID.value || !instrumentId.value) return null
    const key = `${setterID.value}-${instrumentId.value}`
    return availableCombinations.value.find(combo => combo.value === key) || null
  })

  // 当前选中的组合显示文本
  const currentCombinationDisplay = computed(() => {
    if (!currentCombination.value) return { setterName: '请选择', instrumentName: '交易组合' }
    return {
      setterName: currentCombination.value.setterName,
      instrumentName: currentCombination.value.instrumentName
    }
  })

  // 选择器的值（用于 v-model）
  const pickerValue = computed({
    get: () => currentCombination.value?.value || '',
    set: () => {
      // 这里不需要处理，因为会通过 confirm 事件处理
    }
  })

  // 根据当前操作类型过滤合同（基于当前选中的合同）
  const getCurrentAvailableContracts = (isPointPrice: boolean) => {
    return computed(() => {
      if (isPointPrice) {
        // 点价操作：只显示基差合同
        return currentContracts.value.filter(contract => contract.priceType === 'basis')
      } else {
        // 洗基差操作：只显示固定价合同
        return currentContracts.value.filter(contract => contract.priceType === 'fixed')
      }
    })
  }

  // 统计数据计算（基于当前选中的合同）
  const getContractStats = () => {
    return computed(() => {
      const contracts = currentContracts.value

      let remainingQuantity = 0
      let frozenQuantity = 0
      let totalWeightedPrice = 0
      let totalQuantity = 0

      contracts.forEach(contract => {
        remainingQuantity += contract.remainingQuantity
        frozenQuantity += contract.frozenQuantity

        const availableQuantity = contract.remainingQuantity - contract.frozenQuantity
        if (availableQuantity > 0) {
          totalWeightedPrice += contract.priceValue * availableQuantity
          totalQuantity += availableQuantity
        }
      })

      return {
        remainingQuantity,
        frozenQuantity,
        availableQuantity: totalQuantity,
        weightedPrice: totalQuantity > 0 ? totalWeightedPrice / totalQuantity : 0
      }
    })
  }

  // 当前操作类型的统计数据
  const getContractStatsByType = (isPointPrice: boolean) => {
    return computed(() => {
      const contracts = getCurrentAvailableContracts(isPointPrice).value

      let remainingQuantity = 0
      let frozenQuantity = 0
      let totalWeightedPrice = 0
      let totalQuantity = 0

      contracts.forEach(contract => {
        remainingQuantity += contract.remainingQuantity
        frozenQuantity += contract.frozenQuantity

        const availableQuantity = contract.remainingQuantity - contract.frozenQuantity
        if (availableQuantity > 0) {
          totalWeightedPrice += contract.priceValue * availableQuantity
          totalQuantity += availableQuantity
        }
      })

      return {
        remainingQuantity,
        frozenQuantity,
        availableQuantity: totalQuantity,
        weightedPrice: totalQuantity > 0 ? totalWeightedPrice / totalQuantity : 0
      }
    })
  }

  // 加载期货合约信息
  async function loadInstrument(id: number) {
    try {
      console.log('开始加载期货合约信息, ID:', id)
      const response = await getInstrumentById(id)
      console.log('期货合约API响应:', response)
      if (response.code === 0) {
        instrument.value = response.data
        console.log('期货合约信息加载成功:', instrument.value)
      }
    } catch (error) {
      console.error('加载期货合约信息失败:', error)
    }
  }

  // 加载所有可用合同（不按 setterID 和 instrumentId 过滤）
  async function loadAllAvailableContracts() {
    try {
      console.log('开始加载所有可用合同')
      const response = await getContractsAsPricer({ status: 'Executing' })
      console.log('合同API响应:', response)
      if (response.code === 0) {
        availableContracts.value = response.data?.list || []
        console.log('所有可用合同加载成功，数量:', availableContracts.value.length)
        
        // 如果有可用合同但没有选中组合，自动选择第一个
        if (availableContracts.value.length > 0 && availableCombinations.value.length > 0 && !currentCombination.value) {
          const firstCombo = availableCombinations.value[0]
          setterID.value = firstCombo.setterID
          instrumentId.value = firstCombo.instrumentRefID
          setterName.value = firstCombo.setterName
          
          // 加载对应的期货合约信息
          if (firstCombo.instrumentRefID) {
            await loadInstrument(firstCombo.instrumentRefID)
          }
        }
      } else {
        console.warn('合同API返回错误:', response.msg)
      }
    } catch (error) {
      console.error('加载所有可用合同失败:', error)
    }
  }

  // 处理组合选择变更
  function handleCombinationChange(selectedValue: string) {
    console.log('组合选择变更:', selectedValue)

    const selectedCombo = availableCombinations.value.find(combo => combo.value === selectedValue)
    if (selectedCombo) {
      // 更新 setterID 和 instrumentId
      setterID.value = selectedCombo.setterID
      instrumentId.value = selectedCombo.instrumentRefID
      setterName.value = selectedCombo.setterName

      console.log('更新选中组合:', {
        setterID: setterID.value,
        instrumentId: instrumentId.value,
        setterName: setterName.value
      })

      // 加载对应的期货合约信息
      if (selectedCombo.instrumentRefID) {
        loadInstrument(selectedCombo.instrumentRefID)
      }
    }
  }

  // 重置合同数据
  function resetContractData() {
    availableContracts.value = []
    instrumentId.value = null
    setterID.value = null
    setterName.value = ''
    instrument.value = null
  }

  return {
    // 状态
    availableContracts,
    instrumentId,
    setterID,
    setterName,
    instrument,
    
    // 计算属性
    currentContracts,
    availableCombinations,
    currentCombination,
    currentCombinationDisplay,
    pickerValue,
    
    // 方法
    getCurrentAvailableContracts,
    getContractStats,
    getContractStatsByType,
    loadInstrument,
    loadAllAvailableContracts,
    handleCombinationChange,
    resetContractData
  }
}
