"""
加密解密工具

提供数据加密和解密功能，包括：
- AES加密解密
- 机器特征密钥生成
- Base64编码解码
- 哈希计算
"""

import os
import base64
import hashlib
import platform
import logging
from typing import Optional, Union

from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from ..exceptions import CryptoException, handle_auth_exception


class CryptoManager:
    """加密管理器
    
    提供统一的加密解密接口。
    """
    
    def __init__(self, custom_key: Optional[str] = None):
        """初始化加密管理器
        
        Args:
            custom_key: 自定义密钥（可选）
        """
        self.logger = logging.getLogger(__name__)
        
        # 生成或使用自定义密钥
        if custom_key:
            self.master_key = self._derive_key_from_string(custom_key)
        else:
            self.master_key = self._generate_machine_key()
        
        # 创建Fernet实例
        self.fernet = Fernet(self.master_key)
        
        self.logger.info("加密管理器初始化完成")
    
    def _generate_machine_key(self) -> bytes:
        """生成基于机器特征的密钥
        
        Returns:
            bytes: 32字节的密钥
        """
        try:
            # 收集机器特征信息
            machine_info = [
                platform.node(),           # 计算机名
                platform.system(),         # 操作系统
                platform.processor(),      # 处理器信息
                platform.machine(),        # 机器类型
            ]
            
            # 尝试获取MAC地址
            try:
                import uuid
                mac = uuid.getnode()
                machine_info.append(str(mac))
            except:
                pass
            
            # 尝试获取磁盘序列号（Windows）
            try:
                if platform.system() == "Windows":
                    import subprocess
                    result = subprocess.run(
                        ["wmic", "diskdrive", "get", "serialnumber"],
                        capture_output=True, text=True, timeout=5
                    )
                    if result.returncode == 0:
                        machine_info.append(result.stdout.strip())
            except:
                pass
            
            # 组合机器信息
            machine_string = "|".join(filter(None, machine_info))
            
            # 如果无法获取足够的机器信息，使用默认值
            if not machine_string:
                machine_string = "default_machine_identifier"
                self.logger.warning("无法获取机器特征，使用默认标识符")
            
            # 生成密钥
            return self._derive_key_from_string(machine_string)
            
        except Exception as e:
            self.logger.error(f"生成机器密钥失败: {str(e)}")
            # 使用默认密钥
            return self._derive_key_from_string("default_fallback_key")
    
    def _derive_key_from_string(self, key_string: str, salt: Optional[bytes] = None) -> bytes:
        """从字符串派生密钥
        
        Args:
            key_string: 密钥字符串
            salt: 盐值（可选）
            
        Returns:
            bytes: 32字节的密钥
        """
        if salt is None:
            # 使用固定盐值确保密钥一致性
            salt = b"order_auth_salt_2024"
        
        # 使用PBKDF2派生密钥
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(key_string.encode()))
        return key
    
    @handle_auth_exception
    def encrypt(self, data: Union[str, bytes]) -> str:
        """加密数据
        
        Args:
            data: 待加密的数据
            
        Returns:
            str: Base64编码的加密数据
            
        Raises:
            CryptoException: 加密失败
        """
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            encrypted_data = self.fernet.encrypt(data)
            return base64.b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"数据加密失败: {str(e)}")
            raise CryptoException(f"数据加密失败: {str(e)}", "encrypt")
    
    @handle_auth_exception
    def decrypt(self, encrypted_data: str) -> str:
        """解密数据
        
        Args:
            encrypted_data: Base64编码的加密数据
            
        Returns:
            str: 解密后的数据
            
        Raises:
            CryptoException: 解密失败
        """
        try:
            # Base64解码
            encrypted_bytes = base64.b64decode(encrypted_data.encode('utf-8'))
            
            # Fernet解密
            decrypted_data = self.fernet.decrypt(encrypted_bytes)
            
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"数据解密失败: {str(e)}")
            raise CryptoException(f"数据解密失败: {str(e)}", "decrypt")
    
    @handle_auth_exception
    def encrypt_file(self, file_path: str, output_path: Optional[str] = None) -> str:
        """加密文件
        
        Args:
            file_path: 源文件路径
            output_path: 输出文件路径（可选）
            
        Returns:
            str: 加密文件路径
            
        Raises:
            CryptoException: 加密失败
        """
        try:
            if not os.path.exists(file_path):
                raise CryptoException(f"文件不存在: {file_path}", "encrypt_file")
            
            if output_path is None:
                output_path = f"{file_path}.encrypted"
            
            # 读取文件
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # 加密数据
            encrypted_data = self.fernet.encrypt(file_data)
            
            # 写入加密文件
            with open(output_path, 'wb') as f:
                f.write(encrypted_data)
            
            self.logger.info(f"文件加密成功: {file_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"文件加密失败: {str(e)}")
            raise CryptoException(f"文件加密失败: {str(e)}", "encrypt_file")
    
    @handle_auth_exception
    def decrypt_file(self, encrypted_file_path: str, output_path: Optional[str] = None) -> str:
        """解密文件
        
        Args:
            encrypted_file_path: 加密文件路径
            output_path: 输出文件路径（可选）
            
        Returns:
            str: 解密文件路径
            
        Raises:
            CryptoException: 解密失败
        """
        try:
            if not os.path.exists(encrypted_file_path):
                raise CryptoException(f"加密文件不存在: {encrypted_file_path}", "decrypt_file")
            
            if output_path is None:
                if encrypted_file_path.endswith('.encrypted'):
                    output_path = encrypted_file_path[:-10]  # 移除.encrypted后缀
                else:
                    output_path = f"{encrypted_file_path}.decrypted"
            
            # 读取加密文件
            with open(encrypted_file_path, 'rb') as f:
                encrypted_data = f.read()
            
            # 解密数据
            decrypted_data = self.fernet.decrypt(encrypted_data)
            
            # 写入解密文件
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            self.logger.info(f"文件解密成功: {encrypted_file_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"文件解密失败: {str(e)}")
            raise CryptoException(f"文件解密失败: {str(e)}", "decrypt_file")
    
    @staticmethod
    def generate_random_key() -> str:
        """生成随机密钥
        
        Returns:
            str: Base64编码的随机密钥
        """
        key = Fernet.generate_key()
        return base64.b64encode(key).decode('utf-8')
    
    @staticmethod
    def hash_password(password: str, salt: Optional[str] = None) -> tuple[str, str]:
        """哈希密码
        
        Args:
            password: 密码
            salt: 盐值（可选）
            
        Returns:
            tuple[str, str]: (哈希值, 盐值)
        """
        if salt is None:
            salt = base64.b64encode(os.urandom(32)).decode('utf-8')
        
        # 使用PBKDF2哈希密码
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt.encode(),
            iterations=100000,
        )
        
        hashed = base64.b64encode(kdf.derive(password.encode())).decode('utf-8')
        return hashed, salt
    
    @staticmethod
    def verify_password(password: str, hashed: str, salt: str) -> bool:
        """验证密码
        
        Args:
            password: 原始密码
            hashed: 哈希值
            salt: 盐值
            
        Returns:
            bool: 验证是否通过
        """
        try:
            new_hashed, _ = CryptoManager.hash_password(password, salt)
            return new_hashed == hashed
        except:
            return False
    
    @staticmethod
    def calculate_file_hash(file_path: str, algorithm: str = "sha256") -> str:
        """计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 (md5, sha1, sha256, sha512)
            
        Returns:
            str: 哈希值
            
        Raises:
            CryptoException: 计算失败
        """
        try:
            if not os.path.exists(file_path):
                raise CryptoException(f"文件不存在: {file_path}", "calculate_hash")
            
            # 选择哈希算法
            if algorithm.lower() == "md5":
                hasher = hashlib.md5()
            elif algorithm.lower() == "sha1":
                hasher = hashlib.sha1()
            elif algorithm.lower() == "sha256":
                hasher = hashlib.sha256()
            elif algorithm.lower() == "sha512":
                hasher = hashlib.sha512()
            else:
                raise CryptoException(f"不支持的哈希算法: {algorithm}", "calculate_hash")
            
            # 分块读取文件计算哈希
            with open(file_path, 'rb') as f:
                while chunk := f.read(8192):
                    hasher.update(chunk)
            
            return hasher.hexdigest()
            
        except Exception as e:
            raise CryptoException(f"计算文件哈希失败: {str(e)}", "calculate_hash")
    
    def get_key_info(self) -> dict:
        """获取密钥信息
        
        Returns:
            dict: 密钥信息
        """
        return {
            "key_length": len(self.master_key),
            "key_type": "Fernet",
            "machine_based": True
        }
    
    def test_encryption(self) -> bool:
        """测试加密解密功能
        
        Returns:
            bool: 测试是否通过
        """
        try:
            test_data = "这是一个测试字符串 - Test String 123"
            encrypted = self.encrypt(test_data)
            decrypted = self.decrypt(encrypted)
            
            success = test_data == decrypted
            
            if success:
                self.logger.info("加密解密测试通过")
            else:
                self.logger.error("加密解密测试失败")
            
            return success
            
        except Exception as e:
            self.logger.error(f"加密解密测试异常: {str(e)}")
            return False
