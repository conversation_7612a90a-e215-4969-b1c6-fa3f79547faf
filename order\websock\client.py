"""
WebSocket客户端核心实现

提供WebSocket连接、消息发送接收、状态管理等核心功能
重构版本：移除异步功能，改用同步WebSocket客户端和EventEngine事件分发
"""

import json
import logging
import time
import threading
from datetime import datetime
from typing import Optional, Dict, Any
import websocket

from vnpy.event import EventEngine, Event

from auth.managers.token_manager import TokenManager
from auth.events import (
    EVENT_USER_LOGIN_SUCCESS, EVENT_USER_LOGOUT, EVENT_TOKEN_REFRESHED
)

from .models import (
    WSMessageEnvelope, ConnectionState, ConnectionInfo, WSConfig, WSStats,
    AuthMessage, PingMessage
)
from .events import *
from .exceptions import *


class WebSocketClient:
    """WebSocket客户端核心类"""

    # ==================== 初始化和配置 ====================

    def __init__(self, event_engine: EventEngine, config: WSConfig = None):
        """初始化WebSocket客户端

        Args:
            event_engine: vnpy事件引擎
            config: WebSocket配置
        """
        self.event_engine = event_engine
        self.config = config or WSConfig()
        self.logger = logging.getLogger(__name__)

        # WebSocketApp相关
        self.websocket_app: Optional[websocket.WebSocketApp] = None
        self._ws_thread: Optional[threading.Thread] = None
        self.connection_info = ConnectionInfo()
        self.stats = WSStats()

        # 状态管理
        self._state = ConnectionState.DISCONNECTED
        self._connect_time: Optional[datetime] = None

        # 连接控制
        self._local_close_flag: bool = False  # 标记是否主动关闭
        self._last_error: Optional[str] = None

        # 认证集成
        self._current_token: Optional[str] = None

        # 心跳相关
        self._last_ping_time: Optional[float] = None
        self._last_pong_time: Optional[float] = None

        # 事件监听器跟踪
        self._registered_events = []  # 跟踪已注册的事件监听器

        # 设置事件监听
        self._setup_event_listeners()

        # 初始化时自动连接
        self.connect()

    @property
    def state(self) -> ConnectionState:
        """获取连接状态"""
        return self._state

    @property
    def is_connected(self) -> bool:
        """是否已连接"""
        return self._state in (ConnectionState.CONNECTED, ConnectionState.AUTHENTICATED)

    @property
    def is_authenticated(self) -> bool:
        """是否已认证"""
        return self._state == ConnectionState.AUTHENTICATED

    def _setup_event_listeners(self):
        """设置事件监听器"""
        from vnpy.trader.event import EVENT_TIMER

        # 认证相关事件
        self._register_event(EVENT_USER_LOGIN_SUCCESS, self._on_user_login)
        self._register_event(EVENT_USER_LOGOUT, self._on_user_logout)
        self._register_event(EVENT_TOKEN_REFRESHED, self._on_token_refreshed)

        # 定时器事件 - 用于心跳
        self._register_event(EVENT_TIMER, self._on_heartbeat_timer)
        self.logger.info(f"心跳定时器已注册，间隔: {self.config.heartbeat_interval}秒")

    # ==================== 连接管理 ====================

    def connect(self) -> bool:
        """连接到WebSocket服务器"""
        if self.is_connected:
            self.logger.warning("Already connected")
            return True

        try:
            self._set_state(ConnectionState.CONNECTING)
            self.logger.info(f"Connecting to WebSocket server: {self.config.server_url}")

            # 创建WebSocketApp
            self.websocket_app = websocket.WebSocketApp(
                self.config.server_url,
                on_open=self._on_websocket_open,
                on_close=self._on_websocket_close,
                on_error=self._on_websocket_error,
                on_message=self._on_websocket_message
            )

            # 保存连接信息
            self.connection_info.server_url = self.config.server_url

            # 在线程中运行WebSocket
            self.websocket_app.keep_running = True
            self._ws_thread = threading.Thread(
                target=self.websocket_app.run_forever,
                daemon=True
            )
            self._ws_thread.start()

            self.logger.info("WebSocket connection initiated")
            return True

        except Exception as e:
            error_msg = f"Failed to connect: {str(e)}"
            self.logger.error(error_msg)
            self._set_state(ConnectionState.ERROR, error_msg)
            return False
            
    def disconnect(self):
        """断开WebSocket连接"""
        if not self.is_connected:
            return
            
        self.logger.info("Disconnecting WebSocket")

        # 设置主动关闭标志
        self._local_close_flag = True

        # 关闭WebSocketApp
        if self.websocket_app:
            try:
                self.websocket_app.close()
            except Exception as e:
                self.logger.warning(f"Error closing websocket app: {e}")

        # 等待线程结束
        if self._ws_thread and self._ws_thread.is_alive():
            self._ws_thread.join(timeout=5)

        # 清理
        self.websocket_app = None
        self._ws_thread = None

        self.logger.info("WebSocket disconnected")

    def close(self):
        """完整关闭WebSocket客户端并清理所有资源"""
        self.logger.info("开始关闭WebSocket客户端")

        try:
            # 1. 停止定时器心跳（通过注销事件监听器实现）
            self.logger.debug("停止心跳定时器")

            # 2. 断开WebSocket连接
            if self.is_connected:
                self.logger.debug("断开WebSocket连接")
                self.disconnect()

            # 3. 注销所有事件监听器
            self._unregister_all_events()

            # 4. 清理内部状态
            self._clear_internal_state()

            self.logger.info("WebSocket客户端已完全关闭")

        except Exception as e:
            self.logger.error(f"关闭WebSocket客户端时发生错误: {str(e)}")

    # ==================== WebSocket事件处理 ====================

    def _on_websocket_open(self, ws):
        """原生WebSocket连接打开事件"""
        self.logger.info("WebSocket connection opened")
        self._connect_time = datetime.now()
        self.connection_info.connect_time = self._connect_time

        # 设置连接状态
        self._set_state(ConnectionState.CONNECTED)

        # 发送连接成功事件
        self.event_engine.put(Event(EVENT_WS_CONNECTED, {
            "url": self.connection_info.server_url,
            "connect_time": self._connect_time
        }))

        # 如果有token则自动认证
        if self._current_token and self.is_connected:
            self.logger.info("Auto authenticating after connection")
            self.authenticate(self._current_token)

    def _on_websocket_close(self, ws, close_status_code, close_msg):
        """原生WebSocket连接关闭事件"""
        self.logger.info(f"Native WebSocket connection closed: {close_status_code} - {close_msg}")

        # 设置连接状态
        self._set_state(ConnectionState.DISCONNECTED)

        # 发送断开事件
        self.event_engine.put(Event(EVENT_WS_DISCONNECTED, {
            "close_code": close_status_code,
            "close_reason": close_msg,
            "disconnect_time": datetime.now(),
            "was_local_close": self._local_close_flag
        }))

        # 如果不是主动关闭，则需要重新建立连接
        if not self._local_close_flag:
            self.websocket_app.close()
            self.connect()

    def _on_websocket_error(self, ws, error):
        """原生WebSocket错误事件"""
        self.logger.error(f"Native WebSocket error: {error}")

        # 设置错误状态
        self._set_state(ConnectionState.ERROR, str(error))

        # 发送错误事件
        self.event_engine.put(Event(EVENT_WS_ERROR, {
            "error": str(error),
            "timestamp": datetime.now()
        }))

    def _on_websocket_message(self, ws, message):
        """原生WebSocket消息事件"""
        try:
            self._handle_received_message(message)
        except Exception as e:
            self.logger.error(f"Error handling received message: {e}")

    # ==================== 消息处理 ====================

    def _handle_received_message(self, message_str: str):
        """处理接收到的消息"""
        try:
            self.logger.debug(f"Received raw message: {message_str}")

            # 更新统计
            self.stats.messages_received += 1
            if isinstance(message_str, str):
                self.stats.bytes_received += len(message_str.encode('utf-8'))
            else:
                self.stats.bytes_received += len(message_str)

            # 解析消息
            if isinstance(message_str, bytes):
                message_str = message_str.decode('utf-8')

            # 尝试解析为WSMessageEnvelope格式
            try:
                message = WSMessageEnvelope.from_json(message_str)
                self.logger.debug(f"Parsed message: event={message.event}, payload={message.payload}")
            except json.JSONDecodeError as e:
                self.logger.warning(f"Failed to parse as WSMessageEnvelope: {e}")
                # 如果不是标准格式，创建一个通用消息
                try:
                    raw_data = json.loads(message_str)
                    message = WSMessageEnvelope(
                        event="raw_message",
                        payload=raw_data,
                        timestamp=time.time(),
                        source="server"
                    )
                except json.JSONDecodeError:
                    # 完全无法解析的消息，作为文本处理
                    message = WSMessageEnvelope(
                        event="text_message",
                        payload={"text": message_str},
                        timestamp=time.time(),
                        source="server"
                    )

            # 处理特殊消息
            if message.event == "pong":
                self._handle_pong_message(message)

            # 发送消息接收事件
            self.event_engine.put(Event(EVENT_WS_MESSAGE_RECEIVED, {
                "event": message.event,
                "payload": message.payload,
                "source": message.source,
                "timestamp": datetime.now()
            }))

            # 分发消息事件
            self._dispatch_message_event(message)

        except Exception as e:
            self.logger.error(f"Error processing received message: {e}")

    def _handle_pong_message(self, message: WSMessageEnvelope):
        """处理pong消息"""
        self.connection_info.last_heartbeat = datetime.now()
        self._last_pong_time = time.time()

        # 计算网络延迟
        if self._last_ping_time:
            latency = (self._last_pong_time - self._last_ping_time) * 1000
            self.event_engine.put(Event(EVENT_WS_PONG_RECEIVED, {
                "latency_ms": latency,
                "timestamp": datetime.now()
            }))

    def send_message(self, message: WSMessageEnvelope) -> bool:
        """发送消息

        Args:
            message: 要发送的消息

        Returns:
            bool: 发送是否成功
        """
        if not self.websocket_app:
            raise ConnectionException("WebSocket not connected")

        try:
            # 序列化消息
            message_str = message.to_json()
            message_bytes = message_str.encode('utf-8')

            # 发送消息
            self.websocket_app.send(message_str)

            # 更新统计
            self.stats.messages_sent += 1
            self.stats.bytes_sent += len(message_bytes)

            # 发送消息发送事件
            self.event_engine.put(Event(EVENT_WS_MESSAGE_SENT, {
                "event": message.event,
                "message_size": len(message_bytes),
                "timestamp": datetime.now()
            }))

            self.logger.debug(f"Message sent: {message.event}")
            return True

        except Exception as e:
            self.logger.error(f"Failed to send message: {str(e)}")
            return False

    def _dispatch_message_event(self, message: WSMessageEnvelope):
        """通过EventEngine分发消息事件"""
        try:
            # 发送具体的消息事件，格式为 websocket.{event_name}
            event_type = f"websocket.{message.event}"
            self.event_engine.put(Event(event_type, {
                "payload": message.payload,
                "source": message.source,
                "timestamp": message.timestamp
            }))

            self.logger.debug(f"Dispatched message event: {event_type}")

        except Exception as e:
            self.logger.error(f"Failed to dispatch message event: {str(e)}")

    def _unregister_all_events(self):
        """注销所有已注册的事件监听器"""
        self.logger.debug(f"开始注销 {len(self._registered_events)} 个事件监听器")

        for event_type, handler in self._registered_events:
            try:
                self.event_engine.unregister(event_type, handler)
                self.logger.debug(f"已注销事件监听器: {event_type}")
            except Exception as e:
                self.logger.warning(f"注销事件监听器失败 {event_type}: {str(e)}")

        self._registered_events.clear()
        self.logger.debug("所有事件监听器已注销")

    def _clear_internal_state(self):
        """清理内部状态"""
        self.logger.debug("清理内部状态")

        # 清理认证相关状态
        self._current_token = None

        # 清理心跳相关状态
        self._last_ping_time = None
        self._last_pong_time = None

        # 重置连接状态
        self._set_state(ConnectionState.DISCONNECTED)

        # 清理连接信息
        self.connection_info.client_id = None
        self.connection_info.connect_time = None
        self.connection_info.last_heartbeat = None

        self.logger.debug("内部状态已清理")

    # ==================== 认证管理 ====================



    def authenticate(self, token: str) -> bool:
        """进行身份认证

        Args:
            token: JWT token

        Returns:
            bool: 认证是否成功
        """
        if not self.is_connected:
            raise ConnectionException("Must be connected before authentication")
        try:
            # 设置认证状态
            self._set_state(ConnectionState.AUTHENTICATING)

            # 发送认证消息
            auth_msg = AuthMessage(token)
            success = self.send_message(auth_msg)

            if success:
                # 保存token
                self._current_token = token

                # 设置已认证状态
                self._set_state(ConnectionState.AUTHENTICATED)

                # 发送认证成功事件
                self.event_engine.put(Event(EVENT_WS_AUTH_SUCCESS, {
                    "token": token,
                    "timestamp": datetime.now()
                }))

                self.logger.info("Authentication successful")
                return True
            else:
                # 认证失败，回退到已连接状态
                self._set_state(ConnectionState.CONNECTED)
                self.logger.error("Authentication failed: Failed to send auth message")
                return False

        except Exception as e:
            error_msg = f"Authentication error: {str(e)}"
            self.logger.error(error_msg)

            # 认证失败，回退到已连接状态
            self._set_state(ConnectionState.CONNECTED, error_msg)

            # 发送认证失败事件
            self.event_engine.put(Event(EVENT_WS_AUTH_FAILED, {
                "error": error_msg,
                "timestamp": datetime.now()
            }))

            return False

    def _on_user_login(self, event: Event):
        """处理用户登录事件"""
        try:
            token_data = event.data

            # 保存token
            if hasattr(token_data, 'access_token'):
                self._current_token = token_data.access_token
            elif hasattr(token_data, 'token'):
                self._current_token = token_data.token
            else:
                self.logger.warning("No token found in login event")
                return

            self.logger.info("User login detected")

            # 如果已连接则自动认证，如果未连接则等待连接后自动认证
            if self.is_connected and self._current_token:
                self.logger.info("Auto authenticating after user login")
                self.authenticate(self._current_token)

        except Exception as e:
            self.logger.error(f"Error handling user login: {str(e)}")

    def _on_user_logout(self, event: Event):
        """处理用户登出事件"""
        self.logger.info("User logout detected")
        self._current_token = None

        # 主动断开WebSocket连接（不重连）
        self.disconnect()

    def _on_token_refreshed(self, event: Event):
        """处理token刷新事件"""
        try:
            token_data = event.data
            old_token = self._current_token

            # 更新token
            if hasattr(token_data, 'access_token'):
                self._current_token = token_data.access_token
            elif hasattr(token_data, 'token'):
                self._current_token = token_data.token
            else:
                self.logger.warning("No token found in refresh event")
                return

            self.logger.info("Token refreshed")

            # 如果WebSocket已连接且已认证，需要重新认证
            if (self.is_authenticated and old_token != self._current_token and self._current_token):
                self.logger.info("Auto authenticating after token refresh")
                self.authenticate(self._current_token)

        except Exception as e:
            self.logger.error(f"Error handling token refresh: {str(e)}")

    # ==================== 心跳机制 ====================

    def _on_heartbeat_timer(self, event: Event):
        """定时器事件处理 - 仅心跳发送"""
        # 如果未连接，跳过心跳
        if not self.is_connected:
            return

        current_time = time.time()

        # 检查是否需要发送心跳
        if (self._last_ping_time is None or
            current_time - self._last_ping_time >= self.config.heartbeat_interval):

            # 发送心跳
            try:
                client_timestamp = int(current_time * 1000)
                ping_message = PingMessage(client_timestamp=client_timestamp)

                if self.send_message(ping_message):
                    self._last_ping_time = current_time
                    self.logger.debug(f"心跳已发送: {current_time}")

                    # 发送心跳事件
                    self.event_engine.put(Event(EVENT_WS_PING_SENT, {
                        "timestamp": current_time,
                        "client_timestamp": client_timestamp,
                        "client_id": self.connection_info.client_id
                    }))
                else:
                    self.logger.warning("心跳发送失败")

            except Exception as e:
                self.logger.error(f"发送心跳时发生错误: {str(e)}")

        # 检查心跳超时（仅记录，不重连）
        if (self._last_ping_time is not None and
            current_time - self._last_ping_time > self.config.heartbeat_timeout):
            # 如果发送ping后超过超时时间还没收到pong，认为超时
            if (self._last_pong_time is None or
                self._last_ping_time > self._last_pong_time):
                self.logger.error(f"心跳超时检测到。最后ping: {self._last_ping_time}, 最后pong: {self._last_pong_time}")

                # 发送心跳超时事件
                self.event_engine.put(Event(EVENT_WS_HEARTBEAT_TIMEOUT, {
                    "last_ping": self._last_ping_time,
                    "last_pong": self._last_pong_time,
                    "timeout": self.config.heartbeat_timeout,
                    "timestamp": datetime.now()
                }))

    # ==================== 状态和工具 ====================

    def _set_state(self, new_state: ConnectionState, error_msg: str = None):
        """设置连接状态"""
        old_state = self._state
        self._state = new_state

        # 更新连接信息
        self.connection_info.state = new_state
        if error_msg:
            self._last_error = error_msg
            self.stats.last_error = error_msg

        # 发送状态变化事件
        self.event_engine.put(Event(EVENT_WS_STATE_CHANGED, {
            "old_state": old_state.value,
            "new_state": new_state.value,
            "error_msg": error_msg,
            "timestamp": datetime.now()
        }))

        self.logger.info(f"WebSocket state changed: {old_state.value} -> {new_state.value}")

    def _clear_internal_state(self):
        """清理内部状态"""
        self._connect_time = None
        self._last_ping_time = None
        self._last_pong_time = None
        self._local_close_flag = False

        # 清理连接信息
        self.connection_info.client_id = None
        self.connection_info.connect_time = None
        self.connection_info.last_heartbeat = None

        # 清理认证相关状态
        self._current_token = None

        self.logger.debug("内部状态已清理")

    def _register_event(self, event_type: str, handler):
        """注册事件监听器并跟踪"""
        self.event_engine.register(event_type, handler)
        self._registered_events.append((event_type, handler))

    def _unregister_all_events(self):
        """注销所有事件监听器"""
        for event_type, handler in self._registered_events:
            try:
                self.event_engine.unregister(event_type, handler)
            except Exception as e:
                self.logger.warning(f"Failed to unregister event {event_type}: {e}")
        self._registered_events.clear()

    def get_connection_info(self) -> ConnectionInfo:
        """获取连接信息"""
        # 更新运行时间
        if self._connect_time:
            self.stats.uptime_seconds = (datetime.now() - self._connect_time).total_seconds()

        return self.connection_info

    def get_stats(self) -> WSStats:
        """获取统计信息"""
        # 更新运行时间
        if self._connect_time:
            self.stats.uptime_seconds = (datetime.now() - self._connect_time).total_seconds()

        return self.stats

    def get_last_error(self) -> Optional[str]:
        """获取最后的错误信息"""
        return self._last_error


    def _dispatch_message_event(self, message: WSMessageEnvelope):
        """通过EventEngine分发消息事件"""
        event_name = f"websocket.{message.event}"
        event_data = {
            "payload": message.payload,
            "timestamp": message.timestamp,
            "source": message.source
        }
        
        # 发送统一的WebSocket消息事件
        self.event_engine.put(Event(event_name, event_data))
        
        # 处理特殊消息
        if message.event == "error":
            error_type = message.payload.get("error_type", "unknown")
            error_msg = message.payload.get("message", "Unknown error")
            self.logger.error(f"Server error [{error_type}]: {error_msg}")
            
            # 发送错误事件
            self.event_engine.put(Event(EVENT_WS_ERROR, {
                "error_type": error_type,
                "error_msg": error_msg,
                "payload": message.payload
            }))
            
    def get_connection_info(self) -> ConnectionInfo:
        """获取连接信息"""
        # 更新运行时间
        if self._connect_time:
            self.stats.uptime_seconds = (datetime.now() - self._connect_time).total_seconds()
            
        return self.connection_info
        
    def get_stats(self) -> WSStats:
        """获取统计信息"""
        # 更新运行时间
        if self._connect_time:
            self.stats.uptime_seconds = (datetime.now() - self._connect_time).total_seconds()
            
        return self.stats
        
    # ========== 认证集成功能 ==========
    
    def _setup_event_listeners(self):
        """设置事件监听器"""
        from vnpy.trader.event import EVENT_TIMER

        # 认证相关事件
        self._register_event(EVENT_USER_LOGIN_SUCCESS, self._on_user_login)
        self._register_event(EVENT_USER_LOGOUT, self._on_user_logout)
        self._register_event(EVENT_TOKEN_REFRESHED, self._on_token_refreshed)

        # 定时器事件 - 用于心跳
        self._register_event(EVENT_TIMER, self._on_heartbeat_timer)
        self.logger.info(f"心跳定时器已注册，间隔: {self.config.heartbeat_interval}秒")

    def _register_event(self, event_type: str, handler):
        """注册事件监听器并跟踪"""
        self.event_engine.register(event_type, handler)
        self._registered_events.append((event_type, handler))
        self.logger.debug(f"已注册事件监听器: {event_type}")



    def get_last_error(self) -> Optional[str]:
        """获取最后的错误信息"""
        return self._last_error



    

if __name__ == "__main__":
    import time
    import logging
    import signal
    import os

    # 设置日志级别
    logging.basicConfig(level=logging.DEBUG)

    event_engine = EventEngine()
    config = {"server_url": "ws://localhost:8888/ws/order"}
    ws_config = WSConfig(**config)

    # 注册消息接收事件监听器
    def on_message_received(event):
        print(f"收到消息: {event.type} - {event.data}")

    def on_connected(event):
        print(f"连接成功: {event.data}")

    def on_disconnected(event):
        print(f"连接断开: {event.data}")

    def on_error(event):
        print(f"连接错误: {event.data}")

    event_engine.register(EVENT_WS_MESSAGE_RECEIVED, on_message_received)
    event_engine.register(EVENT_WS_CONNECTED, on_connected)
    event_engine.register(EVENT_WS_DISCONNECTED, on_disconnected)
    event_engine.register(EVENT_WS_ERROR, on_error)

    # 启动事件引擎
    event_engine.start()

    # 创建WebSocket客户端（不自动连接）
    websocket_client = WebSocketClient(event_engine, ws_config)

    # 设置ctrl + C 可以退程序
    def signal_handler(sig, frame):
        """信号处理器，用于捕获SIGINT信号"""
        print("正在关闭...")
        websocket_client.close()
        event_engine.stop()
        os._exit(0)

    signal.signal(signal.SIGINT, signal_handler)

    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在关闭...")
        websocket_client.close()
        event_engine.stop()
