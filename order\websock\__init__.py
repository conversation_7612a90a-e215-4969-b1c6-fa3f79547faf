"""
WebSocket通信模块

提供与服务端的WebSocket通信功能，包括：
- 连接管理
- Token认证
- 消息处理
- 状态管理
- 心跳机制
- 事件分发

重构版本：移除异步功能和订阅机制，改用同步WebSocket客户端和EventEngine事件分发
"""

from .client import WebSocketClient
from .models import (
    WSMessageEnvelope, 
    ConnectionState, 
    ConnectionInfo,
    AuthMessage,
    PingMessage,
    PongMessage,
    WSConfig,
    WSStats,
    OrderUpdateMessage,
    MarketDataMessage,
    ErrorMessage,
    NotificationMessage
)
from .events import *
from .exceptions import *

__all__ = [
    # 核心客户端
    'WebSocketClient',
    
    # 核心数据模型
    'WSMessageEnvelope',
    'ConnectionState',
    'ConnectionInfo',
    'WSConfig',
    'WSStats',
    
    # 消息类型
    'AuthMessage',
    'PingMessage',
    'PongMessage',  # 新增
    
    # 业务消息类型
    'OrderUpdateMessage',
    'MarketDataMessage',
    'ErrorMessage',
    'NotificationMessage',
    
    # 事件常量（通过 * 导入）
    # 异常类（通过 * 导入）
]