import { http } from '@/http/http'
import type {
  ITradeRequest,
  ICreateTradeRequestRequest,
  ITradeRequestForPricerRequest,
  ITradeRequestForSetterRequest,
  IManualFeedbackRequest,
  IRejectTradeRequestRequest,
  IPageResult,
  ExecutionMode
} from '@/types/trade-request'

/**
 * 交易请求相关API (V4)
 */

// 创建交易请求
export const createTradeRequest = (data: ICreateTradeRequestRequest) => {
  return http.post<ITradeRequest>('/dianjia/traderequests', data)
}

// 获取单个交易请求
export const getTradeRequest = (id: number) => {
  return http.get<ITradeRequest>(`/dianjia/traderequests/${id}`)
}

// 取消交易请求
export const cancelTradeRequest = (id: number) => {
  return http.post<null>(`/dianjia/traderequests/${id}/cancel`)
}

// 拒绝交易请求 (V4新增)
export const rejectTradeRequest = (id: number, data: IRejectTradeRequestRequest) => {
  return http.post<null>(`/dianjia/traderequests/${id}/reject`, data)
}

// 人工反馈交易结果
export const manualFeedback = (id: number, data: IManualFeedbackRequest) => {
  return http.post<null>(`/dianjia/traderequests/${id}/feedback`, data)
}

/**
 * 点价方和被点价方专用接口
 */

// 获取点价方的交易请求列表
export const getTradeRequestsForPricer = (params?: ITradeRequestForPricerRequest) => {
  return http.get<IPageResult<ITradeRequest>>('/dianjia/traderequests/as-pricer', params)
}

// 获取被点价方的交易请求列表
export const getTradeRequestsForSetter = (params?: ITradeRequestForSetterRequest) => {
  return http.get<IPageResult<ITradeRequest>>('/dianjia/traderequests/as-setter', params)
}

/**
 * 便捷方法 - 针对特定业务场景的封装
 */

// 获取我的交易请求（作为点价方）
export const getMyTradeRequestsAsPricer = (params?: ITradeRequestForPricerRequest) => {
  return getTradeRequestsForPricer(params)
}

// 获取我的交易请求（作为被点价方）
export const getMyTradeRequestsAsSetter = (params?: ITradeRequestForSetterRequest) => {
  return getTradeRequestsForSetter(params)
}


export const pointPrice = (setterID: number, instrumentRefID: number, requestedQuantity: number, requestedPrice: number, executionMode: ExecutionMode, expiresAt: string) => {
  return createTradeRequest({
    setterID,
    instrumentRefID,
    requestType: 'PointPrice',
    requestedQuantity,
    requestedPrice,
    executionMode,
    expiresAt
  })
}

// 洗基差操作便捷方法
export const basisWash = (setterID: number, instrumentRefID: number, requestedQuantity: number, executionMode: ExecutionMode, expiresAt: string) => {
  return createTradeRequest({
    setterID,
    instrumentRefID,
    requestType: 'BasisWash',
    requestedQuantity,
    executionMode,
    expiresAt
  })
}

// 获取特定期货合约的点价方交易请求
export const getPricerTradeRequestsByInstrument = (instrumentRefID: number, params?: Omit<ITradeRequestForPricerRequest, 'instrumentRefID'>) => {
  return getTradeRequestsForPricer({
    ...params,
    instrumentRefID
  })
}

// 获取特定期货合约的被点价方交易请求
export const getSetterTradeRequestsByInstrument = (instrumentRefID: number, params?: Omit<ITradeRequestForSetterRequest, 'instrumentRefID'>) => {
  return getTradeRequestsForSetter({
    ...params,
    instrumentRefID
  })
}