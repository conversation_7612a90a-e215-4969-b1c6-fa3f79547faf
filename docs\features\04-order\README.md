# 功能文档：04 - 期货账户下单

## 1. 功能概述

本功能旨在打通线上平台与本地交易终端的连接，实现从 **[功能01 - 基差点价交易](./../01-basis-point-trading/README.md)** 或其他业务场景触发的订单，能够安全、可靠地通过本地部署的 `vn.py` 交易软件执行，并将结果实时反馈回服务器。

此功能的核心是一个典型的C/S（客户端/服务器）架构，通过WebSocket进行实时、双向的指令和数据通信。

## 2. 系统组件

1.  **业务服务器 (`admin/server`)**: 
    -   作为指令的发起方和数据的接收方。
    -   负责管理交易员账户、期货账户信息、订单指令的生成与下发。
    -   通过WebSocket与指定的本地交易终端建立长连接。

2.  **本地交易终端 (Local Trading Client)**:
    -   一个基于 `vn.py` 开发的、部署在交易员本地电脑上的桌面应用程序。
    -   负责实际的下单操作（连接CTP等柜台接口）和账户状态的监控。
    -   是所有交易指令的最终执行者。

3.  **通信协议**: 
    -   **WebSocket**: 用于服务器与本地交易终端之间的实时、低延迟通信。所有指令下发和状态回报都通过此通道完成。

## 3. 本地交易终端核心功能

本地交易终端需要具备以下六大核心功能：

### 3.1. 登录与认证
- **描述**: 终端启动后，必须使用服务器分配的账户进行登录，以验证其操作权限并建立唯一的WebSocket通信信道。
- **流程**: 
  1. 交易员在本地终端输入用户名和密码。
  2. 终端将凭证发送至业务服务器进行验证。
  3. 验证成功后，服务器授权建立WebSocket连接，并标记该终端的在线状态。

### 3.2. 基础数据同步
- **描述**: 登录成功后，终端需要从服务器获取必要的初始数据，以准备后续的交易指令。
- **数据内容**: 包括但不限于该终端绑定的期货账户信息、资金账号、密码、交易席位等。

### 3.3. 下单执行
- **描述**: 接收并执行来自服务器的下单指令。
- **流程**: 
  1. 通过WebSocket接收服务器下发的标准订单对象（JSON格式）。
  2. 终端将该JSON对象解析并转换为 `vn.py` 的订单请求（`OrderRequest`）。
  3. 将订单请求发送至CTP网关，执行下单。

### 3.4. 下单结果反馈
- **描述**: 将订单在交易柜台的完整生命周期状态实时反馈给服务器。
- **反馈内容**: 包括订单的提交状态、委托回报、成交回报、撤单回报等。
- **流程**: `vn.py` 的事件引擎捕获到订单状态变化（`on_order`, `on_trade`）后，立即将这些信息封装并通过WebSocket发送回服务器。

### 3.5. 下单前的交易审核
- **描述**: 在自动化下单流程中加入人工干预节点，为交易员提供最后一道防线。
- **流程**: 
  1. 服务器下发订单指令时，可附加一个“需要审核”的标记。
  2. 本地终端收到此类指令后，不会立即下单，而是在UI界面上弹出一个确认窗口，显示订单详情。
  3. 交易员可以手动点击【同意】执行下单，或【拒绝】取消该笔订单。
  4. 操作结果将通过WebSocket反馈给服务器。

### 3.6. 账户数据上报
- **描述**: 定期或按需将本地终端监控到的期货账户的最新状态同步到服务器。
- **上报内容**: 主要包括账户的资金情况（余额、可用、保证金等）和持仓详情（品种、多空、数量、开仓均价等）。
- **目的**: 使得业务服务器和Web前端能集中展示所有交易账户的风险敞口和盈亏状况。

## 4. 交互流程示意图

```mermaid
sequenceDiagram
    participant Server as 业务服务器
    participant Client as 本地交易终端 (vn.py)
    participant CTP as 期货柜台 (CTP)

    Client->>Server: 登录请求
    Server-->>Client: 登录成功/失败
    Client->>Server: 请求同步基础数据
    Server-->>Client: 返回期货账户等信息

    Note over Server, Client: WebSocket长连接建立

    Server->>Client: 推送下单指令 (含审核标记)
    Client->>Client: UI弹出审核窗口
    Note right of Client: 交易员审核
    Client->>Server: 反馈审核结果 (同意/拒绝)

    alt 同意下单
        Client->>CTP: 发送订单请求 (Place Order)
        CTP-->>Client: 订单状态回报 (Order Status)
        Client->>Server: 实时反馈订单状态
        CTP-->>Client: 成交回报 (Trade)
        Client->>Server: 实时反馈成交信息
    end

    loop 定期/按需
        Client->>Server: 上报账户资金与持仓
    end
```
