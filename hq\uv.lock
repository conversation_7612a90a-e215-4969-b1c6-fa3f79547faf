version = 1
revision = 2
requires-python = ">=3.13"

[[package]]
name = "aiohappyeyeballs"
version = "2.6.1"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/aiohappyeyeballs-2.6.1.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/aiohappyeyeballs-2.6.1-py3-none-any.whl" },
]

[[package]]
name = "aiohttp"
version = "3.12.14"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "aiohappyeyeballs" },
    { name = "aiosignal" },
    { name = "attrs" },
    { name = "frozenlist" },
    { name = "multidict" },
    { name = "propcache" },
    { name = "yarl" },
]
sdist = { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-macosx_10_13_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-musllinux_1_2_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-musllinux_1_2_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-musllinux_1_2_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/aiohttp-3.12.14-cp313-cp313-win_amd64.whl" },
]

[[package]]
name = "aiosignal"
version = "1.4.0"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "frozenlist" },
]
sdist = { url = "https://pypi.vnpy.com/packages/aiosignal-1.4.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/aiosignal-1.4.0-py3-none-any.whl" },
]

[[package]]
name = "apscheduler"
version = "3.11.0"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "tzlocal" },
]
sdist = { url = "https://pypi.vnpy.com/packages/apscheduler-3.11.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/APScheduler-3.11.0-py3-none-any.whl" },
]

[[package]]
name = "attrs"
version = "25.3.0"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/attrs-25.3.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/attrs-25.3.0-py3-none-any.whl" },
]

[[package]]
name = "certifi"
version = "2025.7.14"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/certifi-2025.7.14.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/certifi-2025.7.14-py3-none-any.whl" },
]

[[package]]
name = "cffi"
version = "1.17.1"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "pycparser" },
]
sdist = { url = "https://pypi.vnpy.com/packages/cffi-1.17.1.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/cffi-1.17.1-cp313-cp313-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/cffi-1.17.1-cp313-cp313-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/cffi-1.17.1-cp313-cp313-manylinux_2_12_i686.manylinux2010_i686.manylinux_2_17_i686.manylinux2014_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/cffi-1.17.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/cffi-1.17.1-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/cffi-1.17.1-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/cffi-1.17.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/cffi-1.17.1-cp313-cp313-musllinux_1_1_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/cffi-1.17.1-cp313-cp313-musllinux_1_1_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/cffi-1.17.1-cp313-cp313-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/cffi-1.17.1-cp313-cp313-win_amd64.whl" },
]

[[package]]
name = "charset-normalizer"
version = "3.4.2"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-macosx_10_13_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-cp313-cp313-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/charset_normalizer-3.4.2-py3-none-any.whl" },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/colorama-0.4.6.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/colorama-0.4.6-py2.py3-none-any.whl" },
]

[[package]]
name = "deap"
version = "1.4.3"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "numpy" },
]
sdist = { url = "https://pypi.vnpy.com/packages/deap-1.4.3.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/deap-1.4.3-cp313-cp313-macosx_10_13_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/deap-1.4.3-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/deap-1.4.3-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/deap-1.4.3-cp313-cp313-win_amd64.whl" },
]

[[package]]
name = "fastjsonschema"
version = "2.21.1"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/fastjsonschema-2.21.1.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/fastjsonschema-2.21.1-py3-none-any.whl" },
]

[[package]]
name = "frozenlist"
version = "1.7.0"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-macosx_10_13_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-macosx_10_13_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-cp313-cp313t-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/frozenlist-1.7.0-py3-none-any.whl" },
]

[[package]]
name = "hq"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "aiohttp" },
    { name = "apscheduler" },
    { name = "redis" },
    { name = "requests" },
    { name = "ta-lib" },
    { name = "vnpy" },
    { name = "vnpy-ctp" },
]

[package.metadata]
requires-dist = [
    { name = "aiohttp", specifier = ">=3.12.14" },
    { name = "apscheduler", specifier = ">=3.11.0" },
    { name = "redis", specifier = ">=6.2.0" },
    { name = "requests", specifier = ">=2.32.4" },
    { name = "ta-lib", specifier = "==0.6.4" },
    { name = "vnpy", specifier = ">=4.1.0" },
    { name = "vnpy-ctp", specifier = ">=6.7.7.2" },
]

[[package]]
name = "idna"
version = "3.10"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/idna-3.10.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/idna-3.10-py3-none-any.whl" },
]

[[package]]
name = "jsonschema"
version = "4.25.0"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "attrs" },
    { name = "jsonschema-specifications" },
    { name = "referencing" },
    { name = "rpds-py" },
]
sdist = { url = "https://pypi.vnpy.com/packages/jsonschema-4.25.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/jsonschema-4.25.0-py3-none-any.whl" },
]

[[package]]
name = "jsonschema-specifications"
version = "2025.4.1"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "referencing" },
]
sdist = { url = "https://pypi.vnpy.com/packages/jsonschema_specifications-2025.4.1.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/jsonschema_specifications-2025.4.1-py3-none-any.whl" },
]

[[package]]
name = "jupyter-core"
version = "5.8.1"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "platformdirs" },
    { name = "pywin32", marker = "platform_python_implementation != 'PyPy' and sys_platform == 'win32'" },
    { name = "traitlets" },
]
sdist = { url = "https://pypi.vnpy.com/packages/jupyter_core-5.8.1.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/jupyter_core-5.8.1-py3-none-any.whl" },
]

[[package]]
name = "loguru"
version = "0.7.3"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
    { name = "win32-setctime", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://pypi.vnpy.com/packages/loguru-0.7.3.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/loguru-0.7.3-py3-none-any.whl" },
]

[[package]]
name = "multidict"
version = "6.6.3"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/multidict-6.6.3.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-macosx_10_13_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-manylinux1_i686.manylinux2014_i686.manylinux_2_17_i686.manylinux_2_5_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-manylinux2014_armv7l.manylinux_2_17_armv7l.manylinux_2_31_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-musllinux_1_2_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-musllinux_1_2_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-musllinux_1_2_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313-win_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-macosx_10_13_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-manylinux1_i686.manylinux2014_i686.manylinux_2_17_i686.manylinux_2_5_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-manylinux2014_armv7l.manylinux_2_17_armv7l.manylinux_2_31_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-manylinux2014_ppc64le.manylinux_2_17_ppc64le.manylinux_2_28_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-manylinux2014_s390x.manylinux_2_17_s390x.manylinux_2_28_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-musllinux_1_2_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-musllinux_1_2_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-musllinux_1_2_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-cp313-cp313t-win_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/multidict-6.6.3-py3-none-any.whl" },
]

[[package]]
name = "narwhals"
version = "1.48.1"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/narwhals-1.48.1.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/narwhals-1.48.1-py3-none-any.whl" },
]

[[package]]
name = "nbformat"
version = "5.10.4"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "fastjsonschema" },
    { name = "jsonschema" },
    { name = "jupyter-core" },
    { name = "traitlets" },
]
sdist = { url = "https://pypi.vnpy.com/packages/nbformat-5.10.4.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/nbformat-5.10.4-py3-none-any.whl" },
]

[[package]]
name = "numpy"
version = "2.3.2"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/numpy-2.3.2.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313-macosx_14_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313-macosx_14_0_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313-win_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313t-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313t-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313t-macosx_14_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313t-macosx_14_0_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313t-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313t-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313t-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313t-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313t-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313t-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp313-cp313t-win_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314-macosx_14_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314-macosx_14_0_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314-win_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314t-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314t-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314t-macosx_14_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314t-macosx_14_0_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314t-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314t-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314t-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314t-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314t-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314t-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/numpy-2.3.2-cp314-cp314t-win_arm64.whl" },
]

[[package]]
name = "packaging"
version = "25.0"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/packaging-25.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/packaging-25.0-py3-none-any.whl" },
]

[[package]]
name = "pandas"
version = "2.3.1"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "numpy" },
    { name = "python-dateutil" },
    { name = "pytz" },
    { name = "tzdata" },
]
sdist = { url = "https://pypi.vnpy.com/packages/pandas-2.3.1.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313t-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313t-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313t-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/pandas-2.3.1-cp313-cp313t-musllinux_1_2_x86_64.whl" },
]

[[package]]
name = "platformdirs"
version = "4.3.8"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/platformdirs-4.3.8.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/platformdirs-4.3.8-py3-none-any.whl" },
]

[[package]]
name = "plotly"
version = "6.2.0"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "narwhals" },
    { name = "packaging" },
]
sdist = { url = "https://pypi.vnpy.com/packages/plotly-6.2.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/plotly-6.2.0-py3-none-any.whl" },
]

[[package]]
name = "propcache"
version = "0.3.2"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/propcache-0.3.2.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-macosx_10_13_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-musllinux_1_2_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-musllinux_1_2_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-musllinux_1_2_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-macosx_10_13_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-musllinux_1_2_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-musllinux_1_2_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-musllinux_1_2_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-cp313-cp313t-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/propcache-0.3.2-py3-none-any.whl" },
]

[[package]]
name = "pycparser"
version = "2.22"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/pycparser-2.22.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/pycparser-2.22-py3-none-any.whl" },
]

[[package]]
name = "pyqtgraph"
version = "0.13.7"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "numpy" },
]
sdist = { url = "https://pypi.vnpy.com/packages/pyqtgraph-0.13.7.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/pyqtgraph-0.13.7-py3-none-any.whl" },
]

[[package]]
name = "pyside6"
version = "6.8.2.1"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "pyside6-addons" },
    { name = "pyside6-essentials" },
    { name = "shiboken6" },
]
wheels = [
    { url = "https://pypi.vnpy.com/packages/PySide6-6.8.2.1-cp39-abi3-macosx_12_0_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/PySide6-6.8.2.1-cp39-abi3-manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/PySide6-6.8.2.1-cp39-abi3-manylinux_2_39_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/PySide6-6.8.2.1-cp39-abi3-win_amd64.whl" },
]

[[package]]
name = "pyside6-addons"
version = "6.8.2.1"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "pyside6-essentials" },
    { name = "shiboken6" },
]
wheels = [
    { url = "https://pypi.vnpy.com/packages/PySide6_Addons-6.8.2.1-cp39-abi3-macosx_12_0_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/PySide6_Addons-6.8.2.1-cp39-abi3-manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/PySide6_Addons-6.8.2.1-cp39-abi3-manylinux_2_39_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/PySide6_Addons-6.8.2.1-cp39-abi3-win_amd64.whl" },
]

[[package]]
name = "pyside6-essentials"
version = "6.8.2.1"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "shiboken6" },
]
wheels = [
    { url = "https://pypi.vnpy.com/packages/PySide6_Essentials-6.8.2.1-cp39-abi3-macosx_12_0_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/PySide6_Essentials-6.8.2.1-cp39-abi3-manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/PySide6_Essentials-6.8.2.1-cp39-abi3-manylinux_2_39_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/PySide6_Essentials-6.8.2.1-cp39-abi3-win_amd64.whl" },
]

[[package]]
name = "python-dateutil"
version = "2.9.0.post0"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "six" },
]
sdist = { url = "https://pypi.vnpy.com/packages/python-dateutil-2.9.0.post0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/python_dateutil-2.9.0.post0-py2.py3-none-any.whl" },
]

[[package]]
name = "pytz"
version = "2025.2"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/pytz-2025.2.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/pytz-2025.2-py2.py3-none-any.whl" },
]

[[package]]
name = "pywin32"
version = "311"
source = { registry = "https://pypi.vnpy.com/simple" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/pywin32-311-cp313-cp313-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/pywin32-311-cp313-cp313-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/pywin32-311-cp313-cp313-win_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/pywin32-311-cp314-cp314-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/pywin32-311-cp314-cp314-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/pywin32-311-cp314-cp314-win_arm64.whl" },
]

[[package]]
name = "pyzmq"
version = "27.0.0"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "cffi", marker = "implementation_name == 'pypy'" },
]
sdist = { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp312-abi3-macosx_10_15_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp312-abi3-manylinux2014_i686.manylinux_2_17_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp312-abi3-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp312-abi3-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp312-abi3-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp312-abi3-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp312-abi3-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp312-abi3-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp312-abi3-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp312-abi3-win_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp313-cp313t-macosx_10_15_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp313-cp313t-manylinux2014_i686.manylinux_2_17_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp313-cp313t-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp313-cp313t-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp313-cp313t-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp313-cp313t-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp313-cp313t-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp313-cp313t-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/pyzmq-27.0.0-cp313-cp313t-win_amd64.whl" },
]

[[package]]
name = "qdarkstyle"
version = "3.2.3"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "qtpy" },
]
sdist = { url = "https://pypi.vnpy.com/packages/QDarkStyle-3.2.3.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/QDarkStyle-3.2.3-py2.py3-none-any.whl" },
]

[[package]]
name = "qtpy"
version = "2.4.3"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "packaging" },
]
sdist = { url = "https://pypi.vnpy.com/packages/qtpy-2.4.3.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/QtPy-2.4.3-py3-none-any.whl" },
]

[[package]]
name = "redis"
version = "6.2.0"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/redis-6.2.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/redis-6.2.0-py3-none-any.whl" },
]

[[package]]
name = "referencing"
version = "0.36.2"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "attrs" },
    { name = "rpds-py" },
]
sdist = { url = "https://pypi.vnpy.com/packages/referencing-0.36.2.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/referencing-0.36.2-py3-none-any.whl" },
]

[[package]]
name = "requests"
version = "2.32.4"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "certifi" },
    { name = "charset-normalizer" },
    { name = "idna" },
    { name = "urllib3" },
]
sdist = { url = "https://pypi.vnpy.com/packages/requests-2.32.4.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/requests-2.32.4-py3-none-any.whl" },
]

[[package]]
name = "rpds-py"
version = "0.26.0"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-macosx_10_12_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-manylinux_2_17_armv7l.manylinux2014_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313-win_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-macosx_10_12_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-manylinux_2_17_armv7l.manylinux2014_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp313-cp313t-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-macosx_10_12_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-manylinux_2_17_armv7l.manylinux2014_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-manylinux_2_5_i686.manylinux1_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314-win_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-macosx_10_12_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-manylinux_2_17_armv7l.manylinux2014_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-manylinux_2_5_i686.manylinux1_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/rpds_py-0.26.0-cp314-cp314t-win_amd64.whl" },
]

[[package]]
name = "setuptools"
version = "80.9.0"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/setuptools-80.9.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/setuptools-80.9.0-py3-none-any.whl" },
]

[[package]]
name = "shiboken6"
version = "6.8.2.1"
source = { registry = "https://pypi.vnpy.com/simple" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/shiboken6-6.8.2.1-cp39-abi3-macosx_12_0_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/shiboken6-6.8.2.1-cp39-abi3-manylinux_2_28_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/shiboken6-6.8.2.1-cp39-abi3-manylinux_2_39_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/shiboken6-6.8.2.1-cp39-abi3-win_amd64.whl" },
]

[[package]]
name = "six"
version = "1.17.0"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/six-1.17.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/six-1.17.0-py2.py3-none-any.whl" },
]

[[package]]
name = "ta-lib"
version = "0.6.4"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "numpy" },
    { name = "setuptools" },
]
sdist = { url = "https://pypi.vnpy.com/packages/ta_lib-0.6.4.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/ta_lib-0.6.4-cp313-cp313-win_amd64.whl" },
]

[[package]]
name = "tqdm"
version = "4.67.1"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "colorama", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://pypi.vnpy.com/packages/tqdm-4.67.1.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/tqdm-4.67.1-py3-none-any.whl" },
]

[[package]]
name = "traitlets"
version = "5.14.3"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/traitlets-5.14.3.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/traitlets-5.14.3-py3-none-any.whl" },
]

[[package]]
name = "tzdata"
version = "2025.2"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/tzdata-2025.2.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/tzdata-2025.2-py2.py3-none-any.whl" },
]

[[package]]
name = "tzlocal"
version = "5.3.1"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "tzdata", marker = "sys_platform == 'win32'" },
]
sdist = { url = "https://pypi.vnpy.com/packages/tzlocal-5.3.1.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/tzlocal-5.3.1-py3-none-any.whl" },
]

[[package]]
name = "urllib3"
version = "2.5.0"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/urllib3-2.5.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/urllib3-2.5.0-py3-none-any.whl" },
]

[[package]]
name = "vnpy"
version = "4.1.0"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "deap" },
    { name = "loguru" },
    { name = "nbformat" },
    { name = "numpy" },
    { name = "pandas" },
    { name = "plotly" },
    { name = "pyqtgraph" },
    { name = "pyside6" },
    { name = "pyzmq" },
    { name = "qdarkstyle" },
    { name = "ta-lib" },
    { name = "tqdm" },
    { name = "tzlocal" },
]
sdist = { url = "https://pypi.vnpy.com/packages/vnpy-4.1.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/vnpy-4.1.0-py3-none-any.whl" },
]

[[package]]
name = "vnpy-ctp"
version = "6.7.7.2"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "vnpy" },
]
sdist = { url = "https://pypi.vnpy.com/packages/vnpy_ctp-6.7.7.2.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/vnpy_ctp-6.7.7.2-cp313-cp313-win_amd64.whl" },
]

[[package]]
name = "win32-setctime"
version = "1.2.0"
source = { registry = "https://pypi.vnpy.com/simple" }
sdist = { url = "https://pypi.vnpy.com/packages/win32_setctime-1.2.0.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/win32_setctime-1.2.0-py3-none-any.whl" },
]

[[package]]
name = "yarl"
version = "1.20.1"
source = { registry = "https://pypi.vnpy.com/simple" }
dependencies = [
    { name = "idna" },
    { name = "multidict" },
    { name = "propcache" },
]
sdist = { url = "https://pypi.vnpy.com/packages/yarl-1.20.1.tar.gz" }
wheels = [
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-macosx_10_13_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-musllinux_1_2_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-musllinux_1_2_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-musllinux_1_2_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-macosx_10_13_universal2.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-macosx_10_13_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-macosx_11_0_arm64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-manylinux_2_17_aarch64.manylinux2014_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-manylinux_2_17_armv7l.manylinux2014_armv7l.manylinux_2_31_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-manylinux_2_17_s390x.manylinux2014_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-manylinux_2_17_x86_64.manylinux2014_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-musllinux_1_2_aarch64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-musllinux_1_2_armv7l.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-musllinux_1_2_i686.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-musllinux_1_2_ppc64le.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-musllinux_1_2_s390x.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-musllinux_1_2_x86_64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-win32.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-cp313-cp313t-win_amd64.whl" },
    { url = "https://pypi.vnpy.com/packages/yarl-1.20.1-py3-none-any.whl" },
]
