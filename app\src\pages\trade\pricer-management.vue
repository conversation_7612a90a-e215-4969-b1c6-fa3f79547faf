<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "我的交易请求",
    "enablePullDownRefresh": true,
    "backgroundTextStyle": "dark",
    "onReachBottomDistance": 50,
    "backgroundColor": "#f7f8fa",
    "pullDownRefresh": {
      "color": "#409eff",
      "offset": 80
    }
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad, onPullDownRefresh, onReachBottom } from '@dcloudio/uni-app'
import type { ITradeRequest, TradeRequestStatus } from '@/types/trade-request'
import { getMyTradeRequestsAsPricer, cancelTradeRequest } from '@/api/traderequest'
import { toast } from '@/utils/toast'
import TradeRequestList from '@/components/TradeRequestList.vue'

defineOptions({
  name: 'PricerManagementPage',
})

// 响应式状态
const tradeRequests = ref<ITradeRequest[]>([])
const loading = ref(false)
const refreshing = ref(false)
const loadingMore = ref(false)
const statusFilter = ref<TradeRequestStatus | ''>('')
const viewMode = ref<'management' | 'viewer'>('management')

// 分页状态
const page = ref(1)
const pageSize = computed(() => viewMode.value === 'viewer' ? 20 : 50)
const hasMore = ref(true)

// 状态筛选选项
const statusOptions = ref([
  { label: '全部', value: '' },
  { label: '执行中', value: 'Executing' },
  { label: '已完成', value: 'Completed' },
  { label: '已拒绝', value: 'Rejected' },
  { label: '已取消', value: 'Cancelled' },
  { label: '已过期', value: 'Expired' }
])

// 计算属性



const loadMoreText = computed(() => {
  if (loadingMore.value) {
    return '加载中...'
  }
  if (!hasMore.value) {
    return '没有更多数据了'
  }
  return '上拉加载更多'
})

const currentMode = computed(() => {
  return viewMode.value === 'viewer' ? 'viewer' : 'pricer'
})

const pageTitle = computed(() => {
  return viewMode.value === 'viewer' ? '交易请求查看' : '我的交易请求'
})

// 加载交易请求列表
async function loadTradeRequests(isRefresh = false) {
  // 防止重复加载
  if ((loading.value || loadingMore.value) && !isRefresh) return

  // 如果是刷新，重置分页状态
  if (isRefresh) {
    refreshing.value = true
    page.value = 1
    hasMore.value = true
  } else if (page.value === 1) {
    loading.value = true
  } else if (viewMode.value === 'viewer') {
    loadingMore.value = true
  }
  
  try {
    const currentPage = isRefresh ? 1 : page.value
    const response = await getMyTradeRequestsAsPricer({
      status: statusFilter.value || undefined,
      page: currentPage,
      pageSize: pageSize.value
    })
    
    if (response.code === 0) {
      const newRequests = response.data.list
      
      if (isRefresh || page.value === 1) {
        // 刷新或首次加载，替换数据
        tradeRequests.value = newRequests
        if (isRefresh) {
          toast.success('刷新成功')
        }
      } else {
        // 加载更多，追加数据（仅在viewer模式下）
        tradeRequests.value.push(...newRequests)
      }
      
      // 更新分页状态（仅在viewer模式下）
      if (viewMode.value === 'viewer') {
        hasMore.value = newRequests.length === pageSize.value
        
        if (hasMore.value && !isRefresh && page.value > 1) {
          // 如果还有更多数据且不是刷新操作，页码递增
          page.value++
        }
      }
    } else {
      toast.error(response.msg || '获取交易请求失败')
    }
  } catch (error) {
    console.error('获取交易请求失败:', error)
    toast.error('网络错误')
  } finally {
    if (isRefresh) {
      refreshing.value = false
      // 确保停止下拉刷新
      setTimeout(() => {
        try {
          uni.stopPullDownRefresh()
        } catch (e) {
          console.warn('停止下拉刷新失败:', e)
        }
      }, 500) // 延迟停止，确保用户能看到刷新动画
    } else if (page.value === 1) {
      loading.value = false
    } else {
      loadingMore.value = false
    }
  }
}

// 加载更多数据（仅在viewer模式下）
async function loadMore() {
  if (viewMode.value !== 'viewer' || !hasMore.value || loadingMore.value || loading.value) return
  
  page.value++
  await loadTradeRequests()
}

// 处理状态筛选
function handleStatusFilter() {
  // 重置分页状态
  page.value = 1
  hasMore.value = true
  loadTradeRequests()
}

// 获取状态标签
function getStatusLabel(value: string) {
  const option = statusOptions.value.find(opt => opt.value === value)
  return option ? option.label : '全部'
}

// 处理下拉刷新
function handleRefresh() {
  loadTradeRequests(true)
}



// 处理取消操作
async function handleCancel(request: ITradeRequest) {
  try {
    // 显示确认对话框
    const confirmResult = await uni.showModal({
      title: '确认取消',
      content: `确定要取消这个${request.requestType === 'PointPrice' ? '点价' : '洗基差'}请求吗？`,
      confirmText: '确认取消',
      cancelText: '我再想想'
    })

    if (!confirmResult.confirm) {
      return
    }

    loading.value = true
    
    const response = await cancelTradeRequest(request.ID)
    
    if (response.code === 0) {
      toast.success('取消成功')
      // 自动刷新列表
      loadTradeRequests(true)
    } else {
      toast.error(response.msg || '取消失败')
    }
  } catch (error) {
    console.error('取消交易请求失败:', error)
    toast.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// 处理页面加载参数
onLoad((options) => {
  // 根据URL参数设置视图模式
  if (options?.mode === 'viewer') {
    viewMode.value = 'viewer'
  }

  // 根据模式设置页面标题
  uni.setNavigationBarTitle({
    title: pageTitle.value
  })
})

// 生命周期
onMounted(() => {
  loadTradeRequests()
})

// 处理下拉刷新
onPullDownRefresh(async () => {
  console.log('下拉刷新触发')
  try {
    await loadTradeRequests(true)
  } catch (error) {
    console.error('下拉刷新失败:', error)
  }
})

// 处理上拉加载更多（仅在viewer模式下）
onReachBottom(() => {
  if (viewMode.value === 'viewer') {
    loadMore()
  }
})
</script>

<template>
  <view class="pricer-management-page">
    <!-- 下拉刷新指示器 -->
    <view v-if="refreshing" class="refresh-indicator bg-blue-500 text-white text-center py-2 mb-3 rounded mx-3">
      正在刷新数据...
    </view>
    <!-- 筛选条件 -->
    <view class="filter-bar flex items-center bg-white rounded-lg shadow-sm px-3 py-2 mb-3 mx-3">
      <wd-select-picker
        v-model="statusFilter"
        :columns="statusOptions"
        placeholder="全部"
        title="选择状态"
        type="radio"
        :show-confirm="false"
        @change="handleStatusFilter"
        class="flex-1"
        use-default-slot
      >
        <view class="status-display">
          <text class="text-sm text-gray-600 mr-2">状态:</text>
          <text class="status-text">{{ getStatusLabel(statusFilter) }}</text>
        </view>
      </wd-select-picker>
    </view>

    <!-- 交易请求列表 -->
    <view class="px-3">
      <TradeRequestList
        :requests="tradeRequests"
        :loading="loading"
        :refreshing="refreshing"
        :mode="currentMode"
        @cancel="handleCancel"
        @refresh="handleRefresh"
      />
    </view>

    <!-- 加载更多提示（仅在viewer模式下显示） -->
    <view v-if="viewMode === 'viewer' && tradeRequests.length > 0" class="load-more-tip text-center py-4">
      <view v-if="loadingMore" class="flex justify-center items-center">
        <view class="loading-spinner w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-2"></view>
        <text class="text-sm text-gray-500">{{ loadMoreText }}</text>
      </view>
      <text v-else class="text-xs text-gray-400">{{ loadMoreText }}</text>
    </view>


  </view>
</template>

<style lang="scss" scoped>
.pricer-management-page {
  background-color: #f7f8fa;
}

.load-more-tip {
  .loading-spinner {
    width: 32rpx;
    height: 32rpx;
    border: 2rpx solid #409eff;
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
}

.empty-state {
  .empty-icon {
    width: 128rpx;
    height: 128rpx;
    opacity: 0.3;
  }
}

.filter-bar {
  .status-display {
    padding: 8rpx 16rpx;
    background-color: #f8f9fa;
    border-radius: 8rpx;
    border: 1rpx solid #e9ecef;

    .status-text {
      color: #495057;
      font-size: 28rpx;
    }
  }
}
</style>