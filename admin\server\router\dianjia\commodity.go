package dianjia

import (
	"github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/gin-gonic/gin"
)

type CommodityRouter struct{}

// InitCommodityRouter 初始化 商品 路由信息
func (s *CommodityRouter) InitCommodityRouter(Router *gin.RouterGroup) {
	commodityRouter := Router.Group("commodity")
	commodityApi := v1.ApiGroupApp.DianjiaApiGroup.CommodityApi
	{
		commodityRouter.POST("createCommodity", commodityApi.CreateCommodity)     // 新建商品
		commodityRouter.DELETE("deleteCommodity", commodityApi.DeleteCommodity)   // 删除商品
		commodityRouter.DELETE("deleteCommodityByIds", commodityApi.DeleteCommodityByIds) // 批量删除商品
		commodityRouter.PUT("updateCommodity", commodityApi.UpdateCommodity)      // 更新商品
		commodityRouter.GET("findCommodity", commodityApi.FindCommodity)          // 根据ID获取商品
		commodityRouter.GET("getCommodityList", commodityApi.GetCommodityList)    // 获取商品列表
		commodityRouter.GET("getAllCommodityList", commodityApi.GetAllCommodityList) // 获取所有商品列表
	}
}