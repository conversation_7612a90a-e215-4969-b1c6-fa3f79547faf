package dianjia

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// Instrument 期货合约表 - 具体的合约信息
type Instrument struct {
	global.GVA_MODEL
	ExchangeID        string     `json:"exchange_id" gorm:"comment:交易所ID;size:10;not null" validate:"required"`
	ProductID         string     `json:"product_id" gorm:"comment:产品ID;size:10;not null" validate:"required"`
	ProductName       string     `json:"product_name" gorm:"comment:产品名称;size:50;not null" validate:"required"`
	InstrumentID      string     `json:"instrument_id" gorm:"comment:合约ID;size:30;not null;uniqueIndex" validate:"required"`
	InstrumentName    string     `json:"instrument_name" gorm:"comment:合约名称;size:50;not null" validate:"required"`
	ProductClass      string     `json:"product_class" gorm:"comment:商品类别;size:1;not null;default:'1'" validate:"required"`
	VolumeMultiple    int        `json:"volume_multiple" gorm:"comment:合约乘数;not null" validate:"required"`
	PriceTick         float64    `json:"price_tick" gorm:"comment:最小变动价位;type:decimal(10,4);not null" validate:"required"`
	LongMarginRatio   *float64   `json:"long_margin_ratio" gorm:"comment:做多保证金率;type:decimal(10,6)"`
	ShortMarginRatio  *float64   `json:"short_margin_ratio" gorm:"comment:做空保证金率;type:decimal(10,6)"`
	OpenRatioByMoney  *float64   `json:"open_ratio_by_money" gorm:"comment:开仓手续费率;type:decimal(10,8)"`
	CloseRatioByMoney *float64   `json:"close_ratio_by_money" gorm:"comment:平仓手续费率;type:decimal(10,8)"`
	CloseTodayRatio   *float64   `json:"close_today_ratio" gorm:"comment:平今手续费率;type:decimal(10,8)"`
	DeliveryYear      *int       `json:"delivery_year" gorm:"comment:交割年份"`
	DeliveryMonth     *int       `json:"delivery_month" gorm:"comment:交割月份"`
	OpenDate          *time.Time `json:"open_date" gorm:"comment:上市日期"`
	ExpireDate        *time.Time `json:"expire_date" gorm:"comment:最后交易日"`
	InstLifePhase     string     `json:"inst_life_phase" gorm:"comment:合约状态;size:1;not null;default:'0'"`
	CreatedAt         time.Time  `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt         time.Time  `json:"updated_at" gorm:"comment:更新时间"`
}

// TableName 指定表名
func (Instrument) TableName() string {
	return "instruments"
}

// InstrumentRequest 创建和更新期货合约的请求结构
type InstrumentRequest struct {
	ID                uint       `json:"id"`
	ExchangeID        string     `json:"exchange_id" validate:"required" label:"交易所ID"`
	ProductID         string     `json:"product_id" validate:"required" label:"产品ID"`
	ProductName       string     `json:"product_name" validate:"required" label:"产品名称"`
	InstrumentID      string     `json:"instrument_id" validate:"required" label:"合约ID"`
	InstrumentName    string     `json:"instrument_name" validate:"required" label:"合约名称"`
	ProductClass      string     `json:"product_class" validate:"required" label:"商品类别"`
	VolumeMultiple    int        `json:"volume_multiple" validate:"required" label:"合约乘数"`
	PriceTick         float64    `json:"price_tick" validate:"required" label:"最小变动价位"`
	LongMarginRatio   *float64   `json:"long_margin_ratio" label:"做多保证金率"`
	ShortMarginRatio  *float64   `json:"short_margin_ratio" label:"做空保证金率"`
	OpenRatioByMoney  *float64   `json:"open_ratio_by_money" label:"开仓手续费率"`
	CloseRatioByMoney *float64   `json:"close_ratio_by_money" label:"平仓手续费率"`
	CloseTodayRatio   *float64   `json:"close_today_ratio" label:"平今手续费率"`
	DeliveryYear      *int       `json:"delivery_year" label:"交割年份"`
	DeliveryMonth     *int       `json:"delivery_month" label:"交割月份"`
	OpenDate          *time.Time `json:"open_date" label:"上市日期"`
	ExpireDate        *time.Time `json:"expire_date" label:"最后交易日"`
	InstLifePhase     string     `json:"inst_life_phase" validate:"required" label:"合约状态"`
}

// InstrumentResponse 期货合约响应结构
type InstrumentResponse struct {
	ID                uint       `json:"id"`
	ExchangeID        string     `json:"exchange_id"`
	ProductID         string     `json:"product_id"`
	ProductName       string     `json:"product_name"`
	InstrumentID      string     `json:"instrument_id"`
	InstrumentName    string     `json:"instrument_name"`
	ProductClass      string     `json:"product_class"`
	ProductClassName  string     `json:"product_class_name,omitempty"`
	VolumeMultiple    int        `json:"volume_multiple"`
	PriceTick         float64    `json:"price_tick"`
	LongMarginRatio   *float64   `json:"long_margin_ratio"`
	ShortMarginRatio  *float64   `json:"short_margin_ratio"`
	OpenRatioByMoney  *float64   `json:"open_ratio_by_money"`
	CloseRatioByMoney *float64   `json:"close_ratio_by_money"`
	CloseTodayRatio   *float64   `json:"close_today_ratio"`
	DeliveryYear      *int       `json:"delivery_year"`
	DeliveryMonth     *int       `json:"delivery_month"`
	OpenDate          *time.Time `json:"open_date"`
	ExpireDate        *time.Time `json:"expire_date"`
	InstLifePhase     string     `json:"inst_life_phase"`
	InstLifePhaseName string     `json:"inst_life_phase_name,omitempty"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
}

// InstrumentListRequest 期货合约列表查询请求
type InstrumentListRequest struct {
	Page           int    `json:"page" form:"page"`
	PageSize       int    `json:"pageSize" form:"pageSize"`
	InstrumentID   string `json:"instrument_id" form:"instrument_id"`
	InstrumentName string `json:"instrument_name" form:"instrument_name"`
	ExchangeID     string `json:"exchange_id" form:"exchange_id"`
	ProductID      string `json:"product_id" form:"product_id"`
	ProductClass   string `json:"product_class" form:"product_class"`
	InstLifePhase  string `json:"inst_life_phase" form:"inst_life_phase"`
}

// InstrumentListResponse 期货合约列表响应
type InstrumentListResponse struct {
	List     []InstrumentResponse `json:"list"`
	Total    int64                `json:"total"`
	Page     int                  `json:"page"`
	PageSize int                  `json:"pageSize"`
}

// InstrumentSelectRequest 期货合约选择器请求
type InstrumentSelectRequest struct {
	ExchangeID string `json:"exchange_id" form:"exchange_id"`
	ProductID  string `json:"product_id" form:"product_id"`
	Keyword    string `json:"keyword" form:"keyword"`
	LifePhase  string `json:"life_phase" form:"life_phase"`
}

// InstrumentSelectResponse 期货合约选择器响应
type InstrumentSelectResponse struct {
	ID             uint   `json:"id"`
	InstrumentID   string `json:"instrument_id"`
	InstrumentName string `json:"instrument_name"`
	ProductName    string `json:"product_name"`
	ExchangeID     string `json:"exchange_id"`
}

// ProductClassMap 商品类别映射
var ProductClassMap = map[string]string{
	"1": "期货",
	"2": "期权",
	"3": "组合",
	"4": "即期",
	"5": "期转现",
	"6": "现货期权",
	"7": "TAS合约",
	"8": "金属指数",
	"9": "期货期权",
}

// InstLifePhaseMap 合约状态映射
var InstLifePhaseMap = map[string]string{
	"0": "未上市",
	"1": "上市",
	"2": "停牌",
	"3": "到期",
}
