# ContractCard 组件

一个可复用的合同卡片展示组件，用于在不同页面中展示合同信息。

## 功能特点

- 📱 响应式设计，适配移动端
- 🎨 统一的UI风格和交互体验
- 🔄 根据用户角色动态显示对应的用户名
- 🎯 支持插槽自定义操作按钮
- 📊 完整的合同信息展示（状态、类型、价格、数量等）

## Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| contract | IContract | 是 | - | 合同数据对象 |
| userRole | 'setter' \| 'pricer' | 是 | - | 用户角色，决定显示哪个用户名 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| click | contract: IContract | 点击卡片时触发 |

## Slots

| 插槽名 | 参数 | 说明 |
|--------|------|------|
| actions | { contract: IContract } | 操作按钮区域，可自定义按钮 |

## 使用示例

### 基础用法

```vue
<template>
  <ContractCard
    :contract="contract"
    user-role="setter"
    @click="handleCardClick"
  />
</template>

<script setup>
import ContractCard from '@/components/ContractCard.vue'

function handleCardClick(contract) {
  console.log('点击了合同:', contract.contractCode)
}
</script>
```

### 带操作按钮

```vue
<template>
  <ContractCard
    :contract="contract"
    user-role="setter"
    @click="handleCardClick"
  >
    <template #actions="{ contract }">
      <view class="contract-actions" @click.stop>
        <wd-button
          v-if="contract.status === 'Unexecuted'"
          type="success"
          size="small"
          @click="activateContract(contract.ID)"
        >
          激活
        </wd-button>
        <wd-button
          v-if="contract.status === 'Executing'"
          type="warning"
          size="small"
          @click="deactivateContract(contract.ID)"
        >
          挂起
        </wd-button>
      </view>
    </template>
  </ContractCard>
</template>
```

### 不同角色的使用

```vue
<!-- setter角色查看时，显示pricer名称 -->
<ContractCard
  :contract="contract"
  user-role="setter"
  @click="goToDetail"
/>

<!-- pricer角色查看时，显示setter名称 -->
<ContractCard
  :contract="contract"
  user-role="pricer"
  @click="goToDetail"
/>
```

## 显示逻辑

### 用户名显示规则

- `userRole="setter"`: 显示点价方(pricer)的名称
- `userRole="pricer"`: 显示被点价方(setter)的名称

### 价格显示格式

- 基差合同: `基差 +100` 或 `基差 -50`
- 固定价合同: `固定价 3500`

### 状态标签颜色

- 未执行(Unexecuted): 警告色(warning)
- 执行中(Executing): 成功色(success)  
- 已完成(Completed): 信息色(info)
- 已取消(Cancelled): 危险色(danger)

### 合同类型标签

- 基差合同: 主色调(primary)
- 固定价合同: 成功色(success)

## 样式特点

- **现代卡片设计**: 16rpx圆角，精致阴影效果，顶部渐变装饰条
- **交互动效**: hover悬浮效果，点击反馈动画
- **信息层级清晰**: 合同编号突出显示，标签分组明确
- **价格突出显示**: 橙色渐变背景，居中显示，带阴影效果
- **数量信息网格**: 四列布局（总量、剩余、冻结、可用），hover交互
- **冻结数量警告**: 红色高亮显示冻结相关信息
- **备注样式**: 左侧蓝色边框，斜体文字，单行省略
- **操作按钮区**: 顶部分割线，右对齐，按钮圆角设计
- **响应式布局**: 适配不同屏幕尺寸，文字溢出处理

## 样式统一说明

所有的卡片样式都已经集中在ContractCard组件中，确保了：

1. **统一的视觉体验**: setter-list.vue和pricer-list.vue现在显示完全一致的卡片样式
2. **维护便利性**: 样式修改只需在一个地方进行
3. **代码复用**: 避免了重复的CSS代码
4. **设计一致性**: 所有使用该组件的页面都保持相同的设计语言

原来的页面样式差异已经消除，现在两个页面都使用相同的高质量卡片设计。这个组件现在可以在任何需要显示合同卡片的地方复用，只需要传入正确的`userRole`参数即可自动显示对应的用户名称。
