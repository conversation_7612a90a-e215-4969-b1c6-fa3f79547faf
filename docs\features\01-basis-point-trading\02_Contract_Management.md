# 02 - 合同管理与操作 (Contract Management and Operations) (V3)

## 1. 功能简介 (Feature Introduction)

本模块基于合同（contracts）核心模型，为用户提供基差点价和一口价洗基差的合同管理平台。
- 合同（contracts）定义了交易的总量、价格类型、参与方等核心条款。
- **核心逻辑 (V3)**: 引入了严格的状态机 (`Unexecuted`, `Executing`)。被点价方（Setter）负责维护合同状态，将其作为可供交易的“资源池”。交易请求不再直接关联单个合同。

---

## 2. 数据定义（合同表结构）

### 2.1. contracts - 核心合同表

| 字段名 | 类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| id | BIGINT | PK, AI | 唯一ID |
| contract_code | VARCHAR(255) | NOT NULL | 合同业务编码 |
| setter_id | BIGINT | NOT NULL, FK | 被点价方（创建者） |
| pricer_id | BIGINT | NOT NULL, FK | 点价方（操作者） |
| instrument_ref_id | BIGINT | NOT NULL, FK | 关联的期货合约ID |
| remarks | TEXT | | 备注信息 |
| **total_quantity** | INT | NOT NULL | **合同总数量** |
| **remaining_quantity**| INT | NOT NULL | **合同剩余可执行数量** |
| **frozen_quantity** | INT | NOT NULL, DEFAULT 0 | **冻结数量**（由系统自动匹配并冻结） |
| **price_type** | VARCHAR(50) | NOT NULL | **价格类型** (`basis`: 基差合同, `fixed`: 固定价合同) |
| **price_value** | DECIMAL(10,2) | | **价格/基差值** (根据 `price_type` 决定) |
| **status** | VARCHAR(50) | NOT NULL | **合同状态** (`Unexecuted`, `Executing`, `Completed`, `Cancelled`) |
| created_at | TIMESTAMP | | 创建时间 |
| updated_at | TIMESTAMP | | 更新时间 |

#### 2.1.1. 数据约束与计算规则
- `frozen_quantity >= 0`
- `frozen_quantity <= remaining_quantity`
- **可用数量 (Available Quantity)** = `remaining_quantity - frozen_quantity`

---

## 3. 合同核心业务逻辑 (V3)

### 3.1. 合同创建
- **来源**: 
1. 由被点价方（Setter）在系统中手动创建。
2. 报价订单，由查看着申请，发布者同意申请者的申请，系统自动生成合同
3. 交易执行，由订单生成合同
  - 基差合同 通过点价生成 固定价合同
  - 固定价合同 通过洗基差 生成基差合同
- **初始状态**: 所有新创建的合同，其状态默认为 **`Unexecuted` (未执行)**。

### 3.2. 合同冻结与解冻机制
- **冻结时机**: 当一笔交易请求被审核通过后，由**系统自动匹配**并冻结一个或多个合同的数量。
1. 系统获取所有的可执行的合同，从中按照顺序获取可用的数量。例如可用数量是 【2,5,5】
2. 如果请求数量大于12，大于可用数量之和， 返回错误信息。
3. 如果请求数量是10， 那么冻结【2,5,3】剩余的可用数量是[0,0,2] 
- **解冻时机**:
  - **交易请求成交时**: `remaining_quantity` 和 `frozen_quantity` 同时扣减。
  - **交易请求失败/取消时**: 仅归还 `frozen_quantity`。
  同样按照数量排序归还解冻。

### 3.3. 合同取消机制
- **核心约束**: 合同只能在 **`Unexecuted` (未执行)** 状态下被取消。
- **操作者**: 仅被点价方（Setter）。
- **部分取消**:
  - Setter可以输入一个数量进行部分取消。
  - 系统扣减 `total_quantity` 和 `remaining_quantity`。
  - 合同状态**保持 `Unexecuted`**。
- **完全取消**:
  - 当取消数量等于 `remaining_quantity` 时，合同状态变为 `Cancelled`。

---

## 4. 合同状态流转与权限 (V3)

### 4.1. 合同状态定义
- **`Unexecuted` (未执行)**: 合同创建后的默认状态。可被激活或取消，但不能用于交易。
- **`Executing` (执行中)**: 合同已激活，成为系统可匹配的资源之一。
- **`Completed` (已完成)**: 合同的 `remaining_quantity` 已全部被消耗，流程结束。
- **`Cancelled` (已取消)**: 合同在 `Unexecuted` 状态下被完全取消，流程结束。

### 4.2. 状态转换图
```mermaid
graph TD
    direction LR
    A[Unexecuted] -- 被点价方激活 --> B(Executing);
    B -- 被点价方挂起 (无冻结时) --> A;
    A -- 被点价方取消 --> D(Cancelled);
    B -- 系统匹配消耗完毕 --> C(Completed);
```

### 4.3. 权限控制矩阵

| 合同状态 | 角色 | 查看详情 | 激活/挂起 | 取消(部分/全部) |
| :--- | :--- | :---: | :---: | :---: |
| **Unexecuted** | 被点价方 | ✓ | ✓ (激活) | ✓ |
| **Unexecuted** | 点价方 | ✓ | ✗ | ✗ |
| **Executing** | 被点价方 | ✓ | ✓ (挂起, 无冻结时) | ✗ |
| **Executing** | 点价方 | ✓ | ✗ | ✗ |
| **Completed** | 双方 | ✓ | ✗ | ✗ |
| **Cancelled** | 双方 | ✓ | ✗ | ✗ |

---

## 5. App端页面实现 (App Page Implementation)

### 5.1. 被点价方 (Setter) - 合同管理页面 (`@/pages/contract/setter-list.vue`)
- **核心功能**: 提供完整的合同CRUD功能，管理合同“资源池”。
- **UI要点**:
  - 列表清晰展示合同核心信息及状态 (`Unexecuted`, `Executing` 等)。
  - 根据合同状态和 `frozen_quantity` 动态显示或禁用操作按钮（激活、挂起、取消）。

### 5.2. 点价方 (Pricer) - 合同中心页面 (`@/pages/contract/pricer-list.vue`)
- **核心功能**: 仅作为信息展示，显示所有与自己相关的合同（所有状态）。点价方不再从此页面发起交易。

---

## 6. API 接口定义 (API Endpoints) (V3)

### 6.1. 合同查询
- **`GET /dianjia/contracts/as-setter`**: 获取当前用户作为被点价方的合同列表。
- **`GET /dianjia/contracts/as-pricer`**: 获取当前用户作为点价方的合同列表。
- **`GET /dianjia/contract/{id}`**: 获取单个合同详情。只能查看自的的合同 setter=selfid 或者 pricer=selfid

### 6.2. 合同操作 (被点价方)
- **`POST /dianjia/contract`**: 创建新合同 (初始状态为 `Unexecuted`)。
- **`PUT /dianjia/contract/{id}`**: 更新合同 (仅限 `Unexecuted` 状态)。
- **`DELETE /dianjia/contract/{id}`**: 删除合同 (仅限 `Unexecuted` 状态)。
- **`POST /dianjia/contract/{id}/activate`**: 激活合同 (状态变为 `Executing`)。
- **`POST /dianjia/contract/{id}/deactivate`**: 挂起合同 (状态变为 `Unexecuted`)。
- **`POST /dianjia/contract/{id}/cancel`**: 部分或全部取消合同。
  - **Body**: `{ "cancel_quantity": 30 }`

---

## 7. 合同相关测试用例 (V3)

| 场景 | 步骤 | 预期结果 |
| :--- | :--- | :--- |
| **状态流转与权限** | 1. Setter创建合同。<br>2. Setter激活合同。<br>3. 系统匹配交易，冻结数量。<br>4. Setter尝试挂起。<br>5. 交易失败，解冻。<br>6. Setter挂起合同。 | 1. 状态为 `Unexecuted`。<br>2. 状态变为 `Executing`。<br>3. `frozen_quantity` > 0。<br>4. 挂起失败。<br>5. `frozen_quantity` = 0。<br>6. 挂起成功，状态变回 `Unexecuted`。 |
| **取消逻辑** | 1. Setter创建100手合同。<br>2. Setter激活合同后尝试取消。<br>3. Setter挂起合同。<br>4. Setter部分取消30手。<br>5. Setter取消剩余70手。 | 2. 取消失败。<br>3. 状态变为 `Unexecuted`。<br>4. `remaining_quantity`=70, 状态仍为 `Unexecuted`。<br>5. 状态变为 `Cancelled`。 |
