#!/usr/bin/env python3
"""
样式覆盖测试脚本

测试登录窗口样式覆盖主窗口样式的功能
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QLineEdit, QMenuBar, QStatusBar, QToolBar
from PySide6.QtCore import QTimer

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_style_override():
    """测试样式覆盖功能"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 导入必要的模块
        from vnpy.event import EventEngine
        from ui.login.login_window import LoginWindow
        from ui.styles.login_styles import LoginStyles
        
        # 创建事件引擎
        event_engine = EventEngine()
        
        # 创建一个模拟的主窗口
        main_window = QMainWindow()
        main_window.setWindowTitle("主窗口 - 原始样式")
        main_window.resize(800, 600)
        
        # 添加一些组件到主窗口
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        
        # 添加各种组件
        label = QLabel("这是主窗口的标签")
        line_edit = QLineEdit("这是输入框")
        button = QPushButton("这是按钮")
        
        layout.addWidget(label)
        layout.addWidget(line_edit)
        layout.addWidget(button)
        
        main_window.setCentralWidget(central_widget)
        
        # 添加菜单栏
        menu_bar = main_window.menuBar()
        file_menu = menu_bar.addMenu("文件")
        file_menu.addAction("新建")
        file_menu.addAction("打开")
        
        # 添加工具栏
        tool_bar = main_window.addToolBar("工具栏")
        tool_bar.addAction("工具1")
        tool_bar.addAction("工具2")
        
        # 添加状态栏
        status_bar = main_window.statusBar()
        status_bar.showMessage("准备就绪")
        
        # 显示主窗口（使用原始样式）
        main_window.show()
        
        # 创建登录窗口
        login_window = LoginWindow(event_engine, None, None)
        
        # 创建样式管理器
        login_styles = LoginStyles()
        
        # 创建测试按钮
        test_button = QPushButton("应用登录样式到主窗口")
        def apply_login_styles():
            try:
                # 获取登录样式
                override_styles = login_styles.get_main_window_override_style()
                
                # 应用到主窗口
                main_window.setStyleSheet(override_styles)
                
                # 强制刷新
                main_window.style().unpolish(main_window)
                main_window.style().polish(main_window)
                main_window.update()
                
                print("✅ 登录样式已应用到主窗口")
                test_button.setText("样式已应用！")
                
            except Exception as e:
                print(f"❌ 应用样式失败：{str(e)}")
        
        test_button.clicked.connect(apply_login_styles)
        layout.addWidget(test_button)
        
        # 创建重置按钮
        reset_button = QPushButton("重置为原始样式")
        def reset_styles():
            main_window.setStyleSheet("")
            main_window.style().unpolish(main_window)
            main_window.style().polish(main_window)
            main_window.update()
            print("✅ 已重置为原始样式")
            test_button.setText("应用登录样式到主窗口")
        
        reset_button.clicked.connect(reset_styles)
        layout.addWidget(reset_button)
        
        # 显示登录窗口按钮
        show_login_button = QPushButton("显示登录窗口")
        def show_login():
            login_window.show_centered()
        
        show_login_button.clicked.connect(show_login)
        layout.addWidget(show_login_button)
        
        print("=" * 60)
        print("样式覆盖测试启动")
        print("=" * 60)
        print("测试功能:")
        print("1. 主窗口显示原始样式")
        print("2. 点击'应用登录样式'按钮测试样式覆盖")
        print("3. 点击'重置'按钮恢复原始样式")
        print("4. 点击'显示登录窗口'查看登录窗口样式")
        print()
        print("观察要点:")
        print("- 主窗口的背景、颜色、字体变化")
        print("- 菜单栏、工具栏、状态栏的样式变化")
        print("- 按钮、输入框的样式变化")
        print("- 登录窗口的独立样式")
        print("=" * 60)
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_style_override()
