<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "交易执行"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'

import type { TradeRequestType, ITradeRequest, ExecutionMode, TradeRequestStatus } from '@/types/trade-request'
import type { IContractResponse, ContractStatus } from '@/types/contract'
import type { IInstrument, IInstrumentSelectItem } from '@/types/instrument'
import { pointPrice, basisWash, getMyTradeRequestsAsPricer } from '@/api/traderequest'
import { toast } from '@/utils/toast'
import QuoteCard from '@/components/QuoteCard.vue'
import { useContractData } from '@/composables/useContractData'
import CombinationSelector from './CombinationSelector.vue'
import TradeOperationForm from './TradeOperationForm.vue'
import TradeRequestList from '@/components/TradeRequestList.vue'
import { useMarketStore } from '@/store/market'


defineOptions({
  name: 'TradeExecutePage',
})



// 使用 composables
const {
  availableContracts,
  instrumentId,
  setterID,
  setterName,
  instrument,
  currentContracts,
  availableCombinations,
  currentCombination,
  currentCombinationDisplay,
  pickerValue,
  getCurrentAvailableContracts,
  getContractStats,
  getContractStatsByType,
  loadInstrument,
  loadAllAvailableContracts,
  handleCombinationChange,
  resetContractData
} = useContractData()

// 响应式状态
const requestType = ref<TradeRequestType>('PointPrice')
const todayTradeRequests = ref<ITradeRequest[]>([])
const loading = ref(false)

// 市场数据 store
const marketStore = useMarketStore()

// 计算属性
const isPointPrice = computed(() => requestType.value === 'PointPrice')

// 使用 composable 中的统计数据计算
const currentContractStats = computed(() => getContractStatsByType(isPointPrice.value).value)

// 获取当前合约的行情数据
const marketData = computed(() => {
  const symbol = instrument.value?.instrument_id
  if (!symbol) return null
  return marketStore.getMarketData(symbol)
})

// 获取涨跌停价格
const limitUpPrice = computed(() => {
  return marketData.value ? parseFloat(marketData.value.limit_up) : undefined
})

const limitDownPrice = computed(() => {
  return marketData.value ? parseFloat(marketData.value.limit_down) : undefined
})

// 处理行情组件的价格点击事件
interface PriceClickEvent {
  type: 'last' | 'bid' | 'ask' | 'limit_up' | 'limit_down'
  price: number
  numericPrice: number
}

// TradeOperationForm 引用
const tradeFormRef = ref<InstanceType<typeof TradeOperationForm>>()

const handlePriceClick = (event: PriceClickEvent) => {
  // 点价和洗基差操作都支持价格点击填充
  if (tradeFormRef.value) {
    tradeFormRef.value.handlePriceClick(event.numericPrice)
    toast.success(`已选择${getPriceTypeName(event.type)}价格: ${event.price}`)
  }
}

const getPriceTypeName = (type: PriceClickEvent['type']) => {
  const typeNames = {
    last: '最新',
    bid: '买一',
    ask: '卖一',
    limit_up: '涨停',
    limit_down: '跌停'
  }
  return typeNames[type]
}

// 加载今日交易请求
async function loadTodayTradeRequests() {
  try {
    const today = new Date()

    // 格式化日期为 YYYY-MM-DD HH:mm:ss 格式，与后端期望的格式一致
    const formatDate = (date: Date) => {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }

    // 今天的开始时间：00:00:00
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
    // 今天的结束时间：23:59:59
    const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)

    const params = {
      instrumentRefID: instrumentId.value || undefined,
      startDate: formatDate(todayStart), // 今天 00:00:00
      endDate: formatDate(todayEnd)       // 今天 23:59:59
    }
    console.log('开始加载今日交易请求', params)
    console.log('日期范围:', {
      startDate: params.startDate,
      endDate: params.endDate,
      todayStart: todayStart.toISOString(),
      todayEnd: todayEnd.toISOString()
    })
    const response = await getMyTradeRequestsAsPricer(params)
    console.log('今日交易请求API响应:', response)
    if (response.code === 0) {
      // 客户端过滤：只显示当前被点价方的交易请求
      const filteredRequests = response.data.list.filter(request =>
        !setterID.value || request.setterID === setterID.value
      )
      todayTradeRequests.value = filteredRequests
      console.log('今日交易请求加载成功:', filteredRequests)
    } else {
      console.warn('今日交易请求API返回错误:', response.msg)
    }
  } catch (error) {
    console.error('加载今日交易请求失败:', error)
  }
}

// 处理交易请求提交
async function handleTradeRequestSubmit(data: { quantity: number; price?: number }) {
  if (!instrumentId.value) {
    toast.error('期货合约信息缺失')
    return
  }

  if (!setterID.value) {
    toast.error('被点价方信息缺失')
    return
  }

  loading.value = true
  
  try {
    let response: any

    const { quantity, price } = data

    console.log('发送交易请求参数:', {
      setterID: setterID.value,
      instrumentId: instrumentId.value,
      quantity,
      price,
      requestType: requestType.value
    })

    if (isPointPrice.value) {
      // 点价操作
      response = await pointPrice(
        setterID.value!,
        instrumentId.value!,
        quantity,
        price!,
        'MANUAL', // V4: 增加执行模式
        new Date(Date.now() + 3600 * 1000).toISOString() // V4: 增加1小时的过期时间
      )
    } else {
      // 洗基差操作
      response = await basisWash(
        setterID.value!,
        instrumentId.value!,
        quantity,
        price!,
        'MANUAL', // V4: 增加执行模式
        new Date(Date.now() + 3600 * 1000).toISOString() // V4: 增加1小时的过期时间
      )
    }

    if (response.code === 0) {
      const typeText = isPointPrice.value ? '点价' : '洗基差'
      toast.success(`${typeText}请求已提交`)
      
      // 重置表单
      if (tradeFormRef.value) {
        tradeFormRef.value.resetForm()
      }
      
      // 刷新今日交易请求列表
      loadTodayTradeRequests()
    } else {
      toast.error(response.msg || '提交失败')
    }
  } catch (error) {
    console.error('提交交易请求失败:', error)
    toast.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}


// Tab 切换函数
function switchTab(newRequestType: TradeRequestType) {
  console.log('切换 Tab:', newRequestType)
  requestType.value = newRequestType

  // 重置表单状态
  if (tradeFormRef.value) {
    tradeFormRef.value.resetForm()
  }

  // 重新加载今日交易请求
  loadTodayTradeRequests()
}

// 处理页面参数的函数
function handlePageParams(options: any) {
  console.log('处理页面参数:', options)

  // 重置表单状态
  if (tradeFormRef.value) {
    tradeFormRef.value.resetForm()
  }

  // 重置数据
  resetContractData()
  todayTradeRequests.value = []

  if (options?.requestType) {
    requestType.value = options.requestType as TradeRequestType
    console.log('设置请求类型:', requestType.value)
  } else {
    // 如果没有传递操作类型，默认设置为点价
    requestType.value = 'PointPrice'
  }

  if (options?.instrumentId) {
    const newInstrumentId = parseInt(options.instrumentId, 10)
    instrumentId.value = newInstrumentId
    loadInstrument(instrumentId.value)
    loadTodayTradeRequests()
  }

  if (options?.setterID) {
    const newSetterID = parseInt(options.setterID, 10)
    setterID.value = newSetterID
  }

  if (options?.setter) {
    setterName.value = options.setter
  }

  // 加载所有可用合同
  loadAllAvailableContracts()
}

// 生命周期钩子
onLoad((options) => {
  handlePageParams(options)
})

// 每次页面显示时重新处理参数
onShow(() => {
  console.log('execute page onShow')
  // 获取当前页面的参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  if (currentPage && currentPage.options) {
    console.log('onShow 重新处理参数:', currentPage.options)

    // 检查参数是否发生变化
    const newInstrumentId = currentPage.options.instrumentId ? parseInt(currentPage.options.instrumentId, 10) : null
    const newSetterID = currentPage.options.setterID ? parseInt(currentPage.options.setterID, 10) : null
    const newTimestamp = currentPage.options._t

    const paramsChanged =
      newInstrumentId !== instrumentId.value ||
      newSetterID !== setterID.value ||
      currentPage.options.setter !== setterName.value ||
      newTimestamp // 如果有时间戳参数，说明是新的跳转

    if (paramsChanged) {
      console.log('参数发生变化或新的跳转，重新加载数据')
      handlePageParams(currentPage.options)
    }
  }
})
</script>

<template>
  <view class="trade-execute-page min-h-screen bg-gray-100">
    <wd-navbar title="交易执行" :fixed="false" />

    <view class="p-3">
      <!-- 1. Tab 切换区域 -->
      <view class="card bg-white rounded-lg shadow-sm mb-3">
        <view class="tab-container">
          <view
            class="tab-item"
            :class="{ 'active': requestType === 'PointPrice' }"
            @click="switchTab('PointPrice')"
          >
            <text class="tab-text">点价</text>
          </view>
          <view
            class="tab-item"
            :class="{ 'active': requestType === 'BasisWash' }"
            @click="switchTab('BasisWash')"
          >
            <text class="tab-text">洗基差</text>
          </view>
        </view>
      </view>

      <!-- 2. 基本信息选择区域 -->
      <view class="card bg-white rounded-lg shadow-sm p-4 mb-3">
        <!-- 第一行：组合选择器 -->
        <view class="mb-4">
          <CombinationSelector
            :combinations="availableCombinations"
            v-model="pickerValue"
            :current-display="currentCombinationDisplay"
            @change="handleCombinationChange"
          />
        </view>

        <!-- 第二行：统计数据 -->
        <view class="stats-grid grid grid-cols-4 gap-2">
          <view class="stat-item bg-blue-50 rounded-lg p-3 text-center">
            <text class="stat-number text-lg font-bold text-blue-600">{{ currentContractStats.remainingQuantity }}</text>
            <text class="stat-label text-xs text-gray-500 block mt-1">{{ isPointPrice ? '基差' : '固价' }}剩余</text>
          </view>
          <view class="stat-item bg-orange-50 rounded-lg p-3 text-center">
            <text class="stat-number text-lg font-bold text-orange-600">{{ currentContractStats.frozenQuantity }}</text>
            <text class="stat-label text-xs text-gray-500 block mt-1">{{ isPointPrice ? '基差' : '固价' }}冻结</text>
          </view>
          <view class="stat-item bg-green-50 rounded-lg p-3 text-center">
            <text class="stat-number text-lg font-bold text-green-600">{{ currentContractStats.availableQuantity }}</text>
            <text class="stat-label text-xs text-gray-500 block mt-1">{{ isPointPrice ? '基差' : '固价' }}可用</text>
          </view>
          <view class="stat-item bg-purple-50 rounded-lg p-3 text-center">
            <text class="stat-number text-lg font-bold text-purple-600">{{ currentContractStats.weightedPrice.toFixed(2) }}</text>
            <text class="stat-label text-xs text-gray-500 block mt-1">{{ isPointPrice ? '基差' : '固价' }}均价</text>
          </view>
        </view>
      </view>

      <!-- 2. 实时行情 -->
      <QuoteCard
        :instrument-id="instrument?.instrument_id || ''"
        :symbol="instrument?.instrument_id || 'i2510'"
        :exchange="'DCE'"
        @price-click="handlePriceClick"
      />

      <!-- 3. 核心操作区 -->
      <view class="mb-3">
        <TradeOperationForm
          ref="tradeFormRef"
          :is-point-price="isPointPrice"
          :available-quantity="currentContractStats.availableQuantity"
          :weighted-price="currentContractStats.weightedPrice"
          :limit-up-price="limitUpPrice"
          :limit-down-price="limitDownPrice"
          :loading="loading"
          @submit="handleTradeRequestSubmit"
        />
      </view>

      <!-- 4. 当日交易请求概览 -->
      <TradeRequestList
        :requests="todayTradeRequests"
        :request-type="requestType"
        :loading="loading"
        mode="viewer"
      />

    </view>


  </view>
</template>

<style lang="scss" scoped>
.trade-execute-page {
  background-color: #f7f8fa;
}

// Tab 切换样式
.tab-container {
  display: flex;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 8rpx;
  margin: 16rpx;

  .tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 16rpx;
    border-radius: 6rpx;
    transition: all 0.3s ease;
    cursor: pointer;

    .tab-text {
      font-size: 28rpx;
      font-weight: 500;
      color: #606266;
      transition: color 0.3s ease;
    }

    &.active {
      background-color: white;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

      .tab-text {
        color: #409eff;
        font-weight: 600;
      }
    }

    &:hover:not(.active) {
      background-color: rgba(255, 255, 255, 0.5);
    }
  }
}

// 统计数据网格样式
.stats-grid {
  .stat-item {
    min-height: 120rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 12rpx;
    padding: 20rpx 12rpx;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2rpx);
      box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
    }

    .stat-number {
      font-size: 36rpx;
      font-weight: 700;
      line-height: 1.2;
      margin-bottom: 8rpx;
    }

    .stat-label {
      font-size: 22rpx;
      color: #909399;
      text-align: center;
      line-height: 1.2;
    }
  }
}
</style>
