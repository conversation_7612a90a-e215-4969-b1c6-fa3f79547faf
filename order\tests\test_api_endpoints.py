#!/usr/bin/env python3
"""
测试API端点与前端一致性
"""

import sys
import logging
import asyncio
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from auth.services.auth_service import AuthService
from auth.managers.login_manager import LoginManager
from vnpy.event import EventEngine

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )

async def test_api_endpoints():
    """测试API端点"""
    logger = logging.getLogger(__name__)
    logger.info("开始测试API端点")
    
    # 创建事件引擎
    event_engine = EventEngine()
    event_engine.start()
    
    try:
        # 创建认证服务
        auth_service = AuthService()
        
        # 打印所有端点
        logger.info("当前配置的API端点:")
        for name, endpoint in auth_service.endpoints.items():
            logger.info(f"  {name}: {endpoint}")
        
        # 验证与前端API的一致性
        expected_endpoints = {
            "login_username": "/user/login",           # loginByUsername
            "login_phone": "/user/loginByPhone",       # loginByPhone  
            "login_wechat": "/user/loginByWechat",     # loginByWechat
            "send_login_code": "/user/sendLoginCode",  # sendLoginCode
            "get_profile": "/user/getProfile",         # getProfile
            "update_profile": "/user/updateProfile",  # updateProfile
            "get_captcha": "/base/captcha",           # getCaptcha
        }
        
        logger.info("\n验证端点一致性:")
        all_match = True
        for name, expected in expected_endpoints.items():
            actual = auth_service.endpoints.get(name)
            if actual == expected:
                logger.info(f"  ✓ {name}: {actual}")
            else:
                logger.error(f"  ✗ {name}: 期望 {expected}, 实际 {actual}")
                all_match = False
        
        if all_match:
            logger.info("\n✓ 所有API端点与前端一致!")
        else:
            logger.error("\n✗ 存在不一致的API端点!")
        
        # 测试登录管理器
        login_manager = LoginManager(event_engine)
        
        # 测试获取验证码方法
        logger.info("\n测试获取验证码方法:")
        try:
            # 这里只是测试方法存在，不实际调用网络请求
            if hasattr(login_manager, 'get_captcha'):
                logger.info("  ✓ LoginManager.get_captcha() 方法存在")
            else:
                logger.error("  ✗ LoginManager.get_captcha() 方法不存在")
                
            if hasattr(auth_service, 'get_captcha'):
                logger.info("  ✓ AuthService.get_captcha() 方法存在")
            else:
                logger.error("  ✗ AuthService.get_captcha() 方法不存在")
                
        except Exception as e:
            logger.error(f"  ✗ 测试验证码方法时出错: {str(e)}")
        
        # 测试方法签名
        logger.info("\n测试方法签名:")
        try:
            import inspect
            
            # 检查 login_with_password 方法签名
            sig = inspect.signature(login_manager.login_with_password)
            params = list(sig.parameters.keys())
            expected_params = ['username', 'password', 'remember_me', 'captcha', 'captcha_id']
            
            if all(param in params for param in expected_params):
                logger.info("  ✓ login_with_password() 方法签名正确")
            else:
                logger.error(f"  ✗ login_with_password() 方法签名不正确")
                logger.error(f"    期望参数: {expected_params}")
                logger.error(f"    实际参数: {params}")
                
        except Exception as e:
            logger.error(f"  ✗ 测试方法签名时出错: {str(e)}")
        
        return all_match
        
    except Exception as e:
        logger.error(f"测试过程中发生错误：{str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 停止事件引擎
        event_engine.stop()
        logger.info("测试结束")

def main():
    """主函数"""
    setup_logging()
    return asyncio.run(test_api_endpoints())

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
