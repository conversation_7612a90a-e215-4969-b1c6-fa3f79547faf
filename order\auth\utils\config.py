"""
配置管理工具

提供统一的配置管理功能，包括：
- YAML/JSON配置文件加载
- 环境变量支持
- 配置合并和覆盖
- 配置验证
"""

import os
import json
import logging
from typing import Any, Dict, Optional, Union
from pathlib import Path

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False

from ..exceptions import ConfigException, handle_auth_exception


class ConfigManager:
    """配置管理器
    
    提供统一的配置管理接口。
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        """初始化配置管理器
        
        Args:
            config_dir: 配置文件目录（可选）
        """
        self.logger = logging.getLogger(__name__)
        
        # 设置配置目录
        if config_dir:
            self.config_dir = Path(config_dir)
        else:
            # 默认配置目录
            current_dir = Path(__file__).parent.parent.parent
            self.config_dir = current_dir / "config"
        
        # 配置数据
        self.config_data = {}
        
        # 环境变量前缀
        self.env_prefix = "ORDER_"
        
        # 加载配置
        self._load_default_config()
        self._load_config_files()
        self._load_environment_variables()
        
        self.logger.info(f"配置管理器初始化完成 - 配置目录: {self.config_dir}")
    
    def _load_default_config(self):
        """加载默认配置"""
        self.config_data = {
            "auth": {
                "server": {
                    "base_url": "https://api.server.com",
                    "timeout": 30,
                    "retry_count": 3,
                    "retry_delay": 1
                },
                "token": {
                    "storage_file": "user_token.json",
                    "encryption_enabled": True,
                    "auto_refresh": True,
                    "refresh_threshold": 300
                },
                "sms": {
                    "countdown_seconds": 60,
                    "max_retry_count": 3
                },
                "security": {
                    "password_min_length": 6,
                    "max_login_attempts": 5,
                    "lockout_duration": 300
                }
            },
            "ui": {
                "login_window": {
                    "width": 400,
                    "height": 350,
                    "resizable": False
                },
                "theme": {
                    "primary_color": "#3498db",
                    "secondary_color": "#95a5a6",
                    "success_color": "#27ae60",
                    "error_color": "#e74c3c",
                    "background_color": "#f8f9fa"
                }
            },
            "logging": {
                "level": "INFO",
                "file_enabled": True,
                "console_enabled": True,
                "max_file_size": 10485760,  # 10MB
                "backup_count": 5
            }
        }
    
    def _load_config_files(self):
        """加载配置文件"""
        if not self.config_dir.exists():
            self.logger.info(f"配置目录不存在: {self.config_dir}")
            return
        
        # 配置文件列表（按优先级排序）
        config_files = [
            "auth_config.yaml",
            "auth_config.yml", 
            "auth_config.json",
            "ui_config.yaml",
            "ui_config.yml",
            "ui_config.json",
            "logging_config.yaml",
            "logging_config.yml",
            "logging_config.json"
        ]
        
        for config_file in config_files:
            file_path = self.config_dir / config_file
            if file_path.exists():
                try:
                    config_data = self._load_config_file(file_path)
                    if config_data:
                        self._merge_config(self.config_data, config_data)
                        self.logger.info(f"加载配置文件: {config_file}")
                except Exception as e:
                    self.logger.error(f"加载配置文件失败 {config_file}: {str(e)}")
    
    def _load_config_file(self, file_path: Path) -> Optional[Dict[str, Any]]:
        """加载单个配置文件
        
        Args:
            file_path: 配置文件路径
            
        Returns:
            Optional[Dict[str, Any]]: 配置数据
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() in ['.yaml', '.yml']:
                    if not YAML_AVAILABLE:
                        self.logger.warning("PyYAML未安装，无法加载YAML配置文件")
                        return None
                    return yaml.safe_load(f)
                elif file_path.suffix.lower() == '.json':
                    return json.load(f)
                else:
                    self.logger.warning(f"不支持的配置文件格式: {file_path}")
                    return None
        except Exception as e:
            self.logger.error(f"读取配置文件失败 {file_path}: {str(e)}")
            return None
    
    def _load_environment_variables(self):
        """加载环境变量"""
        env_mappings = {
            f"{self.env_prefix}SERVER_URL": "auth.server.base_url",
            f"{self.env_prefix}SERVER_TIMEOUT": "auth.server.timeout",
            f"{self.env_prefix}TOKEN_FILE": "auth.token.storage_file",
            f"{self.env_prefix}ENCRYPTION_ENABLED": "auth.token.encryption_enabled",
            f"{self.env_prefix}LOG_LEVEL": "logging.level",
            f"{self.env_prefix}LOG_FILE_ENABLED": "logging.file_enabled"
        }
        
        for env_var, config_key in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                # 类型转换
                converted_value = self._convert_env_value(env_value)
                self._set_nested_value(self.config_data, config_key, converted_value)
                self.logger.info(f"从环境变量加载配置: {env_var} -> {config_key}")
    
    def _convert_env_value(self, value: str) -> Any:
        """转换环境变量值类型
        
        Args:
            value: 环境变量值
            
        Returns:
            Any: 转换后的值
        """
        # 布尔值
        if value.lower() in ['true', 'yes', '1', 'on']:
            return True
        elif value.lower() in ['false', 'no', '0', 'off']:
            return False
        
        # 数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # 字符串
        return value
    
    def _merge_config(self, base: Dict[str, Any], override: Dict[str, Any]):
        """合并配置
        
        Args:
            base: 基础配置
            override: 覆盖配置
        """
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    def _set_nested_value(self, data: Dict[str, Any], key_path: str, value: Any):
        """设置嵌套配置值
        
        Args:
            data: 配置数据
            key_path: 配置键路径（用.分隔）
            value: 配置值
        """
        keys = key_path.split('.')
        current = data
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            key_path: 配置键路径（用.分隔）
            default: 默认值
            
        Returns:
            Any: 配置值
        """
        try:
            keys = key_path.split('.')
            current = self.config_data
            
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return default
            
            return current
            
        except Exception as e:
            self.logger.error(f"获取配置失败 {key_path}: {str(e)}")
            return default
    
    def set(self, key_path: str, value: Any):
        """设置配置值
        
        Args:
            key_path: 配置键路径（用.分隔）
            value: 配置值
        """
        try:
            self._set_nested_value(self.config_data, key_path, value)
            self.logger.info(f"设置配置: {key_path} = {value}")
        except Exception as e:
            self.logger.error(f"设置配置失败 {key_path}: {str(e)}")
    
    def has(self, key_path: str) -> bool:
        """检查配置是否存在
        
        Args:
            key_path: 配置键路径（用.分隔）
            
        Returns:
            bool: 配置是否存在
        """
        try:
            keys = key_path.split('.')
            current = self.config_data
            
            for key in keys:
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return False
            
            return True
            
        except:
            return False
    
    @handle_auth_exception
    def save_config(self, file_path: Optional[str] = None, format: str = "yaml"):
        """保存配置到文件
        
        Args:
            file_path: 文件路径（可选）
            format: 文件格式 (yaml/json)
            
        Raises:
            ConfigException: 保存失败
        """
        try:
            if file_path is None:
                file_path = self.config_dir / f"runtime_config.{format}"
            else:
                file_path = Path(file_path)
            
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                if format.lower() == 'yaml':
                    if not YAML_AVAILABLE:
                        raise ConfigException("PyYAML未安装，无法保存YAML格式")
                    yaml.dump(self.config_data, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                elif format.lower() == 'json':
                    json.dump(self.config_data, f, ensure_ascii=False, indent=2)
                else:
                    raise ConfigException(f"不支持的格式: {format}")
            
            self.logger.info(f"配置已保存到: {file_path}")
            
        except Exception as e:
            raise ConfigException(f"保存配置失败: {str(e)}")
    
    def reload(self):
        """重新加载配置"""
        self.logger.info("重新加载配置")
        self.config_data = {}
        self._load_default_config()
        self._load_config_files()
        self._load_environment_variables()
    
    def get_all(self) -> Dict[str, Any]:
        """获取所有配置
        
        Returns:
            Dict[str, Any]: 所有配置数据
        """
        return self.config_data.copy()
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取配置段
        
        Args:
            section: 配置段名称
            
        Returns:
            Dict[str, Any]: 配置段数据
        """
        return self.get(section, {})
    
    def validate_config(self) -> bool:
        """验证配置
        
        Returns:
            bool: 配置是否有效
        """
        try:
            # 检查必需的配置项
            required_configs = [
                "auth.server.base_url",
                "auth.token.storage_file",
                "ui.login_window.width",
                "ui.login_window.height"
            ]
            
            for config_key in required_configs:
                if not self.has(config_key):
                    self.logger.error(f"缺少必需配置: {config_key}")
                    return False
            
            # 检查配置值的有效性
            if self.get("auth.server.timeout", 0) <= 0:
                self.logger.error("服务器超时时间必须大于0")
                return False
            
            if self.get("ui.login_window.width", 0) <= 0:
                self.logger.error("登录窗口宽度必须大于0")
                return False
            
            self.logger.info("配置验证通过")
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {str(e)}")
            return False
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取配置信息
        
        Returns:
            Dict[str, Any]: 配置信息
        """
        return {
            "config_dir": str(self.config_dir),
            "env_prefix": self.env_prefix,
            "yaml_available": YAML_AVAILABLE,
            "config_keys": list(self._get_all_keys(self.config_data))
        }
    
    def _get_all_keys(self, data: Dict[str, Any], prefix: str = "") -> list:
        """获取所有配置键
        
        Args:
            data: 配置数据
            prefix: 键前缀
            
        Returns:
            list: 所有配置键
        """
        keys = []
        for key, value in data.items():
            full_key = f"{prefix}.{key}" if prefix else key
            keys.append(full_key)
            
            if isinstance(value, dict):
                keys.extend(self._get_all_keys(value, full_key))
        
        return keys
