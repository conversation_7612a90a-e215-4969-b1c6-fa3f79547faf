# Order客户端使用说明

## 🚀 快速开始

### 1. 环境准备

确保已安装Python 3.8+和所需依赖：

```bash
pip install -r requirements.txt
```

### 2. 启动应用程序

#### 方式一：直接运行主程序
```bash
python main.py
```

#### 方式二：使用启动脚本
```bash
# 运行主程序
python run.py main

# 测试登录功能
python run.py test

# 运行使用示例
python run.py example

# 启用调试模式
python run.py main --debug
```

## 📱 登录界面使用

### 登录方式

应用程序支持两种登录方式：

#### 1. 用户名密码登录
- 输入用户名和密码
- 可选择"记住密码"和"自动登录"
- 点击"登录"按钮

#### 2. 手机验证码登录
- 切换到"手机验证码"选项
- 输入手机号
- 点击"获取验证码"
- 输入收到的验证码
- 点击"登录"按钮

### 功能特性

#### 自动登录
- 勾选"自动登录"选项
- 下次启动时会自动尝试登录
- 如果Token有效，直接进入主程序

#### 记住密码
- 勾选"记住密码"选项
- 登录信息会保存到本地
- 下次启动时自动填充

#### 状态指示
- 实时显示连接状态
- 登录过程中显示进度
- 错误时显示具体信息

## ⚙️ 配置说明

### 认证配置 (config/auth_config.yaml)

```yaml
auth:
  server:
    base_url: "https://your-api-server.com"  # 修改为实际服务器地址
    timeout: 30
    retry_count: 3
    
  token:
    storage_file: "user_token.json"
    encryption_enabled: true
    auto_refresh: true
    
  sms:
    countdown_seconds: 60
    max_retry_count: 3
```

### UI配置 (config/ui_config.yaml)

```yaml
ui:
  login_window:
    width: 400
    height: 450
    resizable: false
    
  theme:
    primary_color: "#3498db"
    success_color: "#27ae60"
    error_color: "#e74c3c"
```

## 🔧 开发和调试

### 测试登录功能

```bash
# 运行登录测试
python test_login.py

# 或使用启动脚本
python run.py test
```

### 查看日志

日志文件位于 `logs/` 目录：
- `order_main.log`: 主程序日志
- `order_auth.log`: 认证模块日志
- `order_auth_error.log`: 错误日志

### 调试模式

```bash
python run.py main --debug
```

调试模式会：
- 启用详细日志输出
- 显示更多调试信息
- 便于问题排查

## 🔒 安全注意事项

### Token存储
- Token使用AES加密存储
- 密钥基于机器特征生成
- 文件权限设置为仅所有者可读

### 配置安全
- 不要在配置文件中存储敏感信息
- 使用环境变量覆盖敏感配置
- 定期更新密码和Token

### 网络安全
- 确保使用HTTPS连接
- 验证服务器SSL证书
- 避免在不安全网络下登录

## ❓ 常见问题

### Q: 登录失败怎么办？
A: 
1. 检查网络连接
2. 确认用户名密码正确
3. 查看错误提示信息
4. 检查服务器配置

### Q: 验证码收不到怎么办？
A: 
1. 检查手机号格式
2. 确认网络连接正常
3. 等待60秒后重新发送
4. 联系管理员检查短信服务

### Q: 自动登录失败怎么办？
A: 
1. Token可能已过期
2. 重新手动登录
3. 检查本地Token文件
4. 清除登录配置重新设置

### Q: 界面显示异常怎么办？
A: 
1. 检查PySide6是否正确安装
2. 更新显卡驱动
3. 尝试不同的显示缩放设置
4. 查看日志文件获取详细信息

## 📞 技术支持

如果遇到问题：

1. 查看日志文件获取详细错误信息
2. 检查配置文件是否正确
3. 确认网络连接和服务器状态
4. 联系技术支持团队

## 🔄 更新说明

### 版本检查
应用程序会自动检查更新，如有新版本会提示用户。

### 手动更新
1. 备份配置文件
2. 下载新版本
3. 替换程序文件
4. 恢复配置文件
5. 重新启动应用程序
