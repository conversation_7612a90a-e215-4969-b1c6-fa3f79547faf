# uni-app 页面开发规范

本文档旨在规范 uni-app 项目中页面（`.vue` 文件）的开发方式，确保代码风格统一、结构清晰、易于维护。所有位于 `src/pages` 目录下的页面都应遵循此规范。

## 1. 页面文件结构

一个标准的页面 `.vue` 文件应包含以下四个部分，并严格按照顺序排列：

1.  `<route>`: 页面路由配置块
2.  `<script>`: 逻辑脚本区
3.  `<template>`: 视图模板区
4.  `<style>`: 页面样式区

```vue
<route lang="jsonc" type="page">
{
  // 页面配置
}
</route>

<script lang="ts" setup>
// 逻辑代码
</script>

<template>
  <!-- 视图模板 -->
</template>

<style lang="scss" scoped>
/* 页面样式 */
</style>
```

---

## 2. `<route>` 路由配置

`<route>` 块用于定义页面的路由信息和页面配置，它将被 `unplugin-uni-pages` 插件处理，自动生成 `pages.json` 中的内容。

-   **语言 (lang)**: 推荐使用 `jsonc` 或 `yaml`。`jsonc` 支持注释，更易于理解。
-   **类型 (type)**:
    -   `page`: 普通页面（默认值）。
    -   `home`: 主页/首页，应用启动时加载的第一个页面。
    -   `tabbar`: TabBar 页面。
-   **布局 (layout)**:
    -   `default`: 默认布局，通常包含标准的导航栏。
    -   `tabbar`: TabBar 布局，用于 TabBar 页面。
-   **样式 (style)**:
    -   `navigationBarTitleText`: 页面标题。
    -   `navigationStyle`: 导航栏样式。
        -   `default`: 默认导航栏。
        -   `custom`: 自定义导航栏，此时页面顶部将没有原生导航栏，需要自行实现。

**示例 (`jsonc`)**

```jsonc
<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "页面标题",
    "navigationStyle": "default" // or "custom"
  }
}
</route>
```

**首页示例**

```jsonc
<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "首页"
  }
}
</route>
```

---

## 3. `<script>` 逻辑脚本

脚本区域负责页面的所有业务逻辑。

-   **语言与设置**: 必须使用 `<script lang="ts" setup>` 组合，以充分利用 TypeScript 和 Vue 3 Composition API 的优势。
-   **代码组织**:
    1.  **Imports**: 首先从外部库（如 `vue`, `alova`, `@/store`）和内部模块（`@/api`, `@/service`, `@/utils`）导入所需内容。
    2.  **组件定义**: 可选地使用 `defineOptions({ name: 'PageName' })` 来命名组件，便于调试。
    3.  **状态管理**: 使用 `ref`, `reactive`, `computed` 等定义响应式状态。
    4.  **Store 使用**: 通过 `useUserStore()` 等方式获取 Pinia store 实例。
    5.  **生命周期钩子**: 使用 `onLoad`, `onShow` 等 uni-app 生命周期钩子执行初始化逻辑。
    6.  **函数/方法**: 定义事件处理函数、业务逻辑函数等。

**示例**

```typescript
<script lang="ts" setup>
// 1. Imports
import { ref, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { useUserStore } from '@/store/user';
import { getSomeDataAPI } from '@/service/some/api';
import { toast } from '@/utils/toast';

// 2. 组件定义 (可选)
defineOptions({
  name: 'MyPage',
});

// 3. 状态管理
const userStore = useUserStore();
const pageTitle = ref('我的页面');
const isLoading = ref(false);
const dataList = ref([]);

const canSubmit = computed(() => {
  // ... some logic
  return true;
});

// 4. 生命周期钩子
onLoad(async () => {
  await fetchData();
});

// 5. 函数/方法
async function fetchData() {
  try {
    isLoading.value = true;
    const res = await getSomeDataAPI();
    dataList.value = res.data;
  } catch (error) {
    toast.error('数据加载失败');
  } finally {
    isLoading.value = false;
  }
}

function handleSubmit() {
  if (!canSubmit.value) return;
  // ... 提交逻辑
}
</script>
```

---

## 4. `<template>` 视图模板

模板区域定义了页面的 UI 结构。

-   **样式**:
    -   **主要使用 UnoCSS**: 通过原子化 class（如 `p-4`, `text-center`, `bg-blue-500`）快速构建布局和样式。
    -   **UI 库**: 使用项目统一的 UI 组件库（如 `wd-button`, `wd-input`）构建交互元素。
-   **结构**:
    -   保持清晰的 DOM 结构，合理使用 `view`, `text`, `image` 等基础组件。
    -   使用 `v-if`/`v-else` 控制元素的显示和隐藏，例如处理加载状态。
    -   使用 `@click` 等事件绑定来调用 `<script>` 中定义的方法。

**示例**

```html
<template>
  <view class="page-container p-4">
    <view v-if="isLoading" class="loading-indicator">
      加载中...
    </view>
    <view v-else>
      <h1 class="text-xl font-bold mb-4">{{ pageTitle }}</h1>
      
      <view v-for="item in dataList" :key="item.id" class="list-item">
        {{ item.name }}
      </view>

      <wd-button 
        type="primary" 
        block 
        class="mt-6"
        :disabled="!canSubmit"
        @click="handleSubmit"
      >
        提交
      </wd-button>
    </view>
  </view>
</template>
```

---

## 5. `<style>` 页面样式

当 UnoCSS 和组件库无法满足复杂的样式需求时，可以在 `<style>` 块中编写自定义样式。

-   **语言**: 必须使用 `scss` (`lang="scss"`)。
-   **作用域**: 必须添加 `scoped` 属性，将样式限定在当前页面，避免全局污染。
-   **用途**:
    -   定义复杂的、非原子化的样式。
    -   使用 `rpx` 单位以适应不同屏幕尺寸。
    -   覆盖组件库的局部样式（谨慎使用）。

**示例**

```scss
<style lang="scss" scoped>
.page-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.list-item {
  padding: 12rpx 0;
  border-bottom: 1rpx solid #eee;
  transition: background-color 0.3s;

  &:hover {
    background-color: #f0f0f0;
  }
}
</style>
```

---

# UI 设计与视觉规范

本规范旨在指导AI与设计师协作输出既美观又实用的UI，兼顾视觉、交互、用户需求与技术落地。内容涵盖设计理念、视觉体系、交互原则、可访问性、商业目标等，适用于所有页面和组件开发。

---

## 一、设计理念与核心原则

### 1. 视觉一致性原则

- **统一视觉语言**：采用标准网格系统（4px/8px），所有元素对齐基线，避免错位。
- **色彩体系**：主色、辅助色、功能色、文字色、背景色分层管理，同一功能色值全局一致。
- **风格统一**：圆角、阴影、图标风格等视觉特征全局统一，避免割裂。

### 2. 视觉层级原则

- **信息分级**：通过字号、字重、颜色区分主副标题、正文、说明等层级。
- **对比度控制**：文字与背景对比度符合WCAG标准，按钮等关键操作对比度更高。
- **留白艺术**：合理设置模块、元素间距，突出核心内容，避免信息拥挤。

### 3. 交互可用性原则

- **信息架构清晰**：内容组织贴合用户心智模型，高频功能固定，低频功能收纳。
- **动效反馈即时**：所有交互0.1-0.3s内有反馈，复杂动效≤800ms。
- **操作预期明确**：控件状态（正常/悬停/点击/禁用）可视化，滑动等操作符合物理直觉。

### 4. 场景适配原则

- **多端适配**：弹性布局，适配手机、平板、折叠屏等多种设备。
- **系统规范遵循**：iOS/Android平台遵循各自设计规范，避免风格冲突。
- **用户群体适配**：针对不同用户（如老年、年轻）调整字号、配色等细节。

### 5. 情感化原则

- **色彩心理学**：主色、辅助色选择符合产品定位，强化情绪表达。
- **微交互细节**：人性化反馈、空状态插画、友好文案提升亲和力。
- **品牌元素渗透**：LOGO、品牌色等元素贯穿全局，强化品牌记忆。

### 6. 简洁性原则

- **内容精简**：单屏核心操作≤5个，次要功能收纳，文案简洁。
- **视觉去噪**：去除冗余装饰，统一图标风格，用留白替代边框。

### 7. 可访问性原则

- **文字可读性**：无衬线体，正文≥14pt，避免高饱和度搭配。
- **色盲友好**：关键信息不依赖单一颜色，状态用图标+文字双重标识。
- **键盘/语音导航**：控件支持Tab聚焦，重要操作支持语音指令。

### 8. 商业价值原则

- **核心路径强化**：关键操作用主色、大尺寸、居中突出。
- **数据驱动优化**：A/B测试、用户行为分析持续优化设计。

---

## 二、视觉与组件细则（落地规范）

### 1. 布局与间距

- 采用4px网格系统，所有间距为4的倍数。
- 页面内边距推荐`p-4`（16px/32rpx）或`p-6`（24px/48rpx）。
- 卡片布局：`bg-white rounded-lg shadow-sm p-4`。
- 区块间距`my-4`/`my-6`，元素间距`my-2`/`my-3`。

### 2. 色彩体系

- 主色：`#667eea`（品牌蓝）、`#764ba2`（品牌紫）、渐变主色。
- 辅助色：`#87CEEB`（浅蓝）、`#483D8B`（深紫）。
- 功能色：成功`#67C23A`、警告`#E6A23C`、危险`#F56C6C`、信息`#909399`、微信绿`#07C160`。
- 文字色：标题`#303133`、正文`#606266`、辅助`#909399`、禁用`#C0C4CC`、反白`#FFFFFF`。
- 背景色：主背景渐变/纯色，卡片`#FFFFFF`，分割线`#E4E7ED`。

### 3. 字体规范

- 一级标题：40rpx(20px)，加粗
- 二级标题：36rpx(18px)，加粗
- 三级标题：32rpx(16px)，中等
- 正文：28rpx(14px)，常规
- 说明：24rpx(12px)，常规

### 4. 组件应用（基于wot-design-uni）

**重要提示**：在开发过程中，应使用 `context7` 工具获取 `wot-design-uni` 最新的官方文档和组件使用规范，以确保代码的标准化和一致性。

- 按钮：主操作`type="primary"`，次要`type="info"`，危险`type="error"`，文字按钮`type="text"`。
- 表单：`wd-cell-group`包裹`wd-input`，每项有`label`，用`rules`校验。
- 导航栏：自定义用`<wd-navbar>`，设置`title`、`left-text`。
- 信息反馈：`wd-toast`轻提示，`wd-message-box`确认对话框。
- 单元格：`wd-cell`展示列表、设置项。

---

## 三、开发与实现建议

- **页面结构**：严格遵循`<route>`→`<script>`→`<template>`→`<style>`顺序。
- **样式优先级**：优先用UnoCSS原子类，复杂样式用`<style lang="scss" scoped>`。
- **动效实现**：按钮、加载等交互需有动画反馈，时长合理。
- **无障碍实现**：所有交互控件支持键盘、语音操作，状态有多重标识。
- **数据驱动**：设计方案需结合用户数据持续优化。

---

## 四、原则协同与应用

这8大原则相互支撑：视觉一致性是基础，视觉层级引导视线，交互可用性保障流畅操作，场景适配扩大适用范围，情感化增强认同，简洁性提升效率，可访问性包容差异，商业价值对齐目标。实际设计时应根据业务场景灵活调整优先级，但始终围绕“用户需求”与“业务目标”展开。

---

**建议：将本规范作为AI生成UI和团队设计开发的统一标准，持续结合实际项目反馈迭代完善。**

如需输出为Markdown文档或进一步细化某一部分，请告知！

---

# 代码开发规范

本文档定义了项目代码开发的标准规范，确保代码质量、可维护性和团队协作效率。

## 1. 类型定义规范

### 1.1 类型文件组织结构

所有TypeScript类型定义必须统一管理在 `src/types/` 目录下：

```
src/types/
├── index.ts          # 统一导出所有类型
├── user.ts           # 用户相关类型
├── auth.ts           # 认证登录相关类型
├── contract.ts       # 合约相关类型（示例）
└── README.md         # 类型定义说明文档
```

### 1.2 类型定义原则

1. **按功能模块分类**：不同业务模块的类型放在对应的文件中
2. **避免重复定义**：统一的类型只定义一次，其他地方通过导入使用
3. **向后兼容**：保留旧版本的类型定义，标记为 `@deprecated`
4. **统一导出**：通过 `index.ts` 提供统一的导出入口

### 1.3 使用方式

#### 推荐用法（从统一入口导入）
```typescript
import type { IUser, ILoginResponse, IPhoneLoginRequest } from '@/types'
```

#### 也可以从具体文件导入
```typescript
import type { IUser } from '@/types/user'
import type { IPhoneLoginRequest } from '@/types/auth'
```

#### 通过 API 模块导入（保持现有兼容性）
```typescript
import type { IPhoneLoginRequest, IUsernameLoginRequest } from '@/api/auth'
```

### 1.4 新增类型流程

1. 根据功能确定应该放在哪个文件中
2. 在对应文件中添加类型定义
3. 在 `index.ts` 中添加导出
4. 更新相关的 API 文件导出（如果需要）

## 2. User Store 使用规范

### 2.1 基本使用

```typescript
import { useUserStore } from '@/store/user'

// 在组合式API中使用
const userStore = useUserStore()

// 访问状态
console.log(userStore.userInfo)
console.log(userStore.isLoggedIn)
console.log(userStore.token)
```

### 2.2 核心方法说明

#### 登录方法
```typescript
// 手机号验证码登录
await userStore.phoneLogin({
  phone: '13800138000',
  code: '123456'
})

// 用户名密码登录
await userStore.usernameLogin({
  username: 'testuser',
  password: 'password123',
  captcha: 'abc123',
  captchaId: 'captcha-id'
})

// 微信登录
await userStore.wxLogin()
```

#### 用户资料管理
```typescript
// 获取用户资料
await userStore.getUserProfile()

// 更新用户资料
await userStore.updateUserProfile({
  nickName: '新昵称',
  headerImg: 'avatar-url'
})

// 设置用户头像
userStore.setUserAvatar('new-avatar-url')
```

#### 验证码相关
```typescript
// 发送登录验证码
await userStore.sendVerificationCode('13800138000')
```

#### 退出登录
```typescript
await userStore.logout()
```

### 2.3 状态访问

```typescript
// 用户信息
const user = userStore.userInfo

// 登录状态
const isLoggedIn = userStore.isLoggedIn

// 访问令牌
const token = userStore.token
```

### 2.4 在组件中的完整使用示例

```vue
<script lang="ts" setup>
import { useUserStore } from '@/store/user'
import type { IPhoneLoginRequest } from '@/types'

const userStore = useUserStore()

// 登录函数
async function handleLogin() {
  try {
    const loginData: IPhoneLoginRequest = {
      phone: '13800138000',
      code: '123456'
    }
    
    await userStore.phoneLogin(loginData)
    
    // 登录成功后跳转
    uni.switchTab({
      url: '/pages/index/index'
    })
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 检查登录状态
if (userStore.isLoggedIn) {
  console.log('用户已登录:', userStore.userInfo.nickName)
}
</script>
```

## 3. API 模块规范

### 3.1 API 文件组织

```typescript
// src/api/auth.ts
import { http } from '@/http/http'
import type {
  ISendCodeRequest,
  IPhoneLoginRequest,
  IUsernameLoginRequest,
  ICaptchaResponse,
} from '@/types'
import type { IUser, ILoginResponse, IUpdateProfileRequest } from '@/types'

// 为了保持兼容性，重新导出类型
export type {
  ISendCodeRequest,
  IPhoneLoginRequest,
  IUsernameLoginRequest,
  IUpdateProfileRequest,
  ICaptchaResponse,
}

// API 函数定义...
```

### 3.2 API 函数规范

1. **使用统一的HTTP客户端**：所有API调用使用 `@/http/http`
2. **类型安全**：所有请求和响应都需要明确的类型定义
3. **错误处理**：API函数本身不处理业务错误，由调用方处理
4. **文档注释**：每个API函数都需要JSDoc注释

```typescript
/**
 * 手机号验证码登录
 * @param data 包含手机号和验证码的登录数据
 * @returns Promise 包含登录结果
 */
export function loginByPhone(data: IPhoneLoginRequest) {
  return http.post<ILoginResponse>('/user/loginByPhone', data)
}
```

## 4. 状态管理规范

### 4.1 Store 文件组织

```
src/store/
├── index.ts          # Pinia 实例和全局配置
├── user.ts           # 用户相关状态
├── app.ts            # 应用配置状态
└── modules/          # 其他业务模块状态
    ├── contract.ts   # 合约相关状态
    └── order.ts      # 订单相关状态
```

### 4.2 Store 定义规范

```typescript
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import type { IUser } from '@/types'

export const useModuleStore = defineStore(
  'module-name',
  () => {
    // 状态定义
    const state = ref<IUser>({...})
    
    // 计算属性
    const computedState = computed(() => {
      return state.value.someProperty
    })
    
    // 方法定义
    const updateState = (newState: Partial<IUser>) => {
      Object.assign(state.value, newState)
    }
    
    return {
      // 导出状态
      state,
      computedState,
      
      // 导出方法
      updateState,
    }
  },
  {
    persist: true, // 启用持久化
  }
)
```

## 5. 组件开发规范

### 5.1 类型导入

```vue
<script lang="ts" setup>
// 统一从 @/types 导入类型
import type { IUser, IPhoneLoginRequest } from '@/types'

// 从 store 导入状态管理
import { useUserStore } from '@/store/user'

// 其他导入...
</script>
```

### 5.2 状态使用

```vue
<script lang="ts" setup>
const userStore = useUserStore()

// 响应式访问
const userInfo = computed(() => userStore.userInfo)
const isLoggedIn = computed(() => userStore.isLoggedIn)
</script>

<template>
  <view v-if="isLoggedIn">
    欢迎，{{ userInfo.nickName }}
  </view>
  <view v-else>
    请先登录
  </view>
</template>
```


## 7. 最佳实践

### 7.1 类型定义
- 接口命名使用 `I` 前缀
- 请求类型使用 `Request` 后缀
- 响应类型使用 `Response` 后缀
- 视图对象使用 `Vo` 后缀

### 7.2 错误处理
- Store 方法内部处理API错误并显示提示
- 组件中捕获Store方法异常进行业务处理
- 使用统一的错误提示组件

### 7.3 性能优化
- 合理使用 computed 访问 store 状态
- 避免在模板中直接访问复杂的 store 方法
- 使用 Pinia 的持久化功能保存重要状态

## 8. 代码检查

### 8.1 必须遵守的规则
- 所有新类型必须定义在 `src/types/` 目录下
- 禁止在组件或API文件中定义重复的类型
- 必须使用统一的 `useUserStore` 进行用户状态管理
- API调用必须通过Store方法，不得直接在组件中调用

### 8.2 推荐使用的工具
- TypeScript 严格模式
- ESLint 代码检查
- Prettier 代码格式化
- Husky + lint-staged 提交前检查

## 9. 示例项目结构

```
src/
├── types/                    # 类型定义（必须）
│   ├── index.ts             # 统一导出
│   ├── user.ts              # 用户类型
│   └── auth.ts              # 认证类型
├── store/                   # 状态管理（必须）
│   ├── index.ts             # Pinia配置
│   └── user.ts              # 用户Store
├── api/                     # API接口（必须）
│   └── auth.ts              # 认证API
├── pages/                   # 页面文件
├── components/              # 组件文件
└── utils/                   # 工具函数
```

遵循本规范可以确保代码的一致性、可维护性和团队协作效率。对于现有代码，建议采用渐进式迁移的方式，逐步向新规范靠拢。