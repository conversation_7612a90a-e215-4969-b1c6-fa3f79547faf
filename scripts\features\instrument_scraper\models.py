from sqlalchemy import Column, String, DateTime, DECIMAL, BigInteger
from modules.database import Base

class Instrument(Base):
    """
    SQLAlchemy ORM model for an instrument.
    This model is mapped to the `instruments` table created by GORM.
    """
    __tablename__ = "instruments"

    # Columns managed by GORM
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    created_at = Column(DateTime, comment="创建时间")
    updated_at = Column(DateTime, comment="更新时间")
    deleted_at = Column(DateTime, index=True)

    # Columns to be synchronized from API
    exchange_id = Column(String(10), nullable=False, comment="交易所ID")
    product_id = Column(String(10), nullable=False, comment="产品ID")
    product_name = Column(String(50), nullable=False, comment="产品名称")
    instrument_id = Column(String(30), unique=True, nullable=False, comment="合约ID")
    instrument_name = Column(String(50), nullable=False, comment="合约名称")
    product_class = Column(String(1), nullable=False, comment="商品类别")
    volume_multiple = Column(BigInteger, nullable=False, comment="合约乘数")
    price_tick = Column(DECIMAL(10, 4), nullable=False, comment="最小变动价位")
    delivery_year = Column(BigInteger, comment="交割年份")
    delivery_month = Column(BigInteger, comment="交割月份")
    open_date = Column(DateTime, comment="上市日期")
    expire_date = Column(DateTime, comment="最后交易日")
    inst_life_phase = Column(String(1), nullable=False, comment="合约状态")

    # Columns not available from the API
    long_margin_ratio = Column(DECIMAL(10, 6), comment="做多保证金率")
    short_margin_ratio = Column(DECIMAL(10, 6), comment="做空保证金率")
    open_ratio_by_money = Column(DECIMAL(10, 8), comment="开仓手续费率")
    close_ratio_by_money = Column(DECIMAL(10, 8), comment="平仓手续费率")
    close_today_ratio = Column(DECIMAL(10, 8), comment="平今手续费率")

    def __repr__(self):
        return f"<Instrument(instrument_id='{self.instrument_id}', name='{self.instrument_name}')>"