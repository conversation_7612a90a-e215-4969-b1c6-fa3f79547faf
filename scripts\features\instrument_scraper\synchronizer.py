import requests
import json
from typing import List, Dict, Any, Tuple
from datetime import datetime

from modules.database import SessionLocal
from .models import Instrument

API_URL = "http://dict.openctp.cn/instruments"

def fetch_instruments(params: Dict[str, str] = None) -> List[Dict[str, Any]]:
    """
    Fetches instrument data from the OpenCTP API.

    Args:
        params: Optional dictionary of query parameters to filter the results.

    Returns:
        A list of dictionaries, where each dictionary represents an instrument.
        Returns an empty list if the request fails or the API returns an error.
    """
    try:
        response = requests.get(API_URL, params=params, timeout=10)
        response.raise_for_status()  # Raise an exception for bad HTTP status codes
        data = response.json()
        
        # Check the API's own response code
        if data.get("rsp_code") == 0 and "data" in data:
            return data["data"]
        else:
            print(f"API returned an error: {data.get('rsp_message', 'Unknown error')}")
            return []
            
    except requests.exceptions.RequestException as e:
        print(f"Error fetching instruments from API: {e}")
        return []
    except json.JSONDecodeError:
        print("Error: Failed to decode <PERSON><PERSON><PERSON> from response.")
        return []

def transform_data(instruments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Transforms the raw instrument data to match the database schema.

    Args:
        instruments: A list of instrument data from the API.

    Returns:
        A list of transformed instruments.
    """
    transformed = []
    for inst in instruments:
        instrument_id = inst.get("InstrumentID")
        if not instrument_id:
            print(f"Warning: Skipping instrument with missing InstrumentID. Data: {inst}")
            continue

        # Map API fields to database columns and handle potential empty date strings
        open_date = inst.get("OpenDate") if inst.get("OpenDate") else None
        expire_date = inst.get("ExpireDate") if inst.get("ExpireDate") else None

        transformed.append({
            "exchange_id": inst.get("ExchangeID"),
            "product_id": inst.get("ProductID"),
            "product_name": inst.get("ProductID"),  # Use ProductID as a sensible default for ProductName
            "instrument_id": instrument_id,
            "instrument_name": inst.get("InstrumentName"),
            "product_class": inst.get("ProductClass", '1'),  # Default to '1' if not present
            "volume_multiple": inst.get("VolumeMultiple"),
            "price_tick": inst.get("PriceTick"),
            "delivery_year": inst.get("DeliveryYear"),
            "delivery_month": inst.get("DeliveryMonth"),
            "open_date": open_date,
            "expire_date": expire_date,
            "inst_life_phase": inst.get("InstLifePhase", '1'),

            # --- Map financial ratios ---
            "long_margin_ratio": inst.get("LongMarginRatioByMoney"),
            "short_margin_ratio": inst.get("ShortMarginRatioByMoney"),
            "open_ratio_by_money": inst.get("OpenRatioByMoney"),
            "close_ratio_by_money": inst.get("CloseRatioByMoney"),
            "close_today_ratio": inst.get("CloseTodayRatioByMoney")
        })
    return transformed

import requests
import json
from typing import List, Dict, Any, Tuple
from datetime import datetime

from modules.database import SessionLocal, Base, engine
from .models import Instrument


def sync_to_database(instruments: List[Dict[str, Any]]) -> Tuple[int, int, int]:
    """
    Performs a full synchronization of instrument data with the database.

    Args:
        instruments: A list of transformed instrument data.

    Returns:
        A tuple containing the number of added, updated, and deleted records.
    """
    print("Connecting to the database...")
    db = SessionLocal()
    try:
        print("Performing full synchronization...")

        # Get remote and local data sets for comparison
        remote_instrument_map = {inst['instrument_id']: inst for inst in instruments}
        # Filter out local instruments with no instrument_id to prevent errors
        local_instruments = db.query(Instrument).filter(Instrument.instrument_id != None).all()
        local_instrument_map = {inst.instrument_id: inst for inst in local_instruments}

        remote_ids = set(remote_instrument_map.keys())
        local_ids = set(local_instrument_map.keys())

        # Identify what to add, update, or delete
        ids_to_add = remote_ids - local_ids
        ids_to_update = remote_ids.intersection(local_ids)
        ids_to_delete = local_ids - remote_ids

        # --- Perform database operations ---
        # 1. Delete stale instruments
        if ids_to_delete:
            db.query(Instrument).filter(Instrument.instrument_id.in_(ids_to_delete)).delete(synchronize_session=False)

        # 2. Update existing instruments
        now = datetime.utcnow()
        for inst_id in ids_to_update:
            # Fetch the record to update using filter_by for the unique key, not get()
            local_inst = db.query(Instrument).filter_by(instrument_id=inst_id).one()
            remote_inst_data = remote_instrument_map[inst_id]
            for key, value in remote_inst_data.items():
                setattr(local_inst, key, value)
            local_inst.updated_at = now

        # 3. Add new instruments
        for inst_id in ids_to_add:
            new_inst_data = remote_instrument_map[inst_id]
            new_inst = Instrument(**new_inst_data)
            new_inst.created_at = now
            new_inst.updated_at = now
            db.add(new_inst)

        db.commit()

        return len(ids_to_add), len(ids_to_update), len(ids_to_delete)

    except Exception as e:
        db.rollback()
        print(f"An error occurred during database synchronization: {e}")
        raise
    finally:
        db.close()


if __name__ == '__main__':
    # Example usage:
    # Fetch all futures instruments from SHFE and CFFEX for specific products
    params = {
        "types": "futures",
        "markets": "SHFE,CFFEX",
        "products": "au,rb,IF,IM"
    }

    print("Fetching instruments with params:", params)
    instrument_data = fetch_instruments(params)

    if instrument_data:
        transformed_instruments = transform_data(instrument_data)
        print(f"Successfully fetched and transformed {len(transformed_instruments)} instruments.")

        # To-Do: Add database synchronization logic here
        try:
            added, updated, deleted = sync_to_database(transformed_instruments)
            print("\nSynchronization Report:")
            print(f"- New instruments added: {added}")
            print(f"- Existing instruments updated: {updated}")
            print(f"- Stale instruments deleted: {deleted}")
            print("\nDatabase synchronization complete!")
        except Exception as e:
            print(f"Synchronization failed: {e}")

        # For now, just print the first 5 for verification
        print("\nFirst 5 transformed instruments:")
        for instrument in transformed_instruments[:5]:
            print(json.dumps(instrument, indent=2, ensure_ascii=False))

