# WebSocket 应用功能文档

本目录包含WebSocket应用相关的功能文档。

## 文档列表

1. [实时行情数据订阅方案](./03-real-time-market-data-subscription.md) - 详细的技术方案设计
2. [实施指南](./IMPLEMENTATION_GUIDE.md) - 功能实施的详细说明
3. [架构重构文档](./ARCHITECTURE_REFACTOR.md) - 解决循环依赖的架构重构
4. [最新更新](./LATEST_UPDATES.md) - 最新的功能更新和优化记录

## 功能概述

WebSocket实时行情订阅功能允许App端用户订阅指定合约的实时行情数据，并通过WebSocket连接实时推送更新。

### 主要特性

- **实时订阅**: 支持动态订阅和取消订阅合约行情
- **数据推送**: 基于Redis发布订阅机制的实时数据推送
- **架构优化**: 基于事件处理器注册的可扩展架构
- **响应式更新**: 与Vue 3响应式系统完美兼容
- **错误处理**: 完善的错误处理和恢复机制

### 技术栈

- **前端**: Vue 3 + Pinia + WebSocket
- **后端**: Go + Gin + Gorilla WebSocket
- **数据存储**: Redis (String类型，JSON格式)
- **通信协议**: WebSocket + JSON消息格式

## 快速开始

### 1. 启动后端服务
```bash
cd admin/server
go run .
```

### 2. 启动前端应用
```bash
cd app
pnpm dev
```

### 3. 访问演示页面
打开浏览器访问前端应用，导航到行情演示页面进行测试。

## 更新历史

### 2025-07-28
- ✅ 完成架构重构，解决循环依赖问题
- ✅ 优化Vue 3响应式数据结构
- ✅ 修复Redis数据类型处理问题
- ✅ 统一数据字段命名规范
- ✅ 优化日志输出和错误处理

### 初始版本
- ✅ 实现基本的WebSocket通信
- ✅ 完成行情订阅和推送功能
- ✅ 集成Redis发布订阅机制

## 贡献指南

1. 阅读相关文档了解架构设计
2. 遵循现有的代码规范和命名约定
3. 添加适当的测试用例
4. 更新相关文档

## 支持

如有问题或建议，请查阅相关文档或联系开发团队。
