<template>
  <view class="combination-selector">
    <wd-select-picker
      :columns="combinations"
      :model-value="modelValue"
      @confirm="handleConfirm"
      @change="handleChange"
      :title="title"
      :disabled="disabled"
      type="radio"
      :show-confirm="!autoComplete"
      filterable
      filter-placeholder="搜索交易组合"
      use-default-slot
    >
      <view class="combination-card">
        <view class="combination-content">
          <view class="combination-left">
            <text class="combination-label">被点价方</text>
            <text class="combination-name">{{ currentDisplay.setterName || '请选择' }}</text>
          </view>
          <view class="combination-divider"></view>
          <view class="combination-right">
            <text class="combination-label">期货合约</text>
            <text class="combination-name">{{ currentDisplay.instrumentName || '交易组合' }}</text>
          </view>
        </view>
      </view>
    </wd-select-picker>
  </view>
</template>

<script lang="ts" setup>
import { defineProps, defineEmits } from 'vue'

// 类型定义
interface CombinationOption {
  label: string
  value: string
  setterID: number
  instrumentRefID: number
  setterName: string
  instrumentName: string
}

interface CurrentDisplay {
  setterName: string
  instrumentName: string
}

interface ConfirmEvent {
  value: string
  selectedItems?: CombinationOption[]
}

interface ChangeEvent {
  value: string
}

// Props 定义
const props = withDefaults(defineProps<{
  combinations: CombinationOption[]
  modelValue: string
  currentDisplay: CurrentDisplay
  title?: string
  disabled?: boolean
  autoComplete?: boolean
}>(), {
  title: '选择交易组合',
  disabled: false,
  autoComplete: true
})

// Events 定义
const emit = defineEmits<{
  'update:modelValue': [value: string]
  'change': [value: string]
}>()

// 事件处理
function handleConfirm({ value }: ConfirmEvent) {
  console.log('SelectPicker confirm:', value)
  // 更新 v-model
  emit('update:modelValue', value)
  // 发射变更事件
  emit('change', value)
}

// 处理选择变更事件（用于自动完成）
function handleChange({ value }: ChangeEvent) {
  console.log('SelectPicker change:', value)
  if (props.autoComplete) {
    // 自动完成模式下，选择即确认
    emit('update:modelValue', value)
    emit('change', value)
  }
}
</script>

<style lang="scss" scoped>
.combination-selector {
  .combination-card {
    border: 1px solid #e5e7eb;
    border-radius: 12rpx;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      border-color: #3b82f6;
      box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.15);
      transform: translateY(-2rpx);
    }

    .combination-content {
      display: flex;
      align-items: center;
      padding: 24rpx 20rpx;
      position: relative;

      .combination-left,
      .combination-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;

        .combination-label {
          font-size: 22rpx;
          color: #64748b;
          margin-bottom: 8rpx;
          font-weight: 400;
        }

        .combination-name {
          font-size: 28rpx;
          color: #1e293b;
          font-weight: 600;
          text-align: center;
          line-height: 1.3;
        }
      }

      .combination-divider {
        width: 2rpx;
        height: 60rpx;
        background: linear-gradient(to bottom, transparent, #cbd5e1, transparent);
        margin: 0 20rpx;
      }
    }
  }
}
</style>
