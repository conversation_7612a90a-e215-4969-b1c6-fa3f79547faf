# Order客户端软件开发规格说明书

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2024-12-26
- **文档类型**: 软件开发规格说明书 (Software Requirements Specification)
- **目标受众**: 开发团队、AI开发助手、项目管理人员
- **项目路径**: `c:\Users\<USER>\work\NewDianJia\order\`

## 1. 项目概述

### 1.1 项目背景
Order客户端是一个基于vnpy框架的期货交易客户端软件，主要功能是连接交易服务器，接收交易指令，执行期货交易，并将交易结果反馈给服务器。

### 1.2 项目目标
- 构建一个稳定、高效的期货交易客户端
- 实现与服务器的实时WebSocket通信
- 支持多账户同时登录和交易
- 提供直观的用户界面和实时状态监控
- 确保交易数据的准确性和安全性

### 1.3 技术架构
- **UI框架**: PySide6 (基于vnpy UI框架扩展)
- **交易接口**: vnpy_tts (第一版本), vnpy_ctp (未来版本)
- **通信协议**: WebSocket
- **事件处理**: vnpy EventEngine
- **开发语言**: Python 3.13+

## 2. 功能需求规格

### 2.1 WebSocket连接功能

#### 2.1.1 连接管理
**功能描述**: 建立与服务器的WebSocket连接，维护连接状态

**技术要求**:
- 连接地址: `ws://server_host/ws/order`
- 支持自动重连机制
- 连接状态实时显示
- 心跳检测机制

**参考文档**: `docs/features/08-order-client-websocket-spec/`

**实现要点**:
```python
class OrderWebSocketClient:
    def __init__(self, server_url: str):
        self.server_url = server_url
        self.websocket = None
        self.is_connected = False
        self.heartbeat_interval = 30  # 秒
    
    async def connect(self):
        """建立WebSocket连接"""
        pass
    
    async def send_heartbeat(self):
        """发送心跳包"""
        pass
    
    async def handle_message(self, message):
        """处理接收到的消息"""
        pass
```

#### 2.1.2 消息处理
**功能描述**: 处理服务器发送的各类消息

**消息类型**:
- 心跳消息
- 下单指令
- 撤单指令
- 账户查询请求
- 持仓查询请求

### 2.2 用户登录功能

#### 2.2.1 登录窗口设计
**功能描述**: 提供用户登录界面，支持多种登录方式

**UI设计要求**:
- 窗口大小: 400x300像素
- 居中显示
- 支持用户名密码登录
- 支持手机号验证码登录
- 登录状态指示器

**参考实现**: `app/src/pages/login/index.vue`

**界面布局**:
```
┌─────────────────────────────────────┐
│           Order交易客户端            │
├─────────────────────────────────────┤
│  登录方式: [用户名密码] [手机验证码]  │
│                                     │
│  用户名: [________________]          │
│  密码:   [________________]          │
│                                     │
│  手机号: [________________]          │
│  验证码: [________] [获取验证码]      │
│                                     │
│  [记住密码] [自动登录]               │
│                                     │
│  状态: ●未连接 ●已连接 ●登录中       │
│                                     │
│        [登录]    [取消]              │
└─────────────────────────────────────┘
```

#### 2.2.2 登录流程
**流程步骤**:
1. 用户选择登录方式
2. 输入登录凭据
3. 客户端通过HTTP接口发送登录请求
4. 服务器验证并返回结果，本地存储token用于免密码使用，同时socket连接使用token更新为登录状态
5. 更新登录状态显示

#### 2.2.3 登录功能详细实现方案

##### 2.2.3.1 登录架构设计

**组件架构**:
```
┌─────────────────────────────────────┐
│           LoginWindow               │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │LoginWidget  │ │ StatusIndicator │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│          LoginManager               │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │AuthService  │ │  TokenManager   │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│         NetworkLayer                │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │HttpClient   │ │WebSocketClient  │ │
│  └─────────────┘ └─────────────────┘ │
└─────────────────────────────────────┘
```

##### 2.2.3.2 核心类设计

**1. LoginManager - 登录管理器**
```python
class LoginManager:
    def __init__(self, event_engine: EventEngine):
        self.event_engine = event_engine
        self.auth_service = AuthService()
        self.token_manager = TokenManager()
        self.websocket_client = None
        self.current_user = None

    async def login_with_password(self, username: str, password: str) -> LoginResult:
        """用户名密码登录"""

    async def login_with_phone(self, phone: str, code: str) -> LoginResult:
        """手机验证码登录"""

    async def auto_login(self) -> LoginResult:
        """自动登录（使用保存的token）"""

    def logout(self):
        """登出"""

    def is_logged_in(self) -> bool:
        """检查登录状态"""
```

**2. AuthService - 认证服务**
```python
class AuthService:
    def __init__(self):
        self.base_url = "https://api.server.com"
        self.http_client = HttpClient()

    async def authenticate_password(self, username: str, password: str) -> AuthResponse:
        """密码认证"""

    async def authenticate_phone(self, phone: str, code: str) -> AuthResponse:
        """手机验证码认证"""

    async def send_sms_code(self, phone: str) -> bool:
        """发送短信验证码"""

    async def refresh_token(self, refresh_token: str) -> AuthResponse:
        """刷新token"""

    async def validate_token(self, token: str) -> bool:
        """验证token有效性"""
```

**3. TokenManager - Token管理器**
```python
class TokenManager:
    def __init__(self):
        self.token_file = "user_token.json"
        self.encryption_key = self._get_machine_key()

    def save_token(self, token_data: TokenData):
        """保存token到本地（加密存储）"""

    def load_token(self) -> Optional[TokenData]:
        """从本地加载token"""

    def clear_token(self):
        """清除本地token"""

    def is_token_valid(self, token_data: TokenData) -> bool:
        """检查token是否有效（未过期）"""

    def _encrypt_data(self, data: str) -> str:
        """加密数据"""

    def _decrypt_data(self, encrypted_data: str) -> str:
        """解密数据"""
```

##### 2.2.3.3 数据结构定义

**登录相关数据结构**:
```python
@dataclass
class LoginCredentials:
    login_type: str  # "password" or "phone"
    username: Optional[str] = None
    password: Optional[str] = None
    phone: Optional[str] = None
    sms_code: Optional[str] = None
    remember_me: bool = False
    auto_login: bool = False

@dataclass
class TokenData:
    access_token: str
    refresh_token: str
    expires_at: datetime
    user_info: dict
    created_at: datetime

@dataclass
class LoginResult:
    success: bool
    message: str
    user_info: Optional[dict] = None
    token_data: Optional[TokenData] = None
    error_code: Optional[str] = None

@dataclass
class AuthResponse:
    success: bool
    access_token: Optional[str] = None
    refresh_token: Optional[str] = None
    expires_in: Optional[int] = None
    user_info: Optional[dict] = None
    error_message: Optional[str] = None
    error_code: Optional[str] = None
```

##### 2.2.3.4 登录流程实现

**1. 用户名密码登录流程**:
```python
async def login_with_password(self, username: str, password: str) -> LoginResult:
    try:
        # 1. 参数验证
        if not username or not password:
            return LoginResult(False, "用户名和密码不能为空")

        # 2. 发送认证请求
        auth_response = await self.auth_service.authenticate_password(username, password)

        if not auth_response.success:
            return LoginResult(False, auth_response.error_message, error_code=auth_response.error_code)

        # 3. 保存token
        token_data = TokenData(
            access_token=auth_response.access_token,
            refresh_token=auth_response.refresh_token,
            expires_at=datetime.now() + timedelta(seconds=auth_response.expires_in),
            user_info=auth_response.user_info,
            created_at=datetime.now()
        )

        if self.remember_me:
            self.token_manager.save_token(token_data)

        # 4. 更新WebSocket连接状态
        await self._update_websocket_auth(token_data.access_token)

        # 5. 更新当前用户信息
        self.current_user = auth_response.user_info

        # 6. 发送登录成功事件
        self.event_engine.put(Event(EVENT_USER_LOGIN_SUCCESS, token_data))

        return LoginResult(True, "登录成功", auth_response.user_info, token_data)

    except Exception as e:
        logger.error(f"登录失败: {str(e)}")
        return LoginResult(False, f"登录失败: {str(e)}")
```

**2. 手机验证码登录流程**:
```python
async def login_with_phone(self, phone: str, code: str) -> LoginResult:
    try:
        # 1. 参数验证
        if not phone or not code:
            return LoginResult(False, "手机号和验证码不能为空")

        if not self._validate_phone_format(phone):
            return LoginResult(False, "手机号格式不正确")

        # 2. 发送认证请求
        auth_response = await self.auth_service.authenticate_phone(phone, code)

        # 后续流程与密码登录相同
        # ...

    except Exception as e:
        logger.error(f"手机登录失败: {str(e)}")
        return LoginResult(False, f"登录失败: {str(e)}")
```

**3. 自动登录流程**:
```python
async def auto_login(self) -> LoginResult:
    try:
        # 1. 加载本地token
        token_data = self.token_manager.load_token()
        if not token_data:
            return LoginResult(False, "没有保存的登录信息")

        # 2. 检查token是否过期
        if not self.token_manager.is_token_valid(token_data):
            # 尝试刷新token
            auth_response = await self.auth_service.refresh_token(token_data.refresh_token)
            if not auth_response.success:
                self.token_manager.clear_token()
                return LoginResult(False, "登录信息已过期，请重新登录")

            # 更新token
            token_data.access_token = auth_response.access_token
            token_data.expires_at = datetime.now() + timedelta(seconds=auth_response.expires_in)
            self.token_manager.save_token(token_data)

        # 3. 验证token有效性
        if not await self.auth_service.validate_token(token_data.access_token):
            self.token_manager.clear_token()
            return LoginResult(False, "登录信息无效，请重新登录")

        # 4. 更新WebSocket连接状态
        await self._update_websocket_auth(token_data.access_token)

        # 5. 更新当前用户信息
        self.current_user = token_data.user_info

        # 6. 发送登录成功事件
        self.event_engine.put(Event(EVENT_USER_LOGIN_SUCCESS, token_data))

        return LoginResult(True, "自动登录成功", token_data.user_info, token_data)

    except Exception as e:
        logger.error(f"自动登录失败: {str(e)}")
        return LoginResult(False, f"自动登录失败: {str(e)}")
```

##### 2.2.3.5 WebSocket认证集成

**WebSocket认证更新**:
```python
async def _update_websocket_auth(self, access_token: str):
    """更新WebSocket连接的认证状态"""
    if self.websocket_client and self.websocket_client.is_connected:
        # 发送认证消息到WebSocket
        auth_message = {
            "type": "auth",
            "data": {
                "token": access_token,
                "timestamp": int(time.time())
            }
        }
        await self.websocket_client.send_message(auth_message)
    else:
        # 如果WebSocket未连接，保存token供连接时使用
        self.pending_auth_token = access_token
```

##### 2.2.3.6 安全措施

**1. Token安全存储**:
- 使用机器特征生成加密密钥
- AES加密存储token数据
- 定期清理过期token

**2. 网络安全**:
- HTTPS通信加密
- 请求签名验证
- 防重放攻击

**3. 输入验证**:
- 用户名格式验证
- 密码强度检查
- 手机号格式验证
- 验证码格式检查

##### 2.2.3.7 错误处理

**错误类型定义**:
```python
class LoginErrorCode:
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
    ACCOUNT_LOCKED = "ACCOUNT_LOCKED"
    NETWORK_ERROR = "NETWORK_ERROR"
    SERVER_ERROR = "SERVER_ERROR"
    TOKEN_EXPIRED = "TOKEN_EXPIRED"
    SMS_CODE_INVALID = "SMS_CODE_INVALID"
    SMS_CODE_EXPIRED = "SMS_CODE_EXPIRED"
    PHONE_NOT_REGISTERED = "PHONE_NOT_REGISTERED"
```

**错误处理策略**:
- 网络错误：自动重试3次
- 服务器错误：显示友好错误信息
- 认证失败：记录失败次数，防止暴力破解
- Token过期：自动尝试刷新token

##### 2.2.3.8 事件定义

**登录相关事件**:
```python
# 登录事件
EVENT_USER_LOGIN_START = "user.login.start"
EVENT_USER_LOGIN_SUCCESS = "user.login.success"
EVENT_USER_LOGIN_FAILED = "user.login.failed"
EVENT_USER_LOGOUT = "user.logout"

# 认证事件
EVENT_TOKEN_REFRESHED = "auth.token.refreshed"
EVENT_TOKEN_EXPIRED = "auth.token.expired"
EVENT_AUTH_STATUS_CHANGED = "auth.status.changed"

# SMS事件
EVENT_SMS_CODE_SENT = "sms.code.sent"
EVENT_SMS_CODE_SEND_FAILED = "sms.code.send.failed"
```

##### 2.2.3.9 UI实现方案

**1. LoginWindow - 登录窗口主类**
```python
class LoginWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.login_manager = LoginManager(event_engine)
        self.setup_ui()
        self.setup_connections()
        self.setup_events()

    def setup_ui(self):
        """设置UI界面"""
        self.setWindowTitle("Order交易客户端 - 登录")
        self.setFixedSize(400, 350)
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)

        # 主布局
        main_layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("Order交易客户端")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px;")

        # 登录方式选择
        self.login_type_group = QButtonGroup()
        self.password_radio = QRadioButton("用户名密码")
        self.phone_radio = QRadioButton("手机验证码")
        self.password_radio.setChecked(True)

        # 登录表单
        self.login_widget = LoginWidget()

        # 状态指示器
        self.status_indicator = StatusIndicator()

        # 按钮
        button_layout = QHBoxLayout()
        self.login_button = QPushButton("登录")
        self.cancel_button = QPushButton("取消")

        # 添加到布局
        main_layout.addWidget(title_label)
        main_layout.addWidget(self.create_login_type_widget())
        main_layout.addWidget(self.login_widget)
        main_layout.addWidget(self.status_indicator)
        main_layout.addLayout(button_layout)

    def create_login_type_widget(self) -> QWidget:
        """创建登录方式选择组件"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.addWidget(QLabel("登录方式:"))
        layout.addWidget(self.password_radio)
        layout.addWidget(self.phone_radio)
        layout.addStretch()
        return widget
```

**2. LoginWidget - 登录表单组件**
```python
class LoginWidget(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """设置登录表单UI"""
        layout = QVBoxLayout(self)

        # 用户名密码登录区域
        self.password_group = QGroupBox()
        password_layout = QFormLayout(self.password_group)

        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("请输入用户名")
        self.password_edit = QLineEdit()
        self.password_edit.setPlaceholderText("请输入密码")
        self.password_edit.setEchoMode(QLineEdit.Password)

        password_layout.addRow("用户名:", self.username_edit)
        password_layout.addRow("密码:", self.password_edit)

        # 手机验证码登录区域
        self.phone_group = QGroupBox()
        phone_layout = QFormLayout(self.phone_group)

        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText("请输入手机号")

        code_layout = QHBoxLayout()
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText("请输入验证码")
        self.send_code_button = QPushButton("获取验证码")
        self.send_code_button.setFixedWidth(100)
        code_layout.addWidget(self.code_edit)
        code_layout.addWidget(self.send_code_button)

        phone_layout.addRow("手机号:", self.phone_edit)
        phone_layout.addRow("验证码:", code_layout)

        # 选项
        options_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("记住密码")
        self.auto_login_checkbox = QCheckBox("自动登录")
        options_layout.addWidget(self.remember_checkbox)
        options_layout.addWidget(self.auto_login_checkbox)
        options_layout.addStretch()

        # 添加到主布局
        layout.addWidget(self.password_group)
        layout.addWidget(self.phone_group)
        layout.addLayout(options_layout)

        # 默认显示密码登录
        self.phone_group.hide()

    def switch_to_password_mode(self):
        """切换到密码登录模式"""
        self.password_group.show()
        self.phone_group.hide()

    def switch_to_phone_mode(self):
        """切换到手机登录模式"""
        self.password_group.hide()
        self.phone_group.show()

    def get_login_credentials(self) -> LoginCredentials:
        """获取登录凭据"""
        if self.password_group.isVisible():
            return LoginCredentials(
                login_type="password",
                username=self.username_edit.text().strip(),
                password=self.password_edit.text(),
                remember_me=self.remember_checkbox.isChecked(),
                auto_login=self.auto_login_checkbox.isChecked()
            )
        else:
            return LoginCredentials(
                login_type="phone",
                phone=self.phone_edit.text().strip(),
                sms_code=self.code_edit.text().strip(),
                remember_me=self.remember_checkbox.isChecked(),
                auto_login=self.auto_login_checkbox.isChecked()
            )
```

**3. StatusIndicator - 状态指示器**
```python
class StatusIndicator(QWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """设置状态指示器UI"""
        layout = QHBoxLayout(self)

        self.status_icon = QLabel()
        self.status_icon.setFixedSize(16, 16)

        self.status_text = QLabel("未连接")

        layout.addWidget(QLabel("状态:"))
        layout.addWidget(self.status_icon)
        layout.addWidget(self.status_text)
        layout.addStretch()

        self.set_status("disconnected")

    def set_status(self, status: str, message: str = ""):
        """设置状态"""
        status_config = {
            "disconnected": ("🔴", "未连接"),
            "connecting": ("🟡", "连接中"),
            "connected": ("🟢", "已连接"),
            "authenticating": ("🟡", "登录中"),
            "authenticated": ("🟢", "已登录"),
            "error": ("🔴", "连接失败")
        }

        if status in status_config:
            icon, text = status_config[status]
            self.status_icon.setText(icon)
            self.status_text.setText(message or text)
```

##### 2.2.3.10 登录窗口事件处理

**事件连接和处理**:
```python
def setup_connections(self):
    """设置信号连接"""
    # 登录方式切换
    self.password_radio.toggled.connect(self.on_login_type_changed)
    self.phone_radio.toggled.connect(self.on_login_type_changed)

    # 按钮事件
    self.login_button.clicked.connect(self.on_login_clicked)
    self.cancel_button.clicked.connect(self.reject)
    self.login_widget.send_code_button.clicked.connect(self.on_send_code_clicked)

    # 回车键登录
    self.login_widget.password_edit.returnPressed.connect(self.on_login_clicked)
    self.login_widget.code_edit.returnPressed.connect(self.on_login_clicked)

def setup_events(self):
    """设置事件监听"""
    self.login_manager.event_engine.register(EVENT_USER_LOGIN_SUCCESS, self.on_login_success)
    self.login_manager.event_engine.register(EVENT_USER_LOGIN_FAILED, self.on_login_failed)
    self.login_manager.event_engine.register(EVENT_SMS_CODE_SENT, self.on_sms_code_sent)

async def on_login_clicked(self):
    """处理登录按钮点击"""
    try:
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")
        self.status_indicator.set_status("authenticating")

        credentials = self.login_widget.get_login_credentials()

        if credentials.login_type == "password":
            result = await self.login_manager.login_with_password(
                credentials.username, credentials.password
            )
        else:
            result = await self.login_manager.login_with_phone(
                credentials.phone, credentials.sms_code
            )

        if result.success:
            self.accept()  # 关闭登录窗口
        else:
            self.show_error_message(result.message)

    except Exception as e:
        self.show_error_message(f"登录失败: {str(e)}")
    finally:
        self.login_button.setEnabled(True)
        self.login_button.setText("登录")

async def on_send_code_clicked(self):
    """处理发送验证码按钮点击"""
    phone = self.login_widget.phone_edit.text().strip()
    if not phone:
        self.show_error_message("请输入手机号")
        return

    try:
        self.login_widget.send_code_button.setEnabled(False)
        success = await self.login_manager.auth_service.send_sms_code(phone)

        if success:
            self.start_countdown_timer()
        else:
            self.show_error_message("发送验证码失败")

    except Exception as e:
        self.show_error_message(f"发送验证码失败: {str(e)}")
    finally:
        if not hasattr(self, 'countdown_timer') or not self.countdown_timer.isActive():
            self.login_widget.send_code_button.setEnabled(True)

def start_countdown_timer(self):
    """启动验证码倒计时"""
    self.countdown_seconds = 60
    self.countdown_timer = QTimer()
    self.countdown_timer.timeout.connect(self.update_countdown)
    self.countdown_timer.start(1000)
    self.update_countdown()

def update_countdown(self):
    """更新倒计时显示"""
    if self.countdown_seconds > 0:
        self.login_widget.send_code_button.setText(f"重新发送({self.countdown_seconds})")
        self.countdown_seconds -= 1
    else:
        self.countdown_timer.stop()
        self.login_widget.send_code_button.setText("获取验证码")
        self.login_widget.send_code_button.setEnabled(True)
```

##### 2.2.3.11 样式设计

**登录窗口样式表**:
```python
LOGIN_WINDOW_STYLE = """
QDialog {
    background-color: #f5f5f5;
    border-radius: 8px;
}

QLabel {
    color: #333333;
}

QLineEdit {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

QLineEdit:focus {
    border-color: #007acc;
}

QPushButton {
    padding: 8px 16px;
    background-color: #007acc;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}

QPushButton:hover {
    background-color: #005a9e;
}

QPushButton:pressed {
    background-color: #004578;
}

QPushButton:disabled {
    background-color: #cccccc;
    color: #666666;
}

QGroupBox {
    font-weight: bold;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-top: 10px;
    padding-top: 10px;
}

QCheckBox {
    spacing: 5px;
}

QRadioButton {
    spacing: 5px;
}
"""

##### 2.2.3.12 实施步骤

**第1步：基础架构搭建 (1天)**
1. 创建登录相关的目录结构
2. 定义数据结构和接口
3. 创建基础类框架

**第2步：认证服务实现 (2天)**
1. 实现AuthService类
2. 实现HTTP客户端
3. 实现API接口调用
4. 添加错误处理和重试机制

**第3步：Token管理实现 (1天)**
1. 实现TokenManager类
2. 实现加密存储功能
3. 实现token验证和刷新
4. 添加安全措施

**第4步：登录管理器实现 (2天)**
1. 实现LoginManager类
2. 实现各种登录方式
3. 集成认证服务和token管理
4. 实现事件发送

**第5步：UI界面实现 (3天)**
1. 实现LoginWindow主窗口
2. 实现LoginWidget表单组件
3. 实现StatusIndicator状态指示器
4. 添加样式和动画效果

**第6步：事件集成 (1天)**
1. 集成EventEngine
2. 实现事件监听和处理
3. 连接UI和业务逻辑

**第7步：WebSocket集成 (1天)**
1. 实现WebSocket认证更新
2. 测试token传递
3. 验证认证状态同步

**第8步：测试和优化 (2天)**
1. 单元测试
2. 集成测试
3. UI测试
4. 性能优化

##### 2.2.3.13 测试方案

**1. 单元测试**
```python
class TestAuthService(unittest.TestCase):
    def setUp(self):
        self.auth_service = AuthService()

    async def test_authenticate_password_success(self):
        """测试密码认证成功"""
        result = await self.auth_service.authenticate_password("testuser", "testpass")
        self.assertTrue(result.success)
        self.assertIsNotNone(result.access_token)

    async def test_authenticate_password_invalid(self):
        """测试密码认证失败"""
        result = await self.auth_service.authenticate_password("invalid", "invalid")
        self.assertFalse(result.success)
        self.assertEqual(result.error_code, "INVALID_CREDENTIALS")

class TestTokenManager(unittest.TestCase):
    def setUp(self):
        self.token_manager = TokenManager()

    def test_save_and_load_token(self):
        """测试token保存和加载"""
        token_data = TokenData(
            access_token="test_token",
            refresh_token="refresh_token",
            expires_at=datetime.now() + timedelta(hours=1),
            user_info={"username": "test"},
            created_at=datetime.now()
        )

        self.token_manager.save_token(token_data)
        loaded_token = self.token_manager.load_token()

        self.assertEqual(loaded_token.access_token, token_data.access_token)
        self.assertEqual(loaded_token.user_info, token_data.user_info)
```

**2. 集成测试**
```python
class TestLoginIntegration(unittest.TestCase):
    def setUp(self):
        self.event_engine = EventEngine()
        self.login_manager = LoginManager(self.event_engine)

    async def test_full_login_flow(self):
        """测试完整登录流程"""
        # 1. 密码登录
        result = await self.login_manager.login_with_password("testuser", "testpass")
        self.assertTrue(result.success)

        # 2. 检查登录状态
        self.assertTrue(self.login_manager.is_logged_in())

        # 3. 登出
        self.login_manager.logout()
        self.assertFalse(self.login_manager.is_logged_in())

    async def test_auto_login(self):
        """测试自动登录"""
        # 先正常登录并保存token
        await self.login_manager.login_with_password("testuser", "testpass")

        # 创建新的登录管理器实例
        new_login_manager = LoginManager(self.event_engine)

        # 测试自动登录
        result = await new_login_manager.auto_login()
        self.assertTrue(result.success)
```

**3. UI测试**
```python
class TestLoginWindow(QTestCase):
    def setUp(self):
        self.app = QApplication.instance() or QApplication([])
        self.login_window = LoginWindow()

    def test_ui_initialization(self):
        """测试UI初始化"""
        self.assertIsNotNone(self.login_window.login_widget)
        self.assertIsNotNone(self.login_window.status_indicator)
        self.assertTrue(self.login_window.password_radio.isChecked())

    def test_login_type_switch(self):
        """测试登录方式切换"""
        # 切换到手机登录
        self.login_window.phone_radio.setChecked(True)
        self.assertTrue(self.login_window.login_widget.phone_group.isVisible())
        self.assertFalse(self.login_window.login_widget.password_group.isVisible())

        # 切换回密码登录
        self.login_window.password_radio.setChecked(True)
        self.assertFalse(self.login_window.login_widget.phone_group.isVisible())
        self.assertTrue(self.login_window.login_widget.password_group.isVisible())

    def test_form_validation(self):
        """测试表单验证"""
        credentials = self.login_window.login_widget.get_login_credentials()
        self.assertEqual(credentials.login_type, "password")
```

**4. 性能测试**
- 登录响应时间 < 3秒
- UI响应时间 < 100ms
- 内存使用 < 50MB
- token加密/解密时间 < 10ms

##### 2.2.3.14 部署配置

**配置文件示例 (config/auth_config.yaml)**:
```yaml
auth:
  # 服务器配置
  server:
    base_url: "https://api.server.com"
    timeout: 30
    retry_count: 3

  # Token配置
  token:
    storage_file: "user_token.json"
    encryption_enabled: true
    auto_refresh: true
    refresh_threshold: 300  # 5分钟

  # SMS配置
  sms:
    countdown_seconds: 60
    max_retry_count: 3

  # 安全配置
  security:
    password_min_length: 6
    max_login_attempts: 5
    lockout_duration: 300  # 5分钟
```

这个详细的登录功能实现方案包含了：

1. **完整的架构设计** - 分层清晰，职责明确
2. **详细的类设计** - 包含所有核心类和方法
3. **安全的Token管理** - 加密存储，自动刷新
4. **友好的UI设计** - 支持两种登录方式，状态清晰
5. **完善的错误处理** - 各种异常情况的处理
6. **全面的测试方案** - 单元测试、集成测试、UI测试
7. **详细的实施步骤** - 分步骤实现，便于管理

这个方案可以直接用于开发实施，每个组件都有明确的接口和实现要求。
```

### 2.3 期货账户管理

#### 2.3.1 账户登录功能
**功能描述**: 管理期货账户的登录和状态监控

**技术要求**:
- 支持vnpy_tts接口 (第一版本)
- 预留vnpy_ctp接口切换能力
- 从服务器获取账户配置
- 本地输入交易密码
- 支持多账户同时登录

**账户配置数据结构**:
```python
@dataclass
class FutureAccount:
    account_id: str          # 账户ID
    broker_id: str          # 期货公司代码
    user_id: str            # 用户代码
    server_address: str     # 服务器地址
    auth_code: str          # 授权码
    app_id: str             # 应用ID
    gateway_type: str       # 网关类型 (TTS/CTP)
    status: str             # 登录状态
```

#### 2.3.2 账户状态监控
**状态类型**:
- 未连接 (红色)
- 连接中 (黄色)
- 已连接 (绿色)
- 连接失败 (红色闪烁)

**显示位置**: 主窗口状态栏和账户管理面板

### 2.4 交易功能

#### 2.4.1 接收下单指令
**功能描述**: 从WebSocket接收服务器的下单指令并执行

**消息格式**:
```json
{
    "type": "place_order",
    "data": {
        "account_id": "账户ID",
        "symbol": "合约代码",
        "direction": "买卖方向",
        "offset": "开平仓",
        "price": "价格",
        "volume": "数量",
        "order_type": "订单类型"
    }
}
```

#### 2.4.2 下单执行流程
1. 接收WebSocket下单消息
2. 验证账户状态和参数
3. 通过EventEngine发送到对应网关
4. 网关执行下单操作
5. 接收下单回报
6. 通过WebSocket反馈给服务器

#### 2.4.3 撤单功能
**功能描述**: 处理撤单指令

**实现要点**:
- 接收撤单指令
- 查找对应的原始订单
- 执行撤单操作
- 反馈撤单结果

### 2.5 数据同步功能

#### 2.5.1 账户数据同步
**同步内容**:
- 资金余额
- 可用资金
- 冻结资金
- 手续费
- 保证金

**同步频率**: 每30秒或有变化时立即同步

#### 2.5.2 持仓数据同步
**同步内容**:
- 合约代码
- 持仓方向
- 持仓数量
- 开仓价格
- 当前价格
- 浮动盈亏

**同步频率**: 每10秒或有变化时立即同步

## 3. 技术架构设计

### 3.1 整体架构

```
┌─────────────────────────────────────┐
│              UI Layer               │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │ Login Window│ │  Main Window    │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│           Business Layer            │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │WebSocket Mgr│ │ Account Manager │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│            Gateway Layer            │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │  TTS Gateway│ │  CTP Gateway    │ │
│  └─────────────┘ └─────────────────┘ │
├─────────────────────────────────────┤
│             Event Engine            │
└─────────────────────────────────────┘
```

### 3.2 核心组件设计

#### 3.2.1 WebSocket管理器
```python
class WebSocketManager:
    def __init__(self, event_engine: EventEngine):
        self.event_engine = event_engine
        self.client = None
        self.is_connected = False
    
    async def connect(self, server_url: str):
        """连接服务器"""
        pass
    
    async def send_message(self, message: dict):
        """发送消息"""
        pass
    
    def on_message(self, message: dict):
        """处理接收消息"""
        pass
```

#### 3.2.2 账户管理器
```python
class AccountManager:
    def __init__(self, event_engine: EventEngine):
        self.event_engine = event_engine
        self.accounts: Dict[str, FutureAccount] = {}
        self.gateways: Dict[str, BaseGateway] = {}
    
    def add_account(self, account: FutureAccount):
        """添加账户"""
        pass
    
    def login_account(self, account_id: str, password: str):
        """登录账户"""
        pass
    
    def get_account_status(self, account_id: str) -> str:
        """获取账户状态"""
        pass
```

### 3.3 事件系统设计

#### 3.3.1 事件类型定义
```python
# WebSocket事件
EVENT_WEBSOCKET_CONNECTED = "websocket.connected"
EVENT_WEBSOCKET_DISCONNECTED = "websocket.disconnected"
EVENT_WEBSOCKET_MESSAGE = "websocket.message"

# 账户事件
EVENT_ACCOUNT_LOGIN = "account.login"
EVENT_ACCOUNT_LOGOUT = "account.logout"
EVENT_ACCOUNT_STATUS_CHANGE = "account.status_change"

# 交易事件
EVENT_ORDER_RECEIVED = "order.received"
EVENT_ORDER_SENT = "order.sent"
EVENT_ORDER_FILLED = "order.filled"
EVENT_ORDER_CANCELLED = "order.cancelled"
```

#### 3.3.2 事件处理流程
1. UI层发起操作
2. 业务层处理逻辑
3. 通过EventEngine发送事件
4. 相关组件监听并处理事件
5. 更新UI状态

## 4. 用户界面设计

### 4.1 主窗口布局

```
┌─────────────────────────────────────────────────────────────┐
│ File  Edit  View  Tools  Help                    [_][□][×] │
├─────────────────────────────────────────────────────────────┤
│ 连接状态: ●已连接  用户: admin  账户: 3个已登录              │
├─────────────────────────────────────────────────────────────┤
│ ┌─账户管理─┐ ┌─────────────交易面板─────────────┐ ┌─持仓─┐ │
│ │账户1 ●   │ │合约: [________] 方向: [买入▼]    │ │IF2501│ │
│ │账户2 ●   │ │价格: [_______] 数量: [_____]     │ │  +10 │ │
│ │账户3 ○   │ │类型: [限价▼]   [下单] [撤单]    │ │ 5000 │ │
│ │[添加账户]│ └─────────────────────────────────┘ └──────┘ │
│ └─────────┘                                               │
│ ┌─────────────────委托订单─────────────────┐               │
│ │订单号│合约  │方向│价格 │数量│状态│时间    │               │
│ │001   │IF2501│买  │5000│10  │已成│10:30:15│               │
│ │002   │IC2501│卖  │7000│5   │部成│10:31:20│               │
│ └─────────────────────────────────────────┘               │
│ ┌─────────────────成交记录─────────────────┐               │
│ │成交号│合约  │方向│价格 │数量│手续费│时间  │               │
│ │T001  │IF2501│买  │5000│10  │50   │10:30:16│               │
│ └─────────────────────────────────────────┘               │
├─────────────────────────────────────────────────────────────┤
│ 日志: [2024-12-26 10:30:15] 账户1登录成功                   │
│      [2024-12-26 10:30:16] 接收到下单指令: IF2501 买入 10手  │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 状态指示器设计

#### 4.2.1 连接状态
- **WebSocket连接**: 
  - 🔴 未连接
  - 🟡 连接中  
  - 🟢 已连接
  - 🔴 连接失败

#### 4.2.2 账户状态
- **期货账户**:
  - ○ 未登录 (灰色)
  - ◐ 登录中 (黄色)
  - ● 已登录 (绿色)
  - ✗ 登录失败 (红色)

### 4.3 响应式设计
- 最小窗口尺寸: 1024x768
- 支持窗口缩放
- 关键信息始终可见
- 适配不同分辨率显示器

## 5. 开发计划

### 5.1 开发阶段划分

#### 阶段1: 基础框架搭建 (1-2周)
**目标**: 建立基本的项目结构和核心组件

**任务清单**:
1. **项目初始化**
   - 设置开发环境
   - 配置依赖包管理
   - 建立代码结构

2. **基础UI框架**
   - 创建主窗口基础结构
   - 实现登录窗口
   - 建立UI组件基类

3. **事件系统集成**
   - 集成vnpy EventEngine
   - 定义事件类型
   - 建立事件处理机制

**交付物**:
- 可运行的基础UI框架
- 事件系统集成完成
- 基础项目文档

#### 阶段2: WebSocket通信实现 (1-2周)
**目标**: 实现与服务器的WebSocket通信

**任务清单**:
1. **WebSocket客户端**
   - 实现连接管理
   - 实现消息收发
   - 实现心跳机制

2. **消息处理系统**
   - 定义消息格式
   - 实现消息路由
   - 实现消息验证

3. **连接状态管理**
   - 实现状态监控
   - 实现自动重连
   - 实现状态显示

**交付物**:
- WebSocket通信模块
- 消息处理系统
- 连接状态监控

#### 阶段3: 用户登录功能 (1周)
**目标**: 实现用户登录和认证功能

**任务清单**:
1. **登录界面完善**
   - 实现用户名密码登录
   - 实现手机验证码登录
   - 实现登录状态显示

2. **认证流程**
   - 实现登录请求发送
   - 实现登录响应处理
   - 实现会话管理

**交付物**:
- 完整的登录功能
- 用户认证系统
- 会话管理机制

#### 阶段4: 期货账户管理 (2-3周)
**目标**: 实现期货账户的登录和管理

**任务清单**:
1. **账户管理系统**
   - 实现账户配置管理
   - 实现账户登录功能
   - 实现多账户支持

2. **网关集成**
   - 集成vnpy_tts网关
   - 实现网关状态监控
   - 实现网关切换机制

3. **状态监控**
   - 实现账户状态显示
   - 实现连接状态监控
   - 实现错误处理

**交付物**:
- 账户管理系统
- 网关集成完成
- 状态监控功能

#### 阶段5: 交易功能实现 (2-3周)
**目标**: 实现完整的交易功能

**任务清单**:
1. **下单功能**
   - 实现下单指令接收
   - 实现下单执行
   - 实现下单反馈

2. **撤单功能**
   - 实现撤单指令处理
   - 实现撤单执行
   - 实现撤单反馈

3. **订单管理**
   - 实现订单状态跟踪
   - 实现订单历史记录
   - 实现订单查询

**交付物**:
- 完整的交易功能
- 订单管理系统
- 交易记录功能

#### 阶段6: 数据同步功能 (1-2周)
**目标**: 实现账户和持仓数据同步

**任务清单**:
1. **数据同步机制**
   - 实现账户数据同步
   - 实现持仓数据同步
   - 实现定时同步

2. **数据显示**
   - 实现账户信息显示
   - 实现持仓信息显示
   - 实现实时数据更新

**交付物**:
- 数据同步系统
- 实时数据显示
- 数据管理功能

#### 阶段7: 测试和优化 (1-2周)
**目标**: 完成系统测试和性能优化

**任务清单**:
1. **功能测试**
   - 单元测试
   - 集成测试
   - 用户验收测试

2. **性能优化**
   - 内存使用优化
   - 响应速度优化
   - 稳定性优化

3. **文档完善**
   - 用户手册
   - 开发文档
   - 部署指南

**交付物**:
- 完整的测试报告
- 性能优化报告
- 完整的项目文档

### 5.2 技术风险和应对策略

#### 5.2.1 主要技术风险
1. **vnpy版本兼容性**
   - 风险: vnpy库版本升级导致API变化
   - 应对: 锁定vnpy版本，建立兼容性测试

2. **WebSocket连接稳定性**
   - 风险: 网络不稳定导致连接中断
   - 应对: 实现健壮的重连机制和错误处理

3. **多线程并发问题**
   - 风险: UI线程和交易线程冲突
   - 应对: 使用EventEngine进行线程间通信

4. **交易数据一致性**
   - 风险: 数据同步延迟或丢失
   - 应对: 实现数据校验和补偿机制

#### 5.2.2 质量保证措施
1. **代码质量**
   - 使用类型提示
   - 代码审查机制
   - 自动化测试

2. **性能监控**
   - 内存使用监控
   - 响应时间监控
   - 错误率监控

3. **安全措施**
   - 密码本地存储加密
   - 通信数据加密
   - 访问权限控制

## 6. 总结

本规格说明书详细定义了Order客户端软件的功能需求、技术架构和开发计划。通过分阶段的开发方式，确保项目的可控性和质量。重点关注WebSocket通信的稳定性、多账户管理的复杂性和交易数据的准确性。

项目预计总开发周期为8-12周，需要密切关注技术风险，确保按时交付高质量的软件产品。
