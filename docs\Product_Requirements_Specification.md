# 产品需求规格说明书 (PRD) - 大宗商品交易风险管理平台

**版本:** 1.0
**日期:** 2025年7月21日

---

## 1. 引言

### 1.1. 目的
本文档旨在详细说明“大宗商品交易风险管理平台”的功能与非功能性需求。其目的是为产品、研发、测试及项目管理团队提供一个清晰、统一的需求基准，指导后续的产品设计、开发和验证工作。

### 1.2. 项目范围
本项目旨在为大宗商品贸易企业提供一个集基差点价交易和库存风险管理的数字化服务平台。平台的核心目标是解决传统贸易中信息不对称、操作效率低下以及价格风险敞口难以量化和管理的核心痛点。

**初期版本（MVP）将优先实现以下核心模块，并按优先级顺序开发：**
1.  **P1: 基差点价交易数字化服务平台**
2.  **P2: 智能库存风险管理与期现对冲工具**

### 1.3. 名词解释
| 术语 | 解释 |
|---|---|
| **基差** | 现货价格与期货价格之间的差额。 |
| **点价** | 贸易双方不直接确定最终价格，而是约定一个定价公式（如：期货价格 + 基差），买方在约定期限内选择某个期货价格作为最终结算价。 |
| **VaR (Value at Risk)** | 在险价值。在正常的市场条件下，衡量一项资产或投资组合在特定时间内的最大可能损失。 |
| **ERP** | Enterprise Resource Planning，企业资源计划系统。 |

---

## 2. 总体描述

### 2.1. 产品愿景
利用人工智能和大数据技术，打造一个智能、高效、安全的大宗商品交易和风险管理生态系统，降低中小贸易企业的专业门槛和操作风险，提升其市场竞争力。

### 2.2. 用户特征
| 用户角色 | 核心诉求 |
|---|---|
| **贸易商/企业主** | 快速完成交易、有效管理库存价格风险、降低运营成本。 |
| **交易员/采购经理** | 获取实时透明的报价、高效执行点价交易、获得智能化的对冲策略建议。 |
| **风控经理** | 实时监控公司风险敞口、量化库存风险、确保交易合规。 |

### 2.3. 总体功能（按优先级）

#### 2.3.1. P1: 基差点价交易数字化服务平台
- **核心价值:** 将传统线下、人工的基差点价流程线上化、自动化，提高交易效率和透明度。
- **主要功能:**
    - 实时基差数据库与行情展示
    - 卖方在线基差报价
    - 买方在线点价与锁价
    - 自动化生成标准点价确认书

#### 2.3.2. P2: 智能库存风险管理与期现对冲工具
- **核心价值:** 将分散的库存与订单数据整合，通过AI模型量化风险，并提供动态、智能的期货对冲策略。
- **主要功能:**
    - 多源数据接入与整合（ERP、行情数据）
    - 库存价格风险敞口自动化计算（VaR、压力测试）
    - 动态期货对冲策略引擎
    - 可视化风险监控看板
    - 一键生成对冲指令

### 2.4. 约束与假设
- **假设:** 用户企业具备ERP系统或能够提供结构化的采购/销售订单数据。
- **假设:** 用户愿意为高价值的风险管理和交易效率提升服务付费。
- **约束:** 平台需对接外部期货交易所的实时行情数据接口。

---

## 3. 具体需求

### 3.1. P1: 基差点价交易数字化服务平台

#### FR1.1: 基差数据库与行情展示
- **描述:** 系统需整合主流品种、港口、区域的实时现货价与期货价，计算并展示实时基差数据。
- **用户场景:** 用户可以像查看股票行情一样，在平台上查看不同商品、不同区域的基差走势图、区域基差地图和基差日报。
- **验收标准:**
    - 1.1.1: 数据至少覆盖螺纹钢、豆粕等核心交易品种。
    - 1.1.2: 基差数据更新频率不低于分钟级。
    - 1.1.3: 提供基差的K线图、分时图以及历史数据查询功能。
    - 1.1.4: 提供“区域基差地图”，直观展示全国不同仓库的基差差异。

#### FR1.2: 卖方在线报价
- **描述:** 卖方用户（如贸易商）可以在平台发布基差报价。
- **用户场景:** 贸易商登录后，选择品种、仓库、期货合约月份，输入基差值（如 `+100` 元/吨），设置报价有效期和数量，即可发布一个公开或指定的报价。
- **验收标准:**
    - 1.2.1: 报价信息包括：品种、品牌、仓库、计价公式（期货盘面价 + 基差）、数量、有效期、交货期、联系方式等。
    - 1.2.2: 卖方可以管理自己的报价，进行修改、撤回、查看状态等操作。

#### FR1.3: 买方在线点价
- **描述:** 买方用户（如合作厂商）可以根据平台上的报价进行点价操作。
- **用户场景:** 下游厂商看到合适的基差报价后，输入需要采购的数量，选择一个实时的期货价格进行“点价”，系统锁定该价格。
- **验收标准:**
    - 1.3.1: 买方可以浏览所有公开报价，并可通过品种、区域等条件筛选。
    - 1.3.2: 点价时，系统实时显示当前的期货价格，买方点击确认后，该价格被锁定为最终结算价的一部分。
    - 1.3.3: 系统需支持在期货开市时段内进行点价。

#### FR1.4: 自动化生成点价确认书
- **描述:** 点价成功后，系统自动生成具备法律效力的电子点价确认书。
- **用户场景:** 买卖双方在点价完成后，都能在自己的订单中心看到一份详细的电子确认书，无需再线下签署。
- **验收标准:**
    - 1.4.1: 确认书包含：双方信息、品种、数量、确定的期货价格、基差、最终结算价、交货信息等所有关键条款。
    - 1.4.2: 确认书支持下载和打印。
    - 1.4.3: (可选) 确认书关键信息可通过区块链进行存证，保证不可篡改。

### 3.2. P2: 智能库存风险管理与期现对冲工具

#### FR2.1: 库存与订单数据接入
- **描述:** 系统需支持接入企业的ERP系统，或通过模板导入采购/销售订单及库存数据。
- **验收标准:**
    - 2.1.1: 提供主流ERP系统的API对接方案（如金蝶、用友）。
    - 2.1.2: 提供标准的Excel/CSV模板，支持用户手动上传订单和库存数据。
    - 2.1.3: 数据同步频率可配置（实时、每日、每小时）。

#### FR2.2: 风险敞口计算与可视化
- **描述:** 系统基于整合的数据，利用风险模型（VaR、压力测试）自动计算企业当前库存的价格风险敞口。
- **验收标准:**
    - 2.2.1: 在风险看板中，以明确的金额（如VaR值）展示在特定置信度下，未来N天内库存可能的最大损失。
    - 2.2.2: 提供压力测试功能，模拟在极端市场行情（如价格暴跌10%）下，库存的亏损情况。
    - 2.2.3: 风险看板需以图表形式（如仪表盘、趋势图）直观展示风险敞口的变化。

#### FR2.3: 动态对冲策略引擎
- **描述:** 系统根据风险敞口、库存周期和期货合约流动性，自动计算并建议最优的期货对冲策略。
- **验收标准:**
    - 2.3.1: 策略建议需明确指出：应操作的期货品种、合约月份、买卖方向、头寸规模（手数）。
    - 2.3.2: 策略引擎需考虑对冲比率（β系数），并允许用户自定义调整。
    - 2.3.3: 当现有持仓的期货合约临近交割或流动性变差时，系统能发出移仓换月提醒。

#### FR2.4: 一键生成对冲指令
- **描述:** 用户在确认系统建议的对冲策略后，可一键生成交易指令并推送到期货交易系统。
- **验收标准:**
    - 2.4.1: 支持与主流期货公司的交易API进行对接。
    - 2.4.2: 生成的指令需经过用户二次确认（输入交易密码等）后方可发出。
    - 2.4.3: 用户可在平台内查看对冲指令的执行状态和持仓盈亏。

---

## 4. 非功能性需求

### 4.1. 性能需求
- **数据实时性:** 行情、基差等核心数据展示延迟不超过 `3` 秒。
- **响应时间:** 核心交易操作（如点价、报价）的服务器响应时间应在 `500ms` 以内。
- **并发处理:** 系统应能支持至少 `1000` 用户同时在线进行常规操作。

### 4.2. 安全需求
- **数据安全:** 用户核心业务数据（订单、库存、持仓）需加密存储，传输过程使用 `HTTPS` 加密。
- **访问控制:** 建立基于角色的访问控制（RBAC），不同用户角色拥有不同操作权限。
- **交易安全:** 核心交易环节需增加二次验证（如短信验证码、交易密码）。
- **合规性:** 涉及金融服务的功能需满足相关金融监管机构的合规要求。

### 4.3. 可用性需求
- **UI/UX:** 界面设计简洁、直观，符合大宗商品交易员的操作习惯。核心操作路径不超过 `3` 步。
- **可靠性:** 系统核心功能可用性需达到 `99.9%`。
- **兼容性:** Web端需兼容主流浏览器（Chrome, Firefox, Edge）的最新两个版本。

### 4.4. 扩展性需求
- **模块化设计:** 系统应采用微服务或模块化架构，便于未来新增功能模块（如物流、仓储服务对接）。
- **接口开放:** 核心功能（如数据接入、交易指令）应预留标准API接口，便于与其他系统集成。
