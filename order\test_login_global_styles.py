#!/usr/bin/env python3
"""
登录窗口全局样式测试脚本

测试登录窗口是否正确使用全局样式，包括刷新按钮等特殊组件
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer

# 设置日志
logging.basicConfig(level=logging.INFO)

def test_login_global_styles():
    """测试登录窗口全局样式"""
    try:
        # 创建应用
        app = QApplication(sys.argv)
        
        # 导入并应用全局样式
        from ui.styles.global_styles import GlobalStyleManager
        
        global_style_manager = GlobalStyleManager()
        global_styles = global_style_manager.get_complete_global_style()
        app.setStyleSheet(global_styles)
        
        print("✅ 全局样式已应用到整个应用程序")
        
        # 创建登录窗口
        from vnpy.event import EventEngine
        from ui.login.login_window import LoginWindow
        
        event_engine = EventEngine()
        login_window = LoginWindow(event_engine, None, None)
        
        # 显示登录窗口
        login_window.show_centered()
        
        print("=" * 60)
        print("登录窗口全局样式测试")
        print("=" * 60)
        print("测试要点:")
        print("1. 登录窗口背景颜色是否正确")
        print("2. 刷新验证码按钮样式是否更新")
        print("3. 发送验证码按钮样式是否统一")
        print("4. 登录按钮样式是否现代化")
        print("5. 输入框和标签样式是否一致")
        print("6. Tab切换样式是否正确")
        print()
        print("观察要点:")
        print("- 所有按钮使用统一的圆角设计")
        print("- 验证码刷新按钮有正确的悬停效果")
        print("- 背景使用渐变色而不是单色")
        print("- 所有组件颜色协调一致")
        print("- 字体和间距统一")
        print("=" * 60)
        
        # 延迟检查样式应用情况
        def check_styles():
            try:
                # 检查登录窗口的样式
                style_sheet = login_window.styleSheet()
                if style_sheet:
                    print("✅ 登录窗口已应用样式")
                    if "#refreshCaptchaButton" in style_sheet:
                        print("✅ 刷新按钮样式已包含")
                    else:
                        print("❌ 刷新按钮样式未找到")
                    
                    if "#sendCodeButton" in style_sheet:
                        print("✅ 发送验证码按钮样式已包含")
                    else:
                        print("❌ 发送验证码按钮样式未找到")
                        
                    if "background" in style_sheet:
                        print("✅ 背景样式已包含")
                    else:
                        print("❌ 背景样式未找到")
                else:
                    print("❌ 登录窗口未应用样式")
                    
            except Exception as e:
                print(f"❌ 样式检查失败：{str(e)}")
        
        # 延迟执行样式检查
        QTimer.singleShot(1000, check_styles)
        
        # 运行应用
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_global_styles()
