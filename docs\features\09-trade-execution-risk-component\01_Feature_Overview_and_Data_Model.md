
# 功能模块：交易执行风险组件

## 1. 功能模块简介 (Summary)

本功能模块旨在将**交易执行逻辑**与**合同条款**进行解耦。在旧流程中，合同可能直接或间接关联到特定的期货账户进行下单。新设计引入了一个独立的**交易执行风险组件**，由被点价方（Setter）集中管理交易指令在被审核通过后的处理方式。

该组件的核心目标是提供高度灵活的配置，允许被点价方根据业务需求，决定一笔交易是**自动进入真实市场**、**由系统模拟成交**，还是**转为线下处理并由人工反馈结果**。

- **核心参与方**:
  - **被点价方 (Setter)**: 配置并管理执行规则。
  - **点价方 (Pricer)**: 提交交易，并接收清晰、实时的状态反馈。

- **核心解决的问题**:
  - 移除合同中与具体期货账户的硬性绑定。
  - 统一管理不同场景下的交易执行逻辑。
  - 为模拟交易、半自动交易、全自动交易提供统一的配置入口。

---

## 2. 数据定义 (Data Definition)

**版本:** 1.0

### 2.1. `dianjia_risk_configs` - 风险执行配置表 (新增)

本表用于存储被点价方定义的所有交易执行规则。

| 字段名 | 类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT` | `PK, AI` | 唯一ID |
| `setter_id` | `BIGINT` | `NOT NULL, FK` | 规则所属的被点价方用户ID |
| `config_level` | `VARCHAR(20)` | `NOT NULL` | **配置层级** (`CONTRACT`, `PRICER`, `GLOBAL`) |
| `contract_id` | `BIGINT` | `NULL, FK` | 关联的合同ID (当 `config_level`='CONTRACT') |
| `pricer_id` | `BIGINT` | `NULL, FK` | 关联的点价方用户ID (当 `config_level`='PRICER') |
| `execution_mode` | `VARCHAR(20)` | `NOT NULL` | **执行模式** (`AUTOMATIC`, `MANUAL`, `SIMULATED`) |
| `priority` | `INT` | `NOT NULL, DEFAULT 0` | 优先级（数字越大越高），用于同层级规则排序 |
| `is_active` | `BOOLEAN` | `NOT NULL, DEFAULT TRUE` | 是否启用该规则 |
| `remarks` | `TEXT` | | 备注信息 |
| `created_at` | `TIMESTAMP` | | 创建时间 |
| `updated_at` | `TIMESTAMP` | | 更新时间 |



### 2.3. `trades` - 点价交易表 (状态机扩展)

为了支持新的执行模式，`status` 字段的状态机需要扩展。

| 字段名 | 类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `status` | `VARCHAR(50)` | `NOT NULL` | **核心状态字段** (见下方新版状态机) |
| `executed_by` | `VARCHAR(20)` | `NULL` | 记录本次成交的执行方式 (`AUTOMATIC`, `MANUAL`, `SIMULATED`) |
| `executor_id` | `BIGINT` | `NULL, FK` | 如果是人工反馈，记录操作员的用户ID |
| `feedback_notes` | `TEXT` | `NULL` | 人工反馈时填写的备注信息 |


#### 2.3.1. 新版交易生命周期 (状态机)

1.  **`PendingApproval` (待审核)**: 点价方提交，等待被点价方响应。
    -   *可执行操作: 拒绝, 接受*
2.  **`Rejected` (已拒绝)**: 被点价方拒绝，流程结束。
3.  **`Approved` (已接受)**: 被点价方接受。**这是新逻辑的入口**。系统将根据风险配置决定下一步状态。
    -   若模式为 `AUTOMATIC` -> `Executing`
    -   若模式为 `MANUAL` -> `PendingManualFill`
    -   若模式为 `SIMULATED` -> `Filled` (几乎瞬间完成)
4.  **`PendingManualFill` (等待人工回报)**: (新增) 交易已批准，等待操作员线下执行并回报结果。
    -   *可执行操作: 回报成交, 回报部分成交, 回报失败*
5.  **`Executing` (执行中)**: 已提交至自动执行系统（本地下单端），等待回报。
6.  **`Filled` (已成交)**: 流程成功结束。
7.  **`PartiallyFilled` (部分成交)**: 中间状态或最终状态。
8.  **`ExchangeRejected` (执行失败)**: 自动执行系统拒单，或人工回报执行失败。
9.  **`Cancelled` (已撤销)**: 在最终状态前由用户或系统发起了撤销。

```mermaid
graph TD
    A[PendingApproval] -->|拒绝| B(Rejected)
    A -->|接受| C(Approved)
    C -->|执行模式: SIMULATED| F(Filled)
    C -->|执行模式: MANUAL| D(PendingManualFill)
    C -->|执行模式: AUTOMATIC| E(Executing)
    D -->|回报成交| F
    D -->|回报部分成交| G(PartiallyFilled)
    D -->|回报失败| H(ExchangeRejected)
    E --> I{执行结果}
    I -->|完全成交| F
    I -->|部分成交| G
    I -->|执行失败| H
    I -->|撤销| J(Cancelled)
```
