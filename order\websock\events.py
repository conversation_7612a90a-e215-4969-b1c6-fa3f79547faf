"""
WebSocket事件定义

定义WebSocket通信中使用的事件常量
重构版本：移除订阅相关事件，添加心跳相关事件
"""

# WebSocket连接事件
EVENT_WS_CONNECTING = "websocket.connecting"
EVENT_WS_CONNECTED = "websocket.connected"
EVENT_WS_AUTHENTICATED = "websocket.authenticated"
EVENT_WS_DISCONNECTED = "websocket.disconnected"
EVENT_WS_ERROR = "websocket.error"
EVENT_WS_RECONNECTING = "websocket.reconnecting"
EVENT_WS_RECONNECT_SUCCESS = "websocket.reconnect.success"
EVENT_WS_RECONNECT_FAILED = "websocket.reconnect.failed"

# WebSocket消息事件
EVENT_WS_MESSAGE_RECEIVED = "websocket.message.received"
EVENT_WS_MESSAGE_SENT = "websocket.message.sent"

# WebSocket认证事件
EVENT_WS_AUTH_REQUIRED = "websocket.auth.required"
EVENT_WS_AUTH_SUCCESS = "websocket.auth.success"
EVENT_WS_AUTH_FAILED = "websocket.auth.failed"

# WebSocket心跳事件 - 新增增强的心跳机制
EVENT_WS_PING_SENT = "websocket.ping.sent"           # ping消息发送
EVENT_WS_PONG_RECEIVED = "websocket.pong.received"   # pong消息接收
EVENT_WS_HEARTBEAT_TIMEOUT = "websocket.heartbeat.timeout"  # 心跳超时
EVENT_WS_NETWORK_LATENCY = "websocket.network.latency"     # 网络延迟计算
EVENT_WS_HEARTBEAT = "websocket.heartbeat"           # 通用心跳事件（保持兼容性）

# WebSocket业务消息事件 - 通过EventEngine统一分发
EVENT_WS_ORDER_UPDATE = "websocket.order_update"     # 订单更新
EVENT_WS_MARKET_DATA = "websocket.market_data"       # 市场数据
EVENT_WS_NOTIFICATION = "websocket.notification"     # 通知消息
EVENT_WS_AUTH_RESPONSE = "websocket.auth_response"   # 认证响应
EVENT_WS_REGISTER_RESPONSE = "websocket.register_response"  # 注册响应

# WebSocket状态事件
EVENT_WS_STATE_CHANGED = "websocket.state.changed"

# 通用WebSocket消息事件前缀
WEBSOCKET_EVENT_PREFIX = "websocket."

# 事件分类映射
EVENT_CATEGORIES = {
    "connection": [
        EVENT_WS_CONNECTING,
        EVENT_WS_CONNECTED,
        EVENT_WS_AUTHENTICATED,
        EVENT_WS_DISCONNECTED,
        EVENT_WS_RECONNECTING,
        EVENT_WS_RECONNECT_SUCCESS,
        EVENT_WS_RECONNECT_FAILED,
    ],
    "message": [
        EVENT_WS_MESSAGE_RECEIVED,
        EVENT_WS_MESSAGE_SENT,
    ],
    "auth": [
        EVENT_WS_AUTH_REQUIRED,
        EVENT_WS_AUTH_SUCCESS,
        EVENT_WS_AUTH_FAILED,
        EVENT_WS_AUTH_RESPONSE,
    ],
    "heartbeat": [
        EVENT_WS_PING_SENT,
        EVENT_WS_PONG_RECEIVED,
        EVENT_WS_HEARTBEAT_TIMEOUT,
        EVENT_WS_NETWORK_LATENCY,
        EVENT_WS_HEARTBEAT,
    ],
    "business": [
        EVENT_WS_ORDER_UPDATE,
        EVENT_WS_MARKET_DATA,
        EVENT_WS_NOTIFICATION,
        EVENT_WS_REGISTER_RESPONSE,
    ],
    "status": [
        EVENT_WS_STATE_CHANGED,
        EVENT_WS_ERROR,
    ]
}

def get_events_by_category(category: str) -> list:
    """根据分类获取事件列表"""
    return EVENT_CATEGORIES.get(category, [])

def is_websocket_event(event_name: str) -> bool:
    """检查是否为WebSocket事件"""
    return event_name.startswith(WEBSOCKET_EVENT_PREFIX)

def get_event_category(event_name: str) -> str:
    """获取事件分类"""
    for category, events in EVENT_CATEGORIES.items():
        if event_name in events:
            return category
    return "unknown"