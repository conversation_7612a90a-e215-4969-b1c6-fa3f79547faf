# 子功能：实时交互与连接管理

## 1. 功能设计 (Functional Design)

### 1.1 连接建立与认证

1.  客户端向服务器 `ws(s)://<domain>/api/v1/trade` 发起 WebSocket 连接请求。
2.  连接建立后，客户端立即发送**认证请求**消息，携带用户的认证 `token`。
3.  服务器验证 `token`。
    *   成功：返回**认证响应** `{"success": true}`。并将连接信息注册到 Redis。
    *   失败：返回**认证响应** `{"success": false}`，并关闭连接。

### 1.2 使用 Redis 管理连接

为了支持分布式部署，所有 WebSocket 连接信息都必须在 Redis 中进行管理。

1.  **连接注册**：
    *   当一个客户端认证成功后，服务器生成一个全网唯一的 `connection_id`。
    *   在 Redis 中存储以下映射关系：
        *   用户ID -> 连接ID: `HSET user_connections <user_id> <connection_id>`
        *   连接ID -> 节点信息: `SET connection_info:<connection_id> '{"node_id": "server_node_abc"}' EX 300`
        *   `node_id` 是当前处理该连接的服务器实例ID。该键需要设置一个合理的过期时间。

2.  **消息路由**：
    *   当任何后端服务（如交易核心）需要向用户推送消息时，它首先从 Redis 的 `user_connections` 中根据 `user_id` 查到 `connection_id`。
    *   然后根据 `connection_id` 从 `connection_info:<connection_id>` 中查到该连接所在的 `node_id`。
    *   服务将消息投递到目标 `node_id` 的消息队列中（例如 Redis Pub/Sub），由该节点将消息通过 WebSocket 发送给客户端。

### 1.3 心跳与断线重连

1.  **心跳机制**：
    *   客户端每隔 30 秒发送一次**心跳请求 (Ping)**。
    *   服务器收到 Ping 后，立即回复**心跳响应 (Pong)**，并刷新 Redis 中 `connection_info:<connection_id>` 的过期时间。
    *   如果服务器在 90 秒内未收到任何客户端消息（包括Ping），则认为连接已死，主动关闭连接并清理 Redis 中的相关记录。

2.  **断线重连**：
    *   客户端应实现断线自动重连逻辑。
    *   重连成功后，需要重新执行“连接建立与认证”流程。
    *   客户端应在重连后主动向服务器请求一次全量的订单状态，以保证数据一致性。

## 2. 接口定义 (API Definition)

| 功能描述 | HTTP 方法 | 路径 (Endpoint) |
|---|---|---|
| 实时交易与状态同步 | WebSocket | `ws(s)://<your-server-address>/api/v1/trade` |

## 3. 相关页面 (Related Pages)

- `src/views/trade/TradePanel.vue`
- `src/components/orders/ActiveOrdersList.vue`
- `src/components/positions/PositionsList.vue`
- `src/views/history/TradeHistory.vue`

## 4. 测试用例 (Test Cases)

| 用例ID | 场景描述 | 测试步骤 | 预期结果 |
|---|---|---|---|
| TC-001 | 客户端连接成功并发送有效token | 1. 客户端发起连接 2. 发送有效token | 服务器返回认证成功，连接保持 |
| TC-002 | 客户端发送无效token | 1. 客户端发起连接 2. 发送无效token | 服务器返回认证失败，连接关闭 |
| TC-003 | 发送结构正确的下单请求 | 1. 认证成功 2. 发送下单消息 | 收到 `order_update` 消息，状态为 `SUBMITTED` |
| TC-004 | 发送重复 `client_order_id` 的下单请求 | 1. 发送一笔订单 2. 再次发送相同订单 | 服务器应拒绝第二个请求，并可能返回错误提示 |
| TC-005 | 订单在交易所成交 | 1. 下单成功 2. 模拟交易所成交回报 | 客户端能接收到 `order_update` 消息，最终状态为 `FILLED` |
| TC-006 | 客户端断线后自动重连 | 1. 正常连接 2. 手动断开网络 3. 恢复网络 | 客户端能重新认证并恢复会话 |
| TC-007 | 服务器长时间未收到心跳 | 1. 客户端连接后不发送任何消息 | 90秒后服务器主动断开连接 |
| TC-008 | 分布式环境下消息路由 | 1. 用户连接到节点A 2. 交易核心在节点B触发状态更新 | 消息能通过Redis正确路由到节点A并下发给用户 |

## 5. 注意事项 (Notes/Caveats)

- **幂等性**: `client_order_id` 是保证下单操作幂等性的关键，客户端必须为每个新订单生成唯一的ID。
- **安全性**: 生产环境必须使用 `wss://` (WebSocket Secure) 协议，保证数据传输加密。
- **状态一致性**: 客户端在断线重连后，应有同步最新订单状态的机制，防止因网络问题导致信息不一致。
