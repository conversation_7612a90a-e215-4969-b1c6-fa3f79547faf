
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base

# --- Database Configuration ---
# IMPORTANT: Replace user and password with your actual database credentials.
DATABASE_URL = "mysql+pymysql://root:123456@localhost:3306/dianjia"

# --- Engine & Session ---
# The engine is the starting point for any SQLAlchemy application.
# It's the low-level object that connects to the database.
engine = create_engine(
    DATABASE_URL,
    # `echo=True` will log all SQL statements, useful for debugging
    echo=False
)

# The SessionLocal class is a factory for creating new Session objects.
# A Session is the primary interface for all database operations.
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# --- Base for Declarative Models ---
# We will inherit from this class to create each of the ORM models.
Base = declarative_base()

def get_db_session():
    """
    Dependency function to get a database session.
    Ensures the session is properly closed after use.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# You can define your database models here or in a separate `models.py` file.
# For example:
#
# from sqlalchemy import Column, String, Integer, Float, Date
#
# class Instrument(Base):
#     __tablename__ = "instruments"
#
#     instrument_id = Column(String, primary_key=True, index=True)
#     exchange_id = Column(String, index=True)
#     instrument_name = Column(String)
#     product_id = Column(String, index=True)
#     product_class = Column(String)
#     delivery_year = Column(Integer)
#     delivery_month = Column(Integer)
#     create_date = Column(Date)
#     start_date = Column(Date)
#     end_date = Column(Date)
#     multiplier = Column(Integer)
#     price_tick = Column(Float)


# To create all tables in the database, you would run:
# Base.metadata.create_all(bind=engine)
