<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" :rules="searchRule" @keyup.enter="onSubmit">
        <el-form-item label="合约ID" prop="instrument_id">
          <el-input v-model="searchInfo.instrument_id" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="合约名称" prop="instrument_name">
          <el-input v-model="searchInfo.instrument_name" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="交易所" prop="exchange_id">
          <el-select v-model="searchInfo.exchange_id" placeholder="请选择交易所" clearable>
            <el-option label="中国金融期货交易所" value="CFFEX" />
            <el-option label="上海期货交易所" value="SHFE" />
            <el-option label="大连商品交易所" value="DCE" />
            <el-option label="郑州商品交易所" value="CZCE" />
            <el-option label="上海国际能源交易中心" value="INE" />
            <el-option label="广州期货交易所" value="GFEX" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品ID" prop="product_id">
          <el-input v-model="searchInfo.product_id" placeholder="搜索条件" />
        </el-form-item>
        <el-form-item label="商品类别" prop="product_class">
          <el-select v-model="searchInfo.product_class" placeholder="请选择商品类别" clearable>
            <el-option label="期货" value="1" />
            <el-option label="期权" value="2" />
            <el-option label="组合" value="3" />
            <el-option label="即期" value="4" />
            <el-option label="期转现" value="5" />
            <el-option label="现货期权" value="6" />
            <el-option label="TAS合约" value="7" />
            <el-option label="金属指数" value="8" />
            <el-option label="期货期权" value="9" />
          </el-select>
        </el-form-item>
        <el-form-item label="合约状态" prop="inst_life_phase">
          <el-select v-model="searchInfo.inst_life_phase" placeholder="请选择合约状态" clearable>
            <el-option label="未上市" value="0" />
            <el-option label="上市" value="1" />
            <el-option label="停牌" value="2" />
            <el-option label="到期" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="primary" icon="plus" @click="openDialog">新增</el-button>
        <el-button icon="delete" style="margin-left: 10px;" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="ID" prop="id" width="90" />
        <el-table-column align="left" label="合约ID" prop="instrument_id" width="120" />
        <el-table-column align="left" label="合约名称" prop="instrument_name" width="120" />
        <el-table-column align="left" label="交易所" prop="exchange_id" width="120" />
        <el-table-column align="left" label="产品名称" prop="product_name" width="120" />
        <el-table-column align="left" label="商品类别" prop="product_class_name" width="100" />
        <el-table-column align="left" label="合约乘数" prop="volume_multiple" width="100" />
        <el-table-column align="left" label="最小变动价位" prop="price_tick" width="120" />
        <el-table-column align="left" label="合约状态" prop="inst_life_phase_name" width="100" />
        <el-table-column align="left" label="创建时间" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="left" label="操作" fixed="right" min-width="240">
          <template #default="scope">
            <el-button type="primary" link class="table-button" @click="getDetails(scope.row)">
              <el-icon style="margin-right: 5px"><InfoFilled /></el-icon>查看详情
            </el-button>
            <el-button type="primary" link icon="edit" class="table-button" @click="updateInstrumentFunc(scope.row)">变更</el-button>
            <el-button type="primary" link icon="delete" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-drawer
      v-model="dialogFormVisible"
      :show-close="false"
      :before-close="closeDialog"
      size="800px"
    >
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg">{{ type === 'create' ? '新增' : '修改' }}期货合约</span>
          <div>
            <el-button @click="closeDialog">取消</el-button>
            <el-button type="primary" @click="enterDialog">确定</el-button>
          </div>
        </div>
      </template>

      <el-form
        ref="elFormRef"
        :model="formData"
        label-position="top"
        :rules="rule"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合约ID:" prop="instrument_id">
              <el-input v-model="formData.instrument_id" clearable placeholder="请输入合约ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合约名称:" prop="instrument_name">
              <el-input v-model="formData.instrument_name" clearable placeholder="请输入合约名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="交易所:" prop="exchange_id">
              <el-select v-model="formData.exchange_id" placeholder="请选择交易所" clearable style="width: 100%">
                <el-option label="中国金融期货交易所" value="CFFEX" />
                <el-option label="上海期货交易所" value="SHFE" />
                <el-option label="大连商品交易所" value="DCE" />
                <el-option label="郑州商品交易所" value="CZCE" />
                <el-option label="上海国际能源交易中心" value="INE" />
                <el-option label="广州期货交易所" value="GFEX" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品类别:" prop="product_class">
              <el-select v-model="formData.product_class" placeholder="请选择产品类别" clearable style="width: 100%">
                <el-option label="期货" value="1" />
                <el-option label="期权" value="2" />
                <el-option label="组合" value="3" />
                <el-option label="即期" value="4" />
                <el-option label="期转现" value="5" />
                <el-option label="现货期权" value="6" />
                <el-option label="TAS合约" value="7" />
                <el-option label="金属指数" value="8" />
                <el-option label="期货期权" value="9" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="产品ID:" prop="product_id">
              <el-input v-model="formData.product_id" clearable placeholder="请输入产品ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="产品名称:" prop="product_name">
              <el-input v-model="formData.product_name" clearable placeholder="请输入产品名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合约乘数:" prop="volume_multiple">
              <el-input-number v-model="formData.volume_multiple" :min="1" placeholder="请输入合约乘数" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最小变动价位:" prop="price_tick">
              <el-input-number v-model="formData.price_tick" :min="0" :precision="4" :step="0.0001" placeholder="请输入最小变动价位" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="做多保证金率:" prop="long_margin_ratio">
              <el-input-number v-model="formData.long_margin_ratio" :min="0" :max="1" :precision="6" :step="0.01" placeholder="请输入做多保证金率" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="做空保证金率:" prop="short_margin_ratio">
              <el-input-number v-model="formData.short_margin_ratio" :min="0" :max="1" :precision="6" :step="0.01" placeholder="请输入做空保证金率" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="开仓手续费率:" prop="open_ratio_by_money">
              <el-input-number v-model="formData.open_ratio_by_money" :min="0" :precision="8" :step="0.0001" placeholder="请输入开仓手续费率" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="平仓手续费率:" prop="close_ratio_by_money">
              <el-input-number v-model="formData.close_ratio_by_money" :min="0" :precision="8" :step="0.0001" placeholder="请输入平仓手续费率" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="平今手续费率:" prop="close_today_ratio">
              <el-input-number v-model="formData.close_today_ratio" :min="0" :precision="8" :step="0.0001" placeholder="请输入平今手续费率" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="交割年份:" prop="delivery_year">
              <el-input-number v-model="formData.delivery_year" :min="2020" :max="2050" placeholder="请输入交割年份" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交割月份:" prop="delivery_month">
              <el-input-number v-model="formData.delivery_month" :min="1" :max="12" placeholder="请输入交割月份" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="上市日期:" prop="open_date">
              <el-date-picker
                v-model="formData.open_date"
                type="date"
                placeholder="请选择上市日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最后交易日:" prop="expire_date">
              <el-date-picker
                v-model="formData.expire_date"
                type="date"
                placeholder="请选择最后交易日"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="合约状态:" prop="inst_life_phase">
          <el-select v-model="formData.inst_life_phase" placeholder="请选择合约状态" clearable style="width: 100%">
            <el-option label="未上市" value="0" />
            <el-option label="上市" value="1" />
            <el-option label="停牌" value="2" />
            <el-option label="到期" value="3" />
          </el-select>
        </el-form-item>
      </el-form>
    </el-drawer>

    <el-drawer destroy-on-close size="800px" v-model="detailShow" :show-close="true" :before-close="closeDetailShow">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="ID">
          {{ formData.id }}
        </el-descriptions-item>
        <el-descriptions-item label="合约ID">
          {{ formData.instrument_id }}
        </el-descriptions-item>
        <el-descriptions-item label="合约名称">
          {{ formData.instrument_name }}
        </el-descriptions-item>
        <el-descriptions-item label="商品名称">
          {{ formData.commodity_name }}
        </el-descriptions-item>
        <el-descriptions-item label="商品类别">
          {{ formData.product_class_name }}
        </el-descriptions-item>
        <el-descriptions-item label="合约乘数">
          {{ formData.volume_multiple }}
        </el-descriptions-item>
        <el-descriptions-item label="最小变动价位">
          {{ formData.price_tick }}
        </el-descriptions-item>
        <el-descriptions-item label="做多保证金率">
          {{ formData.long_margin_ratio }}
        </el-descriptions-item>
        <el-descriptions-item label="做空保证金率">
          {{ formData.short_margin_ratio }}
        </el-descriptions-item>
        <el-descriptions-item label="开仓手续费率">
          {{ formData.open_ratio_by_money }}
        </el-descriptions-item>
        <el-descriptions-item label="平仓手续费率">
          {{ formData.close_ratio_by_money }}
        </el-descriptions-item>
        <el-descriptions-item label="平今手续费率">
          {{ formData.close_today_ratio }}
        </el-descriptions-item>
        <el-descriptions-item label="交割年份">
          {{ formData.delivery_year }}
        </el-descriptions-item>
        <el-descriptions-item label="交割月份">
          {{ formData.delivery_month }}
        </el-descriptions-item>
        <el-descriptions-item label="上市日期">
          {{ formatDate(formData.open_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="最后交易日">
          {{ formatDate(formData.expire_date) }}
        </el-descriptions-item>
        <el-descriptions-item label="合约状态">
          {{ formData.inst_life_phase_name }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDate(formData.created_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatDate(formData.updated_at) }}
        </el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script setup>
import {
  createInstrument,
  deleteInstrument,
  deleteInstrumentByIds,
  updateInstrument,
  findInstrument,
  getInstrumentList
} from '@/api/instrument'

import { getAllCommodityList } from '@/api/commodity'

// 全量引入格式化工具 请按需保留
import { getDictFunc, formatDate, formatBoolean, filterDict, ReturnArrImg, onDownloadFile } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive } from 'vue'

defineOptions({
  name: 'Instrument'
})

// 自动化生成的字典（可能为空）以及初始化时的字典，请手动补充
const formData = ref({
  instrument_id: '',
  instrument_name: '',
  commodity_id: undefined,
  product_class: '1',
  volume_multiple: 1,
  price_tick: 0.01,
  long_margin_ratio: null,
  short_margin_ratio: null,
  open_ratio_by_money: null,
  close_ratio_by_money: null,
  close_today_ratio: null,
  delivery_year: null,
  delivery_month: null,
  open_date: null,
  expire_date: null,
  inst_life_phase: '0',
})

// 验证规则
const rule = reactive({
  instrument_id: [{
    required: true,
    message: '',
    trigger: ['input', 'blur'],
  }],
  instrument_name: [{
    required: true,
    message: '',
    trigger: ['input', 'blur'],
  }],
  commodity_id: [{
    required: true,
    message: '',
    trigger: ['change'],
  }],
  product_class: [{
    required: true,
    message: '',
    trigger: ['change'],
  }],
  volume_multiple: [{
    required: true,
    message: '',
    trigger: ['blur'],
  }],
  price_tick: [{
    required: true,
    message: '',
    trigger: ['blur'],
  }],
  inst_life_phase: [{
    required: true,
    message: '',
    trigger: ['change'],
  }],
})

const searchRule = reactive({
  createdAt: [
    { validator: (rule, value, callback) => {
      if (searchInfo.value.startCreatedAt && !searchInfo.value.endCreatedAt) {
        callback(new Error('请填写结束日期'))
      } else if (!searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt) {
        callback(new Error('请填写开始日期'))
      } else if (searchInfo.value.startCreatedAt && searchInfo.value.endCreatedAt && (searchInfo.value.startCreatedAt.getTime() === searchInfo.value.endCreatedAt.getTime() || searchInfo.value.startCreatedAt.getTime() > searchInfo.value.endCreatedAt.getTime())) {
        callback(new Error('开始日期应当早于结束日期'))
      } else {
        callback()
      }
    }, trigger: 'change' }
  ],
})

const elFormRef = ref()
const elSearchFormRef = ref()

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})

// 重置
const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    pageSize.value = 10
    getTableData()
  })
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

// 修改页面容量
const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async() => {
  const table = await getInstrumentList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

getTableData()

// ============== 表格控制部分结束 ===============

// 商品选项
const commodityOptions = ref([])

// 获取需要的字典 可能为空 按需保留
const setOptions = async () =>{
  const res = await getAllCommodityList()
  if (res.code === 0) {
    commodityOptions.value = res.data
  }
}

// 获取需要的字典 可能为空 按需保留
setOptions()


// 多选数据
const multipleSelection = ref([])
// 多选
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteInstrumentFunc(row)
  })
}

// 多选删除
const onDelete = async() => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async() => {
    const IDs = []
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    multipleSelection.value &&
      multipleSelection.value.map(item => {
        IDs.push(item.ID)
      })
    const res = await deleteInstrumentByIds({ IDs })
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === IDs.length && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  })
}

// 行为控制标记（弹窗内部需要增还是改）
const type = ref('')

// 更新行
const updateInstrumentFunc = async(row) => {
  const res = await findInstrument({ ID: row.ID })
  type.value = 'update'
  if (res.code === 0) {
    formData.value = res.data
    dialogFormVisible.value = true
  }
}


// 删除行
const deleteInstrumentFunc = async (row) => {
  const res = await deleteInstrument({ ID: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

// 弹窗控制标记
const dialogFormVisible = ref(false)

// 打开弹窗
const openDialog = () => {
  type.value = 'create'
  dialogFormVisible.value = true
}

// 关闭弹窗
const closeDialog = () => {
  dialogFormVisible.value = false
  formData.value = {
    instrument_id: '',
    instrument_name: '',
    commodity_id: undefined,
    product_class: '1',
    volume_multiple: 1,
    price_tick: 0.01,
    long_margin_ratio: null,
    short_margin_ratio: null,
    open_ratio_by_money: null,
    close_ratio_by_money: null,
    close_today_ratio: null,
    delivery_year: null,
    delivery_month: null,
    open_date: null,
    expire_date: null,
    inst_life_phase: '0',
  }
}
// 弹窗确定
const enterDialog = async () => {
  elFormRef.value?.validate( async (valid) => {
    if (!valid) return
    let res
    switch (type.value) {
      case 'create':
        res = await createInstrument(formData.value)
        break
      case 'update':
        res = await updateInstrument(formData.value)
        break
      default:
        res = await createInstrument(formData.value)
        break
    }
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '创建/更改成功'
      })
      closeDialog()
      getTableData()
    }
  })
}


const detailShow = ref(false)
const closeDetailShow = () => {
  detailShow.value = false
  formData.value = {
    instrument_id: '',
    instrument_name: '',
    commodity_id: undefined,
    product_class: '1',
    volume_multiple: 1,
    price_tick: 0.01,
    long_margin_ratio: null,
    short_margin_ratio: null,
    open_ratio_by_money: null,
    close_ratio_by_money: null,
    close_today_ratio: null,
    delivery_year: null,
    delivery_month: null,
    open_date: null,
    expire_date: null,
    inst_life_phase: '0',
  }
}

const getDetails = async (row) => {
  // 打开详情弹窗
  const res = await findInstrument({ ID: row.ID })
  if (res.code === 0) {
    formData.value = res.data
    detailShow.value = true
  }
}
</script>

<style>
</style>