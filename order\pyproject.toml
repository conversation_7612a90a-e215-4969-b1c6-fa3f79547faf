[project]
name = "order"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "cryptography>=45.0.5",
    "httpx>=0.28.1",
    "pyyaml>=6.0.2",
    "ta-lib==0.6.4",
    "vnpy>=4.1.0",
    "vnpy-ctp>=*******",
    "websocket-client>=1.6.0",
    "websockets>=15.0.1",
]

[tool.uv]
# 默认索引源
index-url = "https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple"

# 额外索引源
extra-index-url = [
    "https://pypi.vnpy.com/simple/"
]

# 定义命名索引
[[tool.uv.index]]
name = "vnpy"
url = "https://pypi.vnpy.com/simple/"

[tool.uv.sources]
# 指定ta-lib从vnpy源下载
ta-lib = { index = "vnpy" }
