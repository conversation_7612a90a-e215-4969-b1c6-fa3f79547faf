/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/workspace/index" |
       "/pages/index/index" |
       "/pages/instrument" |
       "/pages/market-demo" |
       "/pages/about/about" |
       "/pages/about/alova" |
       "/pages/about/vue-query" |
       "/pages/contract/cancel-records" |
       "/pages/contract/detail" |
       "/pages/contract/form" |
       "/pages/contract/list" |
       "/pages/contract/pricer-list" |
       "/pages/contract/setter-list" |
       "/pages/login/index" |
       "/pages/quotes/detail" |
       "/pages/quotes/edit" |
       "/pages/quotes/marketplace" |
       "/pages/quotes/my-list" |
       "/pages/test/test-user-selector" |
       "/pages/trade/CombinationSelector" |
       "/pages/trade/execute" |
       "/pages/trade/pricer-management" |
       "/pages/trade/setter-management" |
       "/pages/trade/TradeOperationForm" |
       "/pages-sub/demo/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/index/index" | "/pages/about/about"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
