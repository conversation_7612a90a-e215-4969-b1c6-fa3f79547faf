# Order客户端认证配置文件

auth:
  # 服务器配置
  server:
    base_url: "http://localhost:8888"
    timeout: 30
    retry_count: 3
    retry_delay: 1
    
  # Token配置
  token:
    storage_file: "user_token.json"
    encryption_enabled: true
    auto_refresh: true
    refresh_threshold: 300  # 5分钟
    
  # 短信验证码配置
  sms:
    countdown_seconds: 60
    max_retry_count: 3
    
  # 安全配置
  security:
    password_min_length: 6
    max_login_attempts: 5
    lockout_duration: 300  # 5分钟
    
  # 验证配置
  validation:
    phone_pattern: "^1[3-9]\\d{9}$"
    username_min_length: 3
    username_max_length: 20
    sms_code_length: 6
